from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel
import asyncio
import json
import sys
from datetime import datetime
from functools import partial
#特殊函数
from itertools import product
import time
from PyQt6.QtCore import Qt, QRect
import aiohttp as aiohttp
# import cv2
import re

from PyQt6.QtNetwork import QNetworkAccessManager, QNetworkRequest, QNetworkReply
from lxml import etree
from urllib import request
from urllib.request import urlopen
import requests
from urllib import parse
from PyQt6.QtMultimedia import *
# from PIL import Image
from PyQt6.QtWidgets import QApplication, QWidget, QVBoxLayout, QTextEdit, QLineEdit, QPushButton, QTableWidgetItem, \
    QListWidgetItem, QHBoxLayout
from PyQt6.QtWidgets import QMainWindow, QApplication, QFrame, QFileDialog, QSlider, QSizePolicy, QGraphicsBlurEffect
from PyQt6.QtGui import QMouseEvent, QIcon, QPalette, QFont
from zjph import Ui_MainWindow
from PyQt6.QtCore import QTimer, QEvent, QDateTime, QUrl
from PyQt6.QtGui import QPixmap
from PyQt6.QtCore import QTimer, QEvent
from PyQt6.QtCore import Qt
from PyQt6.QtWidgets import QApplication, QLabel, QWidget, QVBoxLayout,QScrollArea
import os
from PyQt6.QtGui import QPainter, QColor, QPen
from PyQt6 import QtGui, QtCore
import os
import glob
from PyQt6.QtWidgets import QGraphicsDropShadowEffect
# from PyQt6.QtWebEngineCore import QWebEnginePage

class ZJPHWindow(Ui_MainWindow,QMainWindow):
    def __init__(self):
        super(Ui_MainWindow, self).__init__()
        self.setupUi(self)
        self.resize(1500, 700)

        self.setWindowTitle("资金流向排行图")
        # 创建一个布局
        layout = QVBoxLayout()

        # 添加一个标签
        label = QLabel("这是新窗口！", self)
        layout.addWidget(label)

        # 设置布局
        self.setLayout(layout)