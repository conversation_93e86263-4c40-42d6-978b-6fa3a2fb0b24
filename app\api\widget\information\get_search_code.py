import json

import requests


class get_search_c:
    def __init__(self):
        self.keyword=""
        self.name_list=[]

    def return_code_l(self,keyword):
        self.keyword = keyword
        self.name_list = []
        self.base_url=f"https://search-codetable.eastmoney.com/codetable/search/web?client=web&clientType=webSuggest&clientVersion=lastest&cb=jQuery35108576508717970958_1743838794116&keyword={self.keyword}&pageIndex=1&pageSize=10&securityFilter=&_=1743838794166"
        data = requests.get(self.base_url, verify=False).text
        data = json.loads(data.strip("jQuery35108576508717970958_1743838794116(").replace(")", ""))
        # print(data["result"][0])
        search_r = data["result"]
        search_result_list = []
        if len(search_r) > 0:
            for i in search_r:
                search_result_list.append(f"{i["securityTypeName"]}     {i["code"]}    {i["shortName"]}    {i["pinyin"]}")
                self.name_list.append(i["shortName"])
        else:
            print(f"查看全部 {keyword} 搜索结果")
        print(search_result_list)
        return search_result_list,self.name_list
get_search_c=get_search_c()
