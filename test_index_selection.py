"""
测试指数选择功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication, QMainWindow
from main_w import Ui_MainWindow
from app.modules.FundHome2.FundHome2 import FundHome2

def test_index_selection():
    """测试指数选择功能"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    ui = Ui_MainWindow()
    ui.setupUi(main_window)
    
    # 创建 FundHome2 实例
    fund_home2 = FundHome2(ui)
    
    # 显示主窗口
    main_window.show()
    
    print("测试指数选择功能:")
    print("1. 应该显示8个指数标签，默认选中第一个")
    print("2. 点击不同标签应该切换选中状态")
    print("3. 选中的标签应该使用 zs_selected_yes 样式")
    print("4. 未选中的标签应该使用 zs_selected_no 样式")
    print("5. 点击标签应该输出对应的指数代码")
    
    print("\n指数名称和代码对应关系:")
    for i, (name, code) in enumerate(zip(fund_home2.index_names, fund_home2.index_codes)):
        print(f"{i+1}. {name}: {code}")
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    test_index_selection()
