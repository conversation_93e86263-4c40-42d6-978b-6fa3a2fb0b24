
from PyQt6.QtNetwork import *
from PyQt6.QtMultimedia import *
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *
# from PyQt6.QtWebEngineCore import QWebEnginePage
from PyQt6.QtCore import QThread, pyqtSignal
# from main_w import Ui_MainWindow
# class hot_search_UI(Ui_MainWindow,QMainWindow):
#     def __init__(self, parent=None):
#         super().__init__(parent)
#
#         pass

def init_hot_widget_status(self):
    """初始化 hot_widget_status 状态"""
    self.hot_widget_status = False
def toggle_lz_hot_widget(self,event):
    if self.hot_widget_status:
        self.hot_widget_status =False
        self.lz_widget.show()
        self.hot_widget.hide()
    else:
        self.hot_widget.show()
        self.lz_widget.hide()
        self.hot_widget_status = True