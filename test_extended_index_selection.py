"""
测试扩展的指数选择功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication, QMainWindow
from main_w import Ui_MainWindow
from app.modules.FundHome2.FundHome2 import FundHome2

def test_extended_index_selection():
    """测试扩展的指数选择功能"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    ui = Ui_MainWindow()
    ui.setupUi(main_window)
    
    # 创建 FundHome2 实例
    fund_home2 = FundHome2(ui)
    
    # 显示主窗口
    main_window.show()
    
    print("测试扩展的指数选择功能:")
    print("=" * 50)
    
    print("\n1. 类型标签功能:")
    print("- 5个类型标签：zs_top_type_1 到 zs_top_type_5")
    print("- 默认选中沪深类型")
    print("- 点击不同类型标签应该切换选中状态")
    print("- 选中的类型标签应该使用橙色背景样式")
    print("- 未选中的类型标签应该使用灰色文字样式")
    
    print("\n2. 指数标签功能:")
    print("- 根据选中的类型显示对应数量的指数标签")
    print("- 多余的标签会被隐藏")
    print("- 点击指数标签应该输出对应的指数代码")
    
    print("\n3. 各类型指数数据:")
    print("沪深指数 (8个):")
    for i, (name, code) in enumerate(zip(fund_home2.hs_name, fund_home2.hs_code)):
        print(f"  {i+1}. {name}: {code}")
    
    print("\n港股指数 (4个):")
    for i, (name, code) in enumerate(zip(fund_home2.gg_name, fund_home2.gg_code)):
        print(f"  {i+1}. {name}: {code}")
    
    print("\n美股指数 (3个):")
    for i, (name, code) in enumerate(zip(fund_home2.mg_name, fund_home2.mg_code)):
        print(f"  {i+1}. {name}: {code}")
    
    print("\n英股指数 (5个):")
    for i, (name, code) in enumerate(zip(fund_home2.yg_name, fund_home2.yg_code)):
        print(f"  {i+1}. {name}: {code}")
    
    print("\n亚洲股指数 (5个):")
    for i, (name, code) in enumerate(zip(fund_home2.yz_name, fund_home2.yz_code)):
        print(f"  {i+1}. {name}: {code}")
    
    print("\n4. 测试说明:")
    print("- 点击不同的类型标签，观察右侧指数标签的变化")
    print("- 点击指数标签，观察控制台输出的指数代码")
    print("- 只有沪深指数会显示图表")
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    test_extended_index_selection()
