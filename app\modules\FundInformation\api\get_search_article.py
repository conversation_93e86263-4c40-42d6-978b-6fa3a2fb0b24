import json

import requests
from bs4 import BeautifulSoup
from PyQt6.QtCore import QThread, pyqtSignal

class get_search_title(QThread):
    finished = pyqtSignal(list)
    def __init__(self,keyword,page):
        super().__init__()
        self.keyword = keyword
        self.page=page
        self.date = []
        self.url = []
        self.title = []
        self.title_or = []
        self.content = []
        self.mediaName = []#来源
        self.header = {
            "Referer": "https://fund.eastmoney.com/",
            "Cookie": "qgqp_b_id=50d74afee65419b05e9120f0df53c69f; emshistory=%5B%22%E6%AD%8C%E5%B0%94%E8%82%A1%E4%BB%BD%22%2C%22%E5%B1%B1%E4%B8%9C%E9%BB%84%E9%87%91%22%2C%22%E7%89%9B%E5%A5%B6%22%2C%22%E5%8C%97%E6%96%B9%E7%A8%80%E5%9C%9F%22%2C%22%E7%B4%AB%E9%87%91%E7%9F%BF%E4%B8%9A%22%5D; st_si=52815941868206; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=3; st_psi=2025040522130754-112200304021-2791448515; st_asi=20250405221304403-118000300904-9986477932-Web_so_ss-2",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 Edg/134.0.0.0"
        }

    def re_cal_(self,str_,keyword,max_):
        re_str_l="<em>"+keyword+"</em>"
        str_=str_.replace(re_str_l,keyword)
        str_=self.max_len_(str_,max_)
        str_=str_.replace(keyword,re_str_l)
        return str_

    def max_len_(self,str_,max_,):
        len_=len(str_)
        if len_ > max_:
            data= str_[:max_]
        else:
            data = str_[:len_]
        return data

    def blue_style(self,keyword,title):
        print(title)
        red_style = f"""<span style="color:red"><strong>{keyword}</strong></span>"""
        count=title.count(red_style)
        blue_start = f"""<span style="color:#293598">"""
        blue_end = f"""</span>"""
        title_ = ""
        temp_l = []
        if count==0:
            return blue_start+title+blue_end
        else:
            l = title.split(red_style)
            print(l)
            for i in l:
                temp = blue_start + i + blue_end
                temp_l.append(temp)
            print(temp_l)
            for i in range(count):
                title_+=temp_l[i]
                title_+=red_style
            title_+=temp_l[-1]
            return title_





    def run(self):
        self.base_url=f'https://search-api-web.eastmoney.com/search/jsonp?cb=jQuery35108729933498276836_1743862416778&param=%7B%22uid%22%3A%22%22%2C%22keyword%22%3A%22{self.keyword}%22%2C%22type%22%3A%5B%22cmsArticleWebOld%22%5D%2C%22client%22%3A%22web%22%2C%22clientType%22%3A%22web%22%2C%22clientVersion%22%3A%22curr%22%2C%22param%22%3A%7B%22cmsArticleWebOld%22%3A%7B%22searchScope%22%3A%22default%22%2C%22sort%22%3A%22default%22%2C%22pageIndex%22%3A{self.page}%2C%22pageSize%22%3A10%2C%22preTag%22%3A%22%3Cem%3E%22%2C%22postTag%22%3A%22%3C%2Fem%3E%22%7D%7D%7D&_=1743862416779'
        data=requests.get(self.base_url).text
        data = json.loads(data.strip("jQuery35108729933498276836_1743862416778(").replace(")", ""))
        temp_title,temp_content,temp_date=[],[],[]

        for i in data["result"]["cmsArticleWebOld"]:
            temp_date.append(i["date"])
            self.url.append(i["url"])
            temp_title.append(" "+i["title"])
            temp_content.append(i["content"])
            self.mediaName.append(["mediaName"])
            self.title_or.append(i["title"].replace("<em>","").replace("</em>",""))
        #对标题限制
        temp_title=[self.re_cal_(i,self.keyword,33) for i in temp_title]
        temp_content=[self.re_cal_(i,self.keyword,86) for i in temp_content]

        red_style = f"""<span style="color:red"><strong>{self.keyword}</strong></span>"""

        gray_style_start = f"""<span style="color:gray">"""
        gray_style_end = f"""&nbsp;-&nbsp;</span>"""
        # 标题处理
        self.title= [i.replace(f"<em>{self.keyword}</em>", red_style) for i in temp_title]
        print(self.title)
        self.title= [self.blue_style(self.keyword,i) for i in self.title]

        content_ = [i.replace(f"<em>{self.keyword}</em>", red_style) for i in temp_content]
        self.date = [gray_style_start + i + gray_style_end for i in temp_date]
        for i in range(10):
            self.content.append(self.date[i] + content_[i])
        self.finished.emit([self.date,self.url,self.title,self.content,self.mediaName,self.title_or])
