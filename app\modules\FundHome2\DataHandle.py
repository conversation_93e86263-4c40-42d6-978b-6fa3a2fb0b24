import os

class DataHandle():
    # 类变量
    history_file = os.path.join(os.path.dirname(__file__), "src/fund_history.txt")

    @staticmethod
    def handle_index_selection(index_name: str, index_code: str):
        """处理指数选择，可以在这里添加更多的数据处理逻辑"""
        try:

            # 这里可以添加更多的数据处理逻辑
            # 比如：获取指数数据、更新图表、保存选择状态等

            return {
                "name": index_name,
                "code": index_code,
                "status": "success"
            }

        except Exception as e:
            print(f"DataHandle处理指数选择失败: {e}")
            return {
                "name": index_name,
                "code": index_code,
                "status": "error",
                "error": str(e)
            }



    @staticmethod
    def return_img_url(zs_code:str,zs_time_index:int):
        """净值图"""
        time_list=["","M1","M2","M3","M4",]
        if zs_time_index==0:
            k="r"
        else:
            k="t"
        base_url=f"https://webquotepic.eastmoney.com/GetPic.aspx?imageType={k}&type={time_list[zs_time_index]}&token=44c9d251add88e27b65ed86506f6e5da&nid={zs_code}&timespan=1754308058"
        return base_url

    @staticmethod
    def return_img_url_2(zs_code: str, zs_time_index: int,unit:int,zb_index_1:int,zb_index_2:int):
        """净值图"""
        time_list = ["", "W", "M", "M5", "M15","M30","M60"]
        # k线图指标,ef,formula
        ef = ["", "EXTENDED_MA", "EXTENDED_BOLL", "EXTENDED_SAR", "EXTENDED_BBI", ]
        formula = ["RSI", "KDJ", "MACD", "WR", "DMI", "BIAS", "OBV", "CCI", "ROC"]
        base_url = f"https://webquoteklinepic.eastmoney.com/GetPic.aspx?nid={zs_code}&type={time_list[zs_time_index]}&unitWidth={unit}&ef={ef[zb_index_1]}&formula={formula[zb_index_2]}&AT=1&imageType=KXL&timespan=1754323595"
        return base_url


    def return_zs_info_color(self,start:str,data:list):
        #最高，最低，涨跌幅，涨跌额
        start=eval(start)
        result=[]
        s = []
        for i in data:
            s.append(float(i.strip("%")))
        result=self.compare_zs(s[0],start,result)
        result=self.compare_zs(s[1],start,result)
        result=self.compare_zs(s[2],0,result)
        result=self.compare_zs(s[3],0,result)
        return result


    # @staticmethod
    def compare_zs(self,a,b,arr:list):
        if a>b:
            arr.append(1)
        elif a==b:
            arr.append(0)
        else:
            arr.append(-1)
        return arr

    def trans_bkzj_data(self,bk_name,bk_value):
        # bk_name已经是一个列表，不需要split
        categories = bk_name
        vertical_categories = ['\n'.join(list(category)) for category in categories]
        # 创建分类和数据的配对，然后按数据从高到低排序
        paired_data = list(zip(vertical_categories, bk_value))
        paired_data.sort(key=lambda x: x[1], reverse=True)

        # 分离排序后的分类和数据
        sorted_categories = [item[0] for item in paired_data]
        sorted_data = [item[1] for item in paired_data]
        return {
            'categories': sorted_categories,
            'data': sorted_data
        }



DataHandle=DataHandle()

