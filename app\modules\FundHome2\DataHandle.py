import os

class DataHandle():
    # 类变量
    history_file = os.path.join(os.path.dirname(__file__), "src/fund_history.txt")

    @staticmethod
    def handle_index_selection(index_name: str, index_code: str):
        """处理指数选择，可以在这里添加更多的数据处理逻辑"""
        try:

            # 这里可以添加更多的数据处理逻辑
            # 比如：获取指数数据、更新图表、保存选择状态等

            return {
                "name": index_name,
                "code": index_code,
                "status": "success"
            }

        except Exception as e:
            print(f"DataHandle处理指数选择失败: {e}")
            return {
                "name": index_name,
                "code": index_code,
                "status": "error",
                "error": str(e)
            }

    @staticmethod
    def return_img_url(zs_code:str,zs_time_index:int):
        """净值图"""
        time_list=["","M1","M2","M3","M4",]
        if zs_time_index==0:
            k="r"
        else:
            k="t"
        if zs_code in ["000001","000300","000016","000003","000688"]:
            c="1"
        else:c="0"
        base_url=f"https://webquotepic.eastmoney.com/GetPic.aspx?imageType={k}&type={time_list[zs_time_index]}&token=44c9d251add88e27b65ed86506f6e5da&nid={c}.{zs_code}&timespan=1754308058"
        return base_url

    @staticmethod
    def return_img_url_2(zs_code: str, zs_time_index: int,unit:int):
        """净值图"""
        time_list = ["", "W", "M", "M5", "M15","M30","M60"]
        if zs_code in ["000001", "000300", "000016", "000003", "000688"]:
            c = "1"
        else:
            c = "0"

        base_url = f"https://webquoteklinepic.eastmoney.com/GetPic.aspx?nid={c}.{zs_code}&type={time_list[zs_time_index]}&unitWidth={unit}&ef=&formula=RSI&AT=1&imageType=KXL&timespan=1754323595"
        return base_url


DataHandle=DataHandle()

