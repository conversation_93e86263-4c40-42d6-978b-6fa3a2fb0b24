"""
测试清空基金历史记录功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.modules.FundHome1.DataHandle import DataHandle

def test_clear_history():
    """测试清空基金历史记录功能"""
    
    print("=== 测试清空基金历史记录功能 ===\n")
    
    # 1. 先添加一些测试数据
    test_funds = [
        "易方达蓝筹精选混合型证券投资基金",
        "华夏成长混合型证券投资基金", 
        "嘉实新兴产业股票型证券投资基金",
        "广发稳健增长混合型证券投资基金A类",
        "南方成份精选混合型证券投资基金A类份额"
    ]
    
    print("1. 添加测试数据到历史记录:")
    for fund in test_funds:
        DataHandle.add_fund_to_history(fund)
        print(f"   ✅ 添加: {fund}")
    
    print("\n" + "="*60 + "\n")
    
    # 2. 查看添加后的历史记录
    print("2. 添加后的历史记录:")
    history_before = DataHandle.get_fund_history()
    for i, fund in enumerate(history_before):
        print(f"   {i+1}. {fund}")
    
    print(f"\n   总计: {len(history_before)} 条记录")
    
    print("\n" + "="*60 + "\n")
    
    # 3. 执行清空操作
    print("3. 执行清空操作:")
    success = DataHandle.clear_fund_history()
    
    if success:
        print("   ✅ 清空操作成功")
    else:
        print("   ❌ 清空操作失败")
    
    print("\n" + "="*60 + "\n")
    
    # 4. 验证清空结果
    print("4. 验证清空结果:")
    history_after = DataHandle.get_fund_history()
    
    if len(history_after) == 0:
        print("   ✅ 历史记录已完全清空")
        print("   ✅ fund_history.txt 文件已清空")
    else:
        print(f"   ❌ 清空失败，仍有 {len(history_after)} 条记录:")
        for i, fund in enumerate(history_after):
            print(f"      {i+1}. {fund}")
    
    print("\n" + "="*60 + "\n")
    
    # 5. 测试清空后再添加记录
    print("5. 测试清空后再添加记录:")
    new_fund = "测试基金清空后添加"
    DataHandle.add_fund_to_history(new_fund)
    print(f"   添加: {new_fund}")
    
    history_final = DataHandle.get_fund_history()
    print(f"   当前历史记录: {history_final}")
    
    if len(history_final) == 1 and history_final[0] == new_fund:
        print("   ✅ 清空后添加功能正常")
    else:
        print("   ❌ 清空后添加功能异常")
    
    print("\n" + "="*60)
    print("测试总结:")
    print("✅ DataHandle.clear_fund_history() 方法已实现")
    print("✅ 可以完全清空 fund_history.txt 文件")
    print("✅ 清空后可以正常添加新记录")
    print("✅ 界面绑定: self.ui.clear_history_lb.mousePressEvent")

def test_file_operations():
    """测试文件操作"""
    print("\n=== 测试文件操作 ===")
    
    # 检查历史文件是否存在
    history_file = DataHandle.history_file
    print(f"历史文件路径: {history_file}")
    
    if os.path.exists(history_file):
        print("✅ 历史文件存在")
        
        # 读取文件内容
        with open(history_file, "r", encoding="utf-8") as f:
            content = f.read()
        
        if content.strip() == "":
            print("✅ 文件内容为空（已清空）")
        else:
            lines = content.strip().split('\n')
            print(f"📄 文件内容（{len(lines)} 行）:")
            for i, line in enumerate(lines):
                print(f"   {i+1}. {line}")
    else:
        print("❌ 历史文件不存在")

if __name__ == "__main__":
    test_clear_history()
    test_file_operations()
