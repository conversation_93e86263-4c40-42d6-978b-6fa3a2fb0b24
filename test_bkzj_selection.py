"""
测试板块资金流选择功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication, QMainWindow
from main_w import Ui_MainWindow
from app.modules.FundHome2.FundHome2 import FundHome2

def test_bkzj_selection():
    """测试板块资金流选择功能"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    ui = Ui_MainWindow()
    ui.setupUi(main_window)
    
    # 创建 FundHome2 实例
    fund_home2 = FundHome2(ui)
    
    # 显示主窗口
    main_window.show()
    
    print("测试板块资金流选择功能:")
    print("=" * 50)
    
    print("\n1. 类型标签功能:")
    print("- 3个类型标签：bkzj_type_1 到 bkzj_type_3")
    print("- 默认选中地域资金流")
    print("- 点击不同类型标签应该切换选中状态")
    print("- 选中的类型标签应该使用橙色背景样式")
    print("- 未选中的类型标签应该使用灰色文字样式")
    
    print("\n2. 时间RadioButton功能:")
    print("- 3个RadioButton：bkzj_time_1 到 bkzj_time_3")
    print("- 使用QButtonGroup确保互斥选择")
    print("- 默认选中今日排行")
    print("- 点击RadioButton应该输出对应的索引")
    
    print("\n3. 板块资金流类型数据:")
    for i, name in enumerate(fund_home2.bkzj_type_names):
        print(f"  类型{i}: {name}")
    
    print("\n4. 板块资金流时间数据:")
    for i, name in enumerate(fund_home2.bkzj_time_names):
        print(f"  时间{i}: {name}")
    
    print("\n5. 测试说明:")
    print("- 点击不同的类型标签，观察样式变化和控制台输出")
    print("- 点击不同的RadioButton，观察控制台输出的索引")
    print("- 类型索引：bkzj_type_1=0, bkzj_type_2=1, bkzj_type_3=2")
    print("- 时间索引：bkzj_time_1=0, bkzj_time_2=1, bkzj_time_3=2")
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    test_bkzj_selection()
