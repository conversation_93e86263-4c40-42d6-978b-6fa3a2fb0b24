import sys
import os
import tushare as ts
import pandas as pd
from PyQt6.QtWidgets import QApplication, QMainWindow
from PyQt6.QtWebEngineWidgets import QWebEngineView
from PyQt6.QtCore import QUrl


class EChartsWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('ECharts with PyQt6')
        self.setGeometry(100, 100, 1150, 380)

        # 获取股票数据
        self.stock_data = self.get_stock_data()

        # 创建 QWebEngineView 组件
        self.browser = QWebEngineView()

        # 获取当前目录的绝对路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        local_file_path = os.path.join(current_dir, 'demo.html')#修改网页地址


        # 使用 QUrl.fromLocalFile() 加载本地 HTML 文件
        url = QUrl.fromLocalFile(local_file_path)

        # 加载 HTML 文件到 QWebEngineView 中
        self.browser.setUrl(url)

        # 页面加载完成后，执行 JavaScript 方法
        self.browser.loadFinished.connect(self.on_load_finished)

        # 设置浏览器为中央控件
        self.setCentralWidget(self.browser)

    def get_stock_data(self):
        with open("../../txt/kva.txt", "r", encoding="utf8") as f:
            data_r = f.read()
        data_1 = eval(data_r.strip("jQuery35101085796900623961_1741149660730(").replace(");", ""))
        data_2 = data_1["data"]["klines"]
        data_2_time = []
        data_2_now = []
        data_2_yes = []
        data_2_max = []
        data_2_min = []
        for i in data_2:
            data_2_time.append(i.split(",")[0])
            data_2_now.append(float(i.split(",")[1]))
            data_2_yes.append(float(i.split(",")[2]))
            data_2_max.append(float(i.split(",")[3]))
            data_2_min.append(float(i.split(",")[4]))
        data_f = {'dates': data_2_time, '开盘': data_2_now, '收盘': data_2_yes, '最高': data_2_max,
                  '最低': data_2_min}
        # 获取所需数据的df,代码，日k
        df = pd.DataFrame(data_f)
        #
        # print(df)
        # 将日期设置为索引并按时间排序
        df.index = pd.to_datetime(df['dates'])
        df = df.sort_index()

        # 提取需要的列（日期、开盘、收盘、最高、最低）
        stock_data = df[['开盘', '收盘', '最高', '最低']]


        # 生成 K 线图所需的数组
        kline_data = []
        # print(*stock_data.itertuples())
        for row in stock_data.itertuples():
            kline_data.append([row[1], row[2], row[3], row[4]])

        # 日期
        dates = stock_data.index.strftime('%Y-%m-%d').tolist()

        # 生成关键点
        point_data = [{
            "name": 'highest value',
            "type": 'max',
            "valueDim": '最高'
        },
            {
            "name": 'lowest value',
            "type": 'min',
            "valueDim": '最低'
        }]

        # 生成平均值
        markLine = self.get_markline_data(stock_data)

        return {
            'dates': dates,
            'kline_data': kline_data,
            'point_data': point_data,
            'markLine': markLine,
        }

    def get_markline_data(self, stock_data):
        # 计算收盘价的平均值
        average_price = stock_data['收盘'].mean()

        # 生成 markLine 数据
        markline_data = {
            'data': [
                {
                    'yAxis': average_price,  # 将 y 轴的值设置为均值
                    'name': '平均值',
                    'lineStyle': {
                        'color': '#FF0000',  # 线条颜色
                        'type': 'dashed',    # 线条类型（虚线）
                        'width': 2           # 线条宽度
                    },
                    'label': {
                        'formatter': f'平均值: {average_price:.2f}',  # 显示的文本
                        'position': 'end',    # 文本位置
                        'color': '#FF0000'    # 文本颜色
                    }
                }
            ]
        }
        return markline_data

    def on_load_finished(self):
        # 页面加载完成后，调用 JavaScript 的 setData 函数
        js_code = f"setData({self.stock_data['dates']}, {self.stock_data['kline_data']},{self.stock_data['point_data']},{self.stock_data['markLine']});"
        print(f"{js_code}")
        self.browser.page().runJavaScript(js_code, self.js_callback)

    def js_callback(self, result):
        print(f"JavaScript Result: {result}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = EChartsWindow()
    window.show()
    sys.exit(app.exec())

