import ast
from datetime import date, timedelta
import requests
from urllib.parse import urlencode
import random
import json
#从首页进入调用此函数以及顶部按钮调用此函数
# 其他排序或自定义tab不使用
class kfjj_data:
    def __init__(self):
        self.base_url="https://fund.eastmoney.com/data/rankhandler.aspx?"
        self.header={
                        "Cookie":'qgqp_b_id=50d74afee65419b05e9120f0df53c69f; fund_registerAd_1=1; st_si=18639092801927; st_asi=delete; ASP.NET_SessionId=iurcwwhnaijp1l0zlgb2mjnn; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=2; st_psi=20250319095514921-112200312936-9443065310',
                        "Referer":"https://fund.eastmoney.com/data/fundranking.html"
                    }
        self.kf_type=["all","zs","gp","hh","zq","qdii","fof"]
        self.kf_index=0
        today = date.today()
        last_year_today = today - timedelta(days=365)
        self.today = today.strftime("%Y-%m-%d")
        self.last_year_today = last_year_today.strftime("%Y-%m-%d")
        self.params={
                    "op": "ph",#排行
                    "dt": "kf",#开放基金
                    "ft":self.kf_type[self.kf_index],#fund_type类型：全部，指数，股票，混合，债券，qdii,fof ["all","zs","gp","hh","zq","qdii","fof"]
                    "rs":"",
                    "gs":0,
                    "sc":"1nzf",#排序依据 基金代码 基金简称， 日期，单位净值，累计净值，且增长率，近1周，近1月，近3月，近6月，近1年，近2年，近3年，今年来，成立来，自定义
                    #["dm","jc","jzrq","dwjz","ljjz","rzdf","zzf","1yzf","3yzf","6yzf","1nzf","2nzf","3nzf","jnzf","lnzf","qjzf"]
                    "st":"desc",#升序，降序["desc","asc"]
                    "sd":self.last_year_today,#sc="qjzf" sd起始日期
                    "ed": self.today,#sc="qjzf" ed结束日期
                    "qdii":"",#所有默认为空   %7C:|
                    #指数，债券，qdii，
                    #指数{"沪深指数":"053|","行业主题":"054|","大盘指数":"01|","中小盘指数":"02,03|","股票指数":"001|","债券指数":"003|","海外指数":"000003|"}
                    #债券{"长期纯债":"041|","短期纯债":"042|","混合债基":"043|","定期开放债券":"008|","可转债":"045|",}
                    #qdii{"全球股票":"311","亚太股票":"312","大中华区股票":"313","美国股票":"317","股债混合":"320","债券":"330","商品":"340",}
                    "tabSubtype":",,,,,",
                    "pi":1,#页数
                    "pn":50,#单页条数
                    "dx":1,
                    "v":0.7143502235731323
                }
        self.bl_kf_pos=[1,2]
        self.gr_kf_pos=[6,7,8,9,10,11,12,13,14,15,16]
        self.top_lb=["全部","指数型","股票型","混合型","债券型","QDII","FOF"]
        self.tb_header_lb=["基金代码","基金名称","基金简称","日期","单位净值","累计净值","日增长率","近1周","近1月","近3月","近6月","近1年","近2年","近3年","今年来","成立以来","成立日期","自定义"]

    def get_kj_type_info(self,):
        return self.bl_kf_pos,self.gr_kf_pos,self.top_lb,self.tb_header_lb


    def return_kf_data_1(self,index):
        try:
            self.kf_index=index
            url=self.base_url+f"op={self.params["op"]}&"+f"dt={self.params["dt"]}&"+f"ft={self.kf_type[self.kf_index]}&"+f"re={self.params["rs"]}&"+f"gs={self.params["gs"]}&"+f"sc={self.params["sc"]}&"+f"st={self.params["st"]}&"+f"sd={self.params["sd"]}&"+f"ed={self.params["ed"]}&"+f"qdii={self.params["qdii"]}&"+f"tabSubtype={self.params["tabSubtype"]}&"+f"pi={self.params["pi"]}&"+f"pn={self.params["pn"]}&"+f"dx={self.params["dx"]}&"+f"v={self.params["v"]}"
            data = requests.get(url, headers=self.header).text
            print(data)
            json1 = data.replace("var rankData = {datas:", "").replace("};", "")
            json2 = data.replace("var rankData = {datas:", "")
            split_s = json2.index("]") + 1  # 分割符索引
            data_l = ast.literal_eval(json2[0:split_s])  # 行数据列表
            data_list = []
            for i in data_l:
                tt = list(i.split(",")[0:17])
                tt = [
                    "-" if item == "" and 5 <= i < 15  # 对第 5-15 个元素，空字符串替换为 "-"
                    else f"{float(item):.2f}%" if item != "" and 5 < i < 16  # 对第 5-15 个元素，非空字符串先转化为浮点型，再保留两位小数并加上 "%"
                    else item  # 其他元素保持不变
                    for i, item in enumerate(tt)
                ]
                if i.split(",")[18] == "":
                    tt.append("-")
                else:
                    tt.append("{:.2f}%".format(float(i.split(",")[18])))
                data_list.append(tt)
            data_all_info=json1[split_s+1:].split(",")
            info_key,info_value=[],[]
            for i in range(0,len(data_all_info)):
                info_key.append(data_all_info[i].split(":")[0])
                info_value.append(int(data_all_info[i].split(":")[1]))
            info_dict=dict(zip(info_key,info_value))
            print(info_dict)
            return data_list,info_dict
        except Exception as e:
            print(e)

kfjj_data=kfjj_data()



