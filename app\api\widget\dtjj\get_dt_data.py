import json
import math
import time

import requests
from PyQt6.QtCore import pyqtSignal, QThread


class dt_web(QThread):
    progress_updated = pyqtSignal(int)  # 进度更新信号
    finished = pyqtSignal(str, int)  # 操作完成信号，返回 web_data 和 page1
    def __init__(self,index,page):
        super().__init__()
        self.index = index
        self.page = page

        self.url_list=[
            "https://fund.eastmoney.com/api/Dtshph.ashx?t=0&c=dwjz&s=desc&issale=1&page=1&psize=100&callback=jQuery18307391960683543473_1742206657552&_=1742206657659",#全部
            "https://fund.eastmoney.com/api/Dtshph.ashx?t=1&c=dwjz&s=desc&issale=1&page=1&psize=100&callback=jQuery18307501794706457241_1742206982483&_=1742206982535",#股票
            "https://fund.eastmoney.com/api/Dtshph.ashx?t=2&c=dwjz&s=desc&issale=1&page=1&psize=100&callback=jQuery1830796136492032189_1742207013309&_=1742207013319",#混合
            "https://fund.eastmoney.com/api/Dtshph.ashx?t=3&c=dwjz&s=desc&issale=1&page=1&psize=100&callback=jQuery183025622923826546407_1742207043178&_=1742207043244",#债券
            "https://fund.eastmoney.com/api/Dtshph.ashx?t=4&c=dwjz&s=desc&issale=1&page=1&psize=100&callback=jQuery1830563764113766912_1742207070564&_=1742207070572",#指数
            "https://fund.eastmoney.com/api/Dtshph.ashx?t=5&c=dwjz&s=desc&issale=1&page=1&psize=100&callback=jQuery18309661930991781378_1742207095436&_=1742207095495",#qdii
            "https://fund.eastmoney.com/api/Dtshph.ashx?t=6&c=dwjz&s=desc&issale=1&page=1&psize=100&callback=jQuery18307031246767408403_1742207119818&_=1742207119826"#fof
            ]

    def run(self):
        #TODO :配置模拟数据
        url =self.url_list[self.index].replace("page=1",f"page={self.page}")
        data = requests.get(url).text.strip("jQuery18305828562889850959_1742192835426(").replace(')', '')
        data1,total_page=json.loads(data)["data"],json.loads(data)["total"]
        page1=math.ceil(total_page/100)
        data1=data1.replace("定投","").replace("选择<br/>比较","").replace("可购","").replace("全部","")+"</body></html"
        with open(r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\api\widget\dtjj\dt_web_head","r",encoding="utf8")as f:
            web_css=f.read()
        web_data=web_css+data1
        # 发送完成信号
        self.finished.emit(web_data, page1)

# dt_web_data=dt_web()


