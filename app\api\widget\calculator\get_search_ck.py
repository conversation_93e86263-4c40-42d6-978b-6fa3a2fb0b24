
import asyncio
import json
from PyQt6.QtCore import QThread, pyqtSignal
import requests
from bs4 import BeautifulSoup

class get_search_bank_data(QThread):
    finished = pyqtSignal(list)
    def __init__(self, search_name):
        super().__init__()
        self.search_name = search_name
        self.result_list = []
        self.header = {
            "User-Agent": " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0",
            "referer": "https://www.csai.cn/bankrate/",
            "Cookie": "_sid_=b480137c68b2abde81f32d89102e3a32; EGG_SESSION_ID=RgLE9MzppEKOkeK1iGdVf1IbYx7r-R4yhjNrzHFFgTphD-bJhzk3_3YuWPnwIxmb5BppSDl1FQdKa9rEfz71-gCBytOwa3FgUSEd3WEZD3mE7JQeSXofgYdjDFZmyLwBKUJU6uQwMNs2BrgPJW1hgA=="
        }

    def run(self):
        try:
            self.url =f"https://www.csai.cn/bankrate/newBankView.do?searchName={self.search_name}&page=&banksort=&fieldName=&scrollId=dingqiDeposit"
            response = requests.get(self.url, self.header ,verify=False)
            response_text = response.text
            soup = BeautifulSoup(response_text, 'html.parser')
            bank_rows = soup.find_all('div', class_='tables_tr')
            bank_ck_list = []
            # 遍历每个银行行
            for row in bank_rows:
                bank_name = [row.find('a').get_text(strip=True)]
                bank_tds = row.find_all('span', class_='bank_td')
                numbers = [td.get_text(strip=True) for td in bank_tds]
                if len(numbers) == 6:
                    bank_name.extend(numbers)
                    bank_ck_list.append(bank_name)
            self.finished.emit(bank_ck_list)
        except Exception as e:
            print(e)
