"""
演示修复后的基金历史记录功能
"""

import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QWidget, QLabel, QVBoxLayout
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

# 模拟 DataHandle 的功能
class MockDataHandle:
    history_data = [
        "汇添富价值精选混合型证券投资基金A类份额",
        "富国天惠精选成长混合型证券投资基金A类",
        "博时主题行业混合型证券投资基金LOF",
        "南方成份精选混合型证券投资基金A类份额",
        "广发稳健增长混合型证券投资基金A类",
        "嘉实新兴产业股票型证券投资基金",
        "华夏成长混合型证券投资基金",
        "易方达蓝筹精选混合型证券投资基金"
    ]
    
    @staticmethod
    def get_fund_history():
        """返回完整的基金名称"""
        return MockDataHandle.history_data[:8]
    
    @staticmethod
    def get_fund_history_for_display():
        """返回截断的基金名称用于显示"""
        full_history = MockDataHandle.get_fund_history()
        formatted_list = []
        for item in full_history:
            if len(item) > 10:
                formatted_list.append(item[:10] + "...")
            else:
                formatted_list.append(item)
        return formatted_list
    
    @staticmethod
    def add_fund_to_history(fund_name):
        """添加基金到历史记录"""
        if fund_name in MockDataHandle.history_data:
            MockDataHandle.history_data.remove(fund_name)
        MockDataHandle.history_data.insert(0, fund_name)

class FixedHistoryDemo(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("修复后的基金历史记录演示")
        self.setGeometry(100, 100, 1200, 300)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 添加说明
        info_label = QLabel("✅ 修复完成：保存完整名称，显示截断名称", self)
        info_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2E8B57; margin: 10px;")
        layout.addWidget(info_label)
        
        self.history_labels = []
        self.create_history_labels()
        
        # 添加说明文字
        instruction_label = QLabel(
            "点击任意基金名称查看详情。完整名称用于代码匹配，截断名称用于显示。\n"
            "鼠标悬停可查看完整名称。", 
            self
        )
        instruction_label.setStyleSheet("color: #666; font-size: 12px; margin: 10px;")
        instruction_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(instruction_label)
    
    def create_history_labels(self):
        """创建历史记录标签"""
        # 清除之前的标签
        for label in self.history_labels:
            label.deleteLater()
        self.history_labels.clear()
        
        # 获取完整历史记录（用于事件绑定）
        full_history = MockDataHandle.get_fund_history()
        
        if not full_history:
            return
        
        # 计算布局参数
        start_x = 50
        start_y = 80
        label_height = 40
        available_width = 1100
        spacing = 10
        label_width = (available_width - (len(full_history) - 1) * spacing) // len(full_history)
        
        for i, full_fund_name in enumerate(full_history):
            # 创建标签
            history_label = QLabel(parent=self.centralWidget())
            
            # 设置位置和尺寸
            x_pos = start_x + i * (label_width + spacing)
            history_label.setGeometry(x_pos, start_y, label_width, label_height)
            
            # 生成显示用的截断名称
            display_name = full_fund_name
            if len(full_fund_name) > 10:
                display_name = full_fund_name[:10] + "..."
            
            # 设置显示文本
            history_label.setText(display_name)
            history_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            
            # 设置工具提示显示完整名称
            history_label.setToolTip(f"完整名称: {full_fund_name}")
            
            # 设置字体
            font = QFont()
            font.setPointSize(10)
            font.setBold(True)
            history_label.setFont(font)
            
            # 设置样式表
            history_label.setStyleSheet("""
                QLabel {
                    font-size: 10px;
                    font-weight: bold;
                    color: #333333;
                    background-color: #f5f5f5;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    padding: 5px;
                }
                QLabel:hover {
                    color: #FF4400;
                    background-color: #fff;
                    border-color: #FF4400;
                }
            """)
            
            # 绑定点击事件 - 使用完整的基金名称
            history_label.mousePressEvent = lambda event, fund=full_fund_name, idx=i: self.on_label_clicked(fund, idx, event)
            
            # 显示标签
            history_label.show()
            
            # 添加到管理列表
            self.history_labels.append(history_label)
    
    def on_label_clicked(self, full_fund_name, index, event):
        """标签点击事件处理"""
        print(f"\n=== 点击事件 ===")
        print(f"完整基金名称: {full_fund_name}")
        print(f"索引: {index}")
        print(f"名称长度: {len(full_fund_name)} 字符")
        
        # 模拟基金代码匹配
        print(f"✅ 可以用完整名称匹配基金代码")
        
        # 更新浏览记录：将点击的基金移到最前面
        MockDataHandle.add_fund_to_history(full_fund_name)
        
        # 重新创建标签以反映新的顺序
        self.create_history_labels()
        
        print(f"✅ 历史记录已更新，{full_fund_name[:10]}... 移到最前面")

def main():
    app = QApplication(sys.argv)
    
    demo = FixedHistoryDemo()
    demo.show()
    
    print("🎉 基金历史记录修复演示")
    print("="*50)
    print("✅ 文件中保存完整基金名称")
    print("✅ 显示时使用截断名称（美观）")
    print("✅ 事件绑定使用完整名称（功能正确）")
    print("✅ 可以正确匹配基金代码")
    print("✅ 鼠标悬停显示完整名称")
    print("✅ 点击后正确更新历史记录顺序")
    print("="*50)
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
