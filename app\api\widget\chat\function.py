from PyQt6.QtCore import Qt
from PyQt6.QtGui import QPixmap, QIcon
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel, QMessageBox

from app.api.widget.chat.chat_api import StreamResponseThread
def init_chatai_widget_status(self):
    self.img_path = r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\img"  # 图片地址
    self.model_name = "deepseek-v3-241226"
    self.chat_history = []  # 保存聊天记录
    self.current_thread = None  # 当前流式输出线程
    self.comboBox_7.currentIndexChanged.connect(self.change_chat_model)
    self.chat_container = QWidget()  # 17
    self.chat_layout = QVBoxLayout(self.chat_container)  # 34
    self.chat_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
    self.scrollArea_2.setWidget(self.chat_container)
    self.pre_send_status = True
    self.chat_bg_lb.setPixmap(QPixmap(self.img_path + r'\chat_bg.png').scaled(1720, 1000))
    icon = QIcon(r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\img\send_bg32.png")  # 替换为你的图片路径
    self.chat_send_btn.setIcon(icon)
    self.chat_send_btn.setIconSize(QPixmap(r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\img\send_bg32.png").size())  # 设置图标大小
    self.chat_send_btn.setToolTip("发送")
    self.chat_input.setPlaceholderText("请输入您的问题...")
    self.chat_send_btn.clicked.connect(self.toggle_send_stop)  # 绑定切换按钮事件
    self.chat_clear_btn.clicked.connect(self.clear_context)



def change_chat_model(self):
    if self.comboBox_7.currentIndex() == 0:
        self.model_name = "deepseek-v3-241226"
    elif self.comboBox_7.currentIndex() == 1:
        self.model_name = "deepseek-r1-250120"
    elif self.comboBox_7.currentIndex() == 2:
        self.model_name = "deepseek-r1-distill-qwen-7b-250120"
    elif self.comboBox_7.currentIndex() == 3:
        self.model_name = "deepseek-r1-distill-qwen-32b-250120"
    elif self.comboBox_7.currentIndex() == 4:
        self.model_name = "doubao-1-5-pro-256k-250115"
    elif self.comboBox_7.currentIndex() == 5:
        self.model_name = "doubao-1-5-pro-32k-250115"

def toggle_send_stop(self):
    if self.pre_send_status:
        self.chat_send_btn.setToolTip("停止")
        self.send_question()
        self.pre_send_status = False
    else:
        self.pre_send_status = True
        self.stop_response()
        self.chat_send_btn.setToolTip("发送")

def send_question(self):
    try:
        question = self.chat_input.toPlainText().strip()
        if not question:
            QMessageBox.warning(self, "提示", "请输入问题！")
            return
        self.add_user_message(question)  # 添加用户问题到聊天窗口
        # 启动流式输出线程
        self.current_thread = StreamResponseThread(self.model_name, question, self.chat_history)
        self.current_thread.stream_signal.connect(self.add_ai_message_stream)
        self.current_thread.finished.connect(self.thread_finished)
        self.current_thread.start()
        self.chat_input.clear()  # 清空输入框
        self.toggle_send_stop_img(True)
    except Exception as e:
        print(e)

def stop_response(self):
    # 终止当前流式输出线程
    if self.current_thread and self.current_thread.isRunning():
        self.current_thread.stop()
        self.current_thread.wait()
    self.toggle_send_stop_img(False)

def add_user_message(self, message):
    # 创建用户问题的 widget
    user_widget = QWidget()
    user_layout = QVBoxLayout(user_widget)
    user_label = QLabel(f"用户：{message}")
    user_label.setAlignment(Qt.AlignmentFlag.AlignRight)
    user_label.setStyleSheet("""
                                color: #0A0A0A; 
                                font-weight: bold;
                                background-color:#F4F5FF;
                                border-radius:10px;
                                border:1px solid #9281FF;
                                padding:5px;
                                font-size:17px;
                            """
                             )
    user_layout.addWidget(user_label)
    self.chat_layout.addWidget(user_widget)
    self.chat_history.append({"role": "user", "content": message})

def add_ai_message_stream(self, chunk):
    try:
        # 获取最后一个 AI 回答的 widget，如果没有则新建
        if not self.chat_history or not self.chat_history[-1]["role"] == "assistant":
            self.chat_history.append({"role": "assistant", "content": ""})
            ai_widget = QWidget()
            ai_widget.setMinimumHeight(100)  # 设置最小高度
            ai_layout = QVBoxLayout(ai_widget)
            self.ai_label = QLabel()
            self.ai_label.setToolTip("双击复制文本")
            self.ai_label.setStyleSheet("""
            QLabel{
            color: #222222; 
            background-color:white;
            border-radius:10px;
            border:1px solid #EAEDF1;
            fon-size:15px;
            padding:5px;
            }

              QLabel:hover{
            background-color:#F1F9FE;
                border: 1px solid #ccc;
            }
            """)
            self.ai_label.setWordWrap(True)
            ai_layout.addWidget(self.ai_label)
            self.chat_layout.addWidget(ai_widget)
        else:
            self.ai_label = self.chat_layout.itemAt(self.chat_layout.count() - 1).widget().layout().itemAt(
                0).widget()
        # 追加流式输出内容
        self.ai_label.setText(self.ai_label.text() + chunk)
        self.chat_history[-1]["content"] += chunk  # 更新聊天历史
        self.scrollArea_2.ensureWidgetVisible(self.ai_label)
    except Exception as e:
        print(e)

def clear_context(self):
    # 清除聊天记录
    for i in reversed(range(self.chat_layout.count())):
        self.chat_layout.itemAt(i).widget().setParent(None)
    self.chat_history = []

def thread_finished(self):
    # 线程结束后的操作
    self.current_thread = None
    self.toggle_send_stop_img(False)

def toggle_send_stop_img(self, status):
    if status:
        icon = QIcon(r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\img\stop_16.png")  # 替换为你的图片路径
        self.chat_send_btn.setIcon(icon)
        self.chat_send_btn.setIconSize(
            QPixmap(r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\img\stop_16.png").size())  # 设置图标大小
        self.pre_send_status = False
        self.chat_send_btn.setToolTip("停止")
    else:
        icon = QIcon(r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\img\send_bg32.png")  # 替换为你的图片路径
        self.chat_send_btn.setIcon(icon)
        self.chat_send_btn.setIconSize(
            QPixmap(r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\img\send_bg32.png").size())  # 设置图标大小
        self.pre_send_status = True
        self.chat_send_btn.setToolTip("发送")