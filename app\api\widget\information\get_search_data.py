import json
import requests


class search_data:
    def __init__(self):
        pass

    def return_search_init(self,):
        url = "https://searchadapter.eastmoney.com/api/HotKeyword/GetBatch?cb=jQuery35109401063130308724_1743079285885&count=20&stockCount=30&token=D43BF722C8E33BDC906FB84D85E326E8&_=1743079285887"
        data = requests.get(url).text
        data = data.strip("jQuery35109401063130308724_1743079285885(").replace(")", "")
        data = json.loads(data)
        Currency = data["Data"]["Currency"]
        Stock = data["Data"]["Stock"]
        Currency_l = []
        Stock_l = []
        Currency_or=[]
        Stock_or=[]
        n,m=1,1
        for i in Currency:
            Currency_l.append(str(n)+"."+i.get("KeyPhrase"))
            Currency_or.append(i.get("KeyPhrase"))
            n+=1
        for j in Stock:
            Stock_l.append(str(m)+"."+j.get("Name") + "[" + j.get("Code") + "]")
            Stock_or.append(j.get("Name"))
            m+=1
        return Currency_l,Currency_or,Stock_l,Stock_or

search_data = search_data()
