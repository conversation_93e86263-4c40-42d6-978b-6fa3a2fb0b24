"""
类型提示优化示例 - 解决 PyCharm 代码提示问题
"""

from __future__ import annotations  # 启用延迟注解
from typing import TYPE_CHECKING, Optional, Union

# 只在类型检查时导入，不影响运行时性能
if TYPE_CHECKING:
    from main_w import Ui_MainWindow
    from PyQt6.QtWidgets import QWidget, QMainWindow

from PyQt6.QtWidgets import QMainWindow
from PyQt6.QtCore import QObject


class OptimizedMainWindow(QMainWindow):
    """优化的主窗口类 - 演示如何正确使用类型提示"""
    
    def __init__(self):
        super().__init__()
        # 方法1：直接类型注释（推荐）
        self.ui: Ui_MainWindow = self._create_ui()
        self.ui.setupUi(self)
        
        # 方法2：使用类型断言
        self._setup_ui_with_hints()
    
    def _create_ui(self) -> Ui_MainWindow:
        """创建 UI 实例的工厂方法"""
        from main_w import Ui_MainWindow
        return Ui_MainWindow()
    
    def _setup_ui_with_hints(self):
        """使用类型断言的方法"""
        # 这样在方法内部就有完整的代码提示
        ui = self.ui  # type: Ui_MainWindow
        
        # 现在 ui. 会有完整的代码提示
        # ui.setupUi(self)
        # ui.centralwidget.show()
        pass
    
    def access_ui_elements(self):
        """访问 UI 元素的示例"""
        # 方法1：直接访问（因为已经有类型注释）
        self.ui.centralwidget.show()
        
        # 方法2：局部类型断言
        ui = self.ui  # type: Ui_MainWindow
        ui.centralwidget.hide()
        
        # 方法3：使用 assert isinstance（运行时检查）
        assert isinstance(self.ui, object)  # 这里可以用具体类型
        self.ui.centralwidget.setVisible(True)


class TypeHintHelper:
    """类型提示辅助类"""
    
    @staticmethod
    def get_ui_type() -> type[Ui_MainWindow]:
        """获取 UI 类型"""
        from main_w import Ui_MainWindow
        return Ui_MainWindow
    
    @staticmethod
    def create_ui_instance() -> Ui_MainWindow:
        """创建 UI 实例"""
        ui_class = TypeHintHelper.get_ui_type()
        return ui_class()


# 使用示例
def example_usage():
    """使用示例"""
    
    # 创建主窗口
    window = OptimizedMainWindow()
    
    # 访问 UI 元素（有完整代码提示）
    window.ui.centralwidget.show()
    
    # 使用辅助类
    helper = TypeHintHelper()
    ui_instance = helper.create_ui_instance()
    
    return window


# 性能优化的装饰器
def lazy_import(module_name: str):
    """延迟导入装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 只在需要时才导入
            import importlib
            module = importlib.import_module(module_name)
            return func(module, *args, **kwargs)
        return wrapper
    return decorator


@lazy_import('main_w')
def create_ui_with_lazy_import(main_w_module):
    """使用延迟导入创建 UI"""
    return main_w_module.Ui_MainWindow()


if __name__ == "__main__":
    # 测试代码
    window = example_usage()
    print("类型提示优化示例创建成功！")
