import asyncio
import re

import aiohttp
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

from PyQt6.QtCore import pyqtSignal, QObject

from app.api.other.wrapp import calculate_time
from bs4 import BeautifulSoup
class url_infos_1():
    def __init__(self,url_l):
        self.urls=url_l
        self.results = [None] * 20
        self.html_start = """
                                <html>
                                    <body>"""
        self.html_end = """
                                </body>
                                </html>
                                """
        self.replace_t='<img src="https://np-newspic.dfcfw.com/download/D25411840164398846730_w690h389.jpg" class="em_handle_adv_close" />'
        self.replace_t2='<img src="https://np-newspic.dfcfw.com/download/D24723587176925434542_w690h180_o.jpg" class="em_handle_adv_close" />'

    async def fetch_url_content(self,session, url):
        try:
            async with session.get(url) as response:
                data = await response.text()
                soup = BeautifulSoup(data, 'html.parser')
                items = soup.find_all('div', class_='time-source')
                # 提取日期和来源
                info = items[0].text.replace(" ", "").replace("\r", "").split("\n", )
                date=info[1]
                source=info[5]
                pattern = r'<!--文章主体-->(.*?)<!--原文标题-->'
                match = re.search(pattern, data, re.DOTALL)
                article_=self.html_start+match.group(1).strip()+self.html_start
                content =[ date,source,article_]
                return content
        except requests.exceptions.RequestException as e:
            return  f"请求失败: {e}"

    async def main(self):
        async with aiohttp.ClientSession() as session:
            tasks = [self.fetch_url_content(session, url) for url in self.urls]
            results = await asyncio.gather(*tasks)
            for i, content in enumerate(results):
                self.results[i]=content
        return self.results

url_infos_1=url_infos_1

