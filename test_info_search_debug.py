"""
测试资讯搜索功能的调试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_search_data():
    """测试热搜数据获取"""
    try:
        from app.modules.FundInformation.api.get_search_data import search_data
        print("测试热搜数据获取...")
        hot_l, hot_or, stock_l, stock_or = search_data.return_search_init()
        print(f"热搜数据获取成功:")
        print(f"热搜关键词数量: {len(hot_l)}")
        print(f"股票数据数量: {len(stock_l)}")
        print(f"前5个热搜: {hot_l[:5]}")
        print(f"前5个股票: {stock_l[:5]}")
        return True
    except Exception as e:
        print(f"热搜数据获取失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_search_code():
    """测试搜索代码功能"""
    try:
        from app.modules.FundInformation.api.get_search_code import get_search_c
        print("\n测试搜索代码功能...")
        keyword = "招商"
        search_result_list, name_list = get_search_c.return_code_l(keyword)
        print(f"搜索'{keyword}'结果:")
        print(f"搜索结果数量: {len(search_result_list)}")
        print(f"名称列表数量: {len(name_list)}")
        if search_result_list:
            print(f"前3个搜索结果: {search_result_list[:3]}")
        return True
    except Exception as e:
        print(f"搜索代码功能失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_search_article():
    """测试搜索文章功能"""
    try:
        from app.modules.FundInformation.api.get_search_article import get_search_title
        print("\n测试搜索文章功能...")
        
        # 创建搜索线程但不启动，只测试初始化
        keyword = "基金"
        page = 1
        search_thread = get_search_title(keyword, page)
        print(f"搜索线程创建成功，关键词: {keyword}, 页码: {page}")
        
        # 测试URL构建
        expected_url = f'https://search-api-web.eastmoney.com/search/jsonp?cb=jQuery35108729933498276836_1743862416778&param=%7B%22uid%22%3A%22%22%2C%22keyword%22%3A%22{keyword}%22%2C%22type%22%3A%5B%22cmsArticleWebOld%22%5D%2C%22client%22%3A%22web%22%2C%22clientType%22%3A%22web%22%2C%22clientVersion%22%3A%22curr%22%2C%22param%22%3A%7B%22cmsArticleWebOld%22%3A%7B%22searchScope%22%3A%22default%22%2C%22sort%22%3A%22default%22%2C%22pageIndex%22%3A{page}%2C%22pageSize%22%3A10%2C%22preTag%22%3A%22%3Cem%3E%22%2C%22postTag%22%3A%22%3C%2Fem%3E%22%7D%7D%7D&_=1743862416779'
        print(f"URL构建正确")
        return True
    except Exception as e:
        print(f"搜索文章功能失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fund_information_init():
    """测试FundInformation初始化"""
    try:
        from PyQt6.QtWidgets import QApplication
        from main_w import Ui_MainWindow
        from app.modules.FundInformation.FundInformation import FundInformation
        
        print("\n测试FundInformation初始化...")
        app = QApplication(sys.argv)
        
        # 创建UI
        ui = Ui_MainWindow()
        
        # 测试FundInformation初始化
        fund_info = FundInformation(ui)
        print("FundInformation初始化成功")
        
        # 检查关键属性
        print(f"搜索历史路径: {fund_info.search_history_path}")
        print(f"收藏路径: {fund_info.collect_path}")
        print(f"浏览历史路径: {fund_info.viewed_history_path}")
        
        return True
    except Exception as e:
        print(f"FundInformation初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试资讯搜索功能...")
    print("=" * 50)
    
    results = []
    
    # 测试各个组件
    results.append(("热搜数据获取", test_search_data()))
    results.append(("搜索代码功能", test_search_code()))
    results.append(("搜索文章功能", test_search_article()))
    results.append(("FundInformation初始化", test_fund_information_init()))
    
    print("\n" + "=" * 50)
    print("测试结果总结:")
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    failed_tests = [name for name, result in results if not result]
    if failed_tests:
        print(f"\n失败的测试: {', '.join(failed_tests)}")
        print("建议检查网络连接和API接口状态")
    else:
        print("\n所有测试通过！")

if __name__ == "__main__":
    main()
