import json
import os
from functools import partial

from app.modules.FundHome2.api.get_news_list import get_news_list
from main_w import Ui_MainWindow
from PyQt6.QtCore import Qt, QStringListModel, QTimer, QUrl
import random
from PyQt6.QtGui import QColor, QPixmap, QImage
from PyQt6.QtWidgets import QHeaderView, QTableWidgetItem, QWidget, QHBoxLayout, QPushButton, QLineEdit, QMessageBox, \
    QTextEdit, QLabel, QButtonGroup
from PyQt6.QtNetwork import QNetworkAccessManager, QNetworkRequest, QNetworkReply
from app.common.api.code_name import FundCodeName
from .api.get_bkzj_data import get_bkzj_data
from .api.get_news_article import get_news_article
from app.common.api.network_to_img import NetworkToImg
from .api.get_zs_infos import get_zs_infos
from .src import style
from .DataHandle import DataHandle
from .src.style import default_style


class FundHome2:
    def __init__(self, ui: Ui_MainWindow):
        self.ui = ui
        self.init_setup()


    def init_setup(self):
        self.article_status=False
        self.ui.index_news_widget.hide()
        # 创建两个独立的按钮组
        self.zyzb_button_group = QButtonGroup()
        self.formula_button_group = QButtonGroup()
        self.ui.news_return.mousePressEvent=self.toggle_article_news_list
        self.ui.long_kx_lb.mousePressEvent=self.stretch_kx
        self.ui.short_kx_lb.mousePressEvent=self.shorten_kx
        self.ui.zs_comboBox_1.currentIndexChanged.connect(self.show_zs_zj_img)
        self.ui.zs_comboBox_2.currentIndexChanged.connect(self.show_zs_kx_img)

        for i in range(1, 6):
            radio_button = getattr(self.ui, f"zyzb_radio_{i}")
            radio_button.clicked.connect(partial(self.zyzb_radio_clicked, index=i-1))
            self.zyzb_button_group.addButton(radio_button, i)
            # formula单选按钮组
        for i in range(1, 10):  # 根据实际数量调整
            radio_button = getattr(self.ui, f"formula_radio_{i}")
            radio_button.clicked.connect(partial(self.formula_radio_clicked, index=i-1))
            self.formula_button_group.addButton(radio_button, i)
        self.unitWidth=-6

        # 各类型指数数据
        self.hs_name = ["上证指数","深成指数","创业指数","深证100","沪深300","上证50","B股指数","科创50指数"]
        self.hs_code = ["1.000001","0.399001","0.399006","0.399330","1.000300","1.000016","1.000003","1.000688"]
        self.gg_name = ["恒生指数","恒生科技指数","国企指数","红筹指数"]
        self.gg_code = ["100.HSI","124.HSTECH","100.HSCEI","124.HSCCI"]
        self.mg_name = ["纳斯达克","标普500","道琼斯"]
        self.mg_code = ["100.NDX","100.SPX","100.DJIA"]
        self.yg_name = ["英国富时250","英国富时350","英国富时全股","英国富时100","富时AIM全股"]
        self.yg_code = ["100.MCX","100.NMX","100.ASX","100.FTSE","100.AXX"]
        self.yz_name = ["越南胡志明","韩国KOSPI","日经225","富时新加坡","泰国SET"]
        self.yz_code = ["100.VNINDEX","100.KS11","100.N225","100.STI","100.SET"]

        #k线指标
        self.kx_zb_index_1=0
        self.kx_zb_index_2=0
        # 设置默认选中第一个
        self.ui.zyzb_radio_1.setChecked(True)
        self.ui.formula_radio_1.setChecked(True)


        # 类型名称列表
        self.type_names = ["沪深", "港股", "美股", "英股", "亚洲股"]

        # 当前选中的类型和指数
        self.current_type = 0  # 默认选中沪深
        self.selected_index = 0  # 默认选中第一个指数

        # 初始化扩展的指数选择功能
        self.init_extended_index_selection()

        # 初始化板块资金流选择功能
        self.init_bkzj_selection()

        #加载新闻
        self.load_news_list()

        # 初始化图表显示
        self.show_zs_zj_img(0)
        self.show_zs_kx_img(0)





    def toggle_article_news_list(self,event):
        if self.article_status:
            self.article_status=False
            self.ui.index_news_widget.hide()
            self.ui.home_news_listWidget.clearSelection()
        else:
            self.article_status=True
            self.ui.index_news_widget.show()



    def load_news_list(self):
        self.worker_thread = get_news_list()
        self.worker_thread.finished.connect(self.task_finished_news_list)
        self.worker_thread.start()

    def task_finished_news_list(self, news_url, news_time, news_media, news_title):
        self.news_url=news_url
        self.news_time=news_time
        self.news_media=news_media
        self.news_title=news_title
        self.ui.home_news_listWidget.clear()

        for i in range(len(news_url)):
            self.ui.home_news_listWidget.addItem(f"{self.news_title[i]}")

        self.ui.home_news_listWidget.itemClicked.connect(self.goto_news_article)

    def goto_news_article(self,item):
        title=item.text()
        index=self.news_title.index(title)
        self.ui.news_title.setText(title)
        self.ui.news_info.setText(f"{self.news_time[index]}       {self.news_media[index]}")
        self.load_news_article(self.news_url[index])
        self.article_status=True
        self.ui.index_news_widget.show()

    #文章内容获取
    def load_news_article(self,url):
        self.worker_thread_1 = get_news_article(url)
        self.worker_thread_1.finished.connect(self.task_finished_news_article)
        self.worker_thread_1.start()

    def task_finished_news_article(self, article_content):
        self.ui.plainTextEdit.setPlainText(article_content)

    def init_extended_index_selection(self):
        """初始化扩展的指数选择功能"""
        try:
            self.init_type_labels()
            self.init_index_labels()
            self.update_display_for_type(0)
        except Exception as e:
            print(f"初始化扩展指数选择功能失败: {e}")

    def init_type_labels(self):
        """初始化类型选择标签"""
        try:
            # 设置类型标签文本和点击事件
            for i in range(1, 6):
                label = getattr(self.ui, f"zs_top_type_{i}")
                if i <= len(self.type_names):
                    label.setText(self.type_names[i-1])
                    # 绑定点击事件
                    label.mousePressEvent = lambda event, idx=i-1: self.on_type_selected(idx, event)

            # 设置默认样式
            self.update_type_styles()

        except Exception as e:
            print(f"初始化类型标签失败: {e}")

    def init_index_labels(self):
        """初始化指数标签"""
        try:
            # 为所有指数标签绑定点击事件
            for i in range(1, 9):
                label = getattr(self.ui, f"hs_{i}")
                label.mousePressEvent = lambda event, idx=i-1: self.on_index_selected(idx, event)

        except Exception as e:
            print(f"初始化指数标签失败: {e}")

    def on_index_selected(self, index: int, event):
        """处理指数选择事件"""
        try:
            # 获取当前类型的指数数据
            current_names, current_codes = self.get_current_type_data()

            # 检查索引是否有效
            if index >= len(current_names):
                return

            self.selected_index = index
            self.update_index_styles()

            # 输出对应的指数代码
            selected_code = current_codes[index]
            selected_name = current_names[index]
            print(f"选中指数: {selected_name}, 代码: {selected_code}")

            self.show_zs_zj_img(self.ui.zs_comboBox_1.currentIndex())
            self.show_zs_kx_img(self.ui.zs_comboBox_2.currentIndex())


            # 调用DataHandle处理数据
            # DataHandle.handle_index_selection(selected_name, selected_code)

        except Exception as e:
            print(f"处理指数选择失败: {e}")

    def update_index_styles(self):
        """更新指数标签样式"""
        try:
            for i in range(1, 9):
                label = getattr(self.ui, f"hs_{i}")
                if i-1 == self.selected_index:
                    # 选中状态
                    label.setStyleSheet(style.zs_selected_yes)
                else:
                    # 未选中状态
                    label.setStyleSheet(style.zs_selected_no)

        except Exception as e:
            print(f"更新指数样式失败: {e}")

    def on_type_selected(self, type_index: int, event):
        """处理类型选择事件"""
        try:
            self.current_type = type_index
            self.selected_index = 0  # 重置为第一个指数

            # 更新显示
            self.update_display_for_type(type_index)
            self.show_zs_zj_img(self.ui.zs_comboBox_1.currentIndex())
            self.show_zs_kx_img(self.ui.zs_comboBox_2.currentIndex())



            print(f"选中类型: {self.type_names[type_index]}")

        except Exception as e:
            print(f"处理类型选择失败: {e}")

    def get_current_type_data(self):
        """获取当前类型的指数名称和代码"""
        type_data_map = {
            0: (self.hs_name, self.hs_code),  # 沪深
            1: (self.gg_name, self.gg_code),  # 港股
            2: (self.mg_name, self.mg_code),  # 美股
            3: (self.yg_name, self.yg_code),  # 英股
            4: (self.yz_name, self.yz_code),  # 亚洲股
        }
        return type_data_map.get(self.current_type, ([], []))

    def update_display_for_type(self, type_index: int):
        """根据类型更新显示"""
        try:
            # 更新类型样式
            self.update_type_styles()

            # 获取当前类型的数据
            current_names, current_codes = self.get_current_type_data()

            # 更新指数标签显示
            for i in range(1, 9):
                label = getattr(self.ui, f"hs_{i}")
                if i-1 < len(current_names):
                    # 显示对应的指数
                    label.setText(current_names[i-1])
                    label.show()
                else:
                    # 隐藏多余的标签
                    label.hide()

            # 更新指数样式
            self.update_index_styles()

        except Exception as e:
            print(f"更新类型显示失败: {e}")

    def update_type_styles(self):
        """更新类型标签样式"""
        try:
            for i in range(1, 6):
                label = getattr(self.ui, f"zs_top_type_{i}")
                if i-1 == self.current_type:
                    label.setStyleSheet(style.selected_style)
                else:
                    label.setStyleSheet(style.default_style)
        except Exception as e:
            print(f"更新类型样式失败: {e}")

    #沪深指数设置
    def show_zs_zj_img(self,index):
        # if self.current_type != 0:
        #     return

        self.network_manager = QNetworkAccessManager()
        self.network_img_loader = NetworkToImg(self.network_manager, self.ui)

        # 获取当前选中的指数代码
        current_names, current_codes = self.get_current_type_data()
        if self.selected_index < len(current_codes):
            img_url = DataHandle.return_img_url(current_codes[self.selected_index], index)
            print(img_url)
            # 使用封装的类加载图片
            self.load_zs_infos(current_codes[self.selected_index])
            self.network_img_loader.load_image(img_url,"index_hs_png_1")

    def show_zs_kx_img(self,index):#k线
        # 只有沪深指数才显示图表
        # if self.current_type != 0:
        #     return

        self.network_manager_1 = QNetworkAccessManager()
        self.network_img_loader_1 = NetworkToImg(self.network_manager_1, self.ui)

        # 获取当前选中的指数代码
        current_names, current_codes = self.get_current_type_data()
        if self.selected_index < len(current_codes):
            img_url = DataHandle.return_img_url_2(current_codes[self.selected_index], index, self.unitWidth,self.kx_zb_index_1,self.kx_zb_index_2)
            print(img_url)
            # 使用封装的类加载图片
            self.network_img_loader.load_image(img_url,"index_hs_png_2")

    def stretch_kx(self,event):
        if self.unitWidth<0:
            self.unitWidth+=1
            self.show_zs_kx_img( self.ui.zs_comboBox_2.currentIndex())

    def shorten_kx(self,event):
        if self.unitWidth>-8:
            self.unitWidth-=1
            self.show_zs_kx_img( self.ui.zs_comboBox_2.currentIndex())

    def zyzb_radio_clicked(self,checked, index):
        if checked:
            print(f"当前选中的单选按钮索引: {index}")
            self.kx_zb_index_1=index
            # 执行对应功能
            self.show_zs_kx_img(self.ui.zs_comboBox_2.currentIndex())

    def formula_radio_clicked(self,checked, index):
        if checked:
            print(f"当前选中的单选按钮索引: {index}")
            self.kx_zb_index_2 = index
            self.show_zs_kx_img(self.ui.zs_comboBox_2.currentIndex())

    #指数对应数据
    def load_zs_infos(self,code):
        self.worker_thread_1 = get_zs_infos(code)
        self.worker_thread_1.finished.connect(self.task_finished_zs_infos)
        self.worker_thread_1.start()

    def task_finished_zs_infos(self,data):

        # 今开，昨收，最高，最低，涨跌幅，涨跌额，换手，振幅
        # print(data)
        for i in range(1,len(data)+1):
            if data[i-1]!="":
                getattr(self.ui,f"zs_info_{i}").setText(data[i-1])
        l=DataHandle.return_zs_info_color(data[0],[data[2],data[3],data[4],data[5]])
        k=["zs_info_3","zs_info_4","zs_info_8","zs_info_6"]
        for i in range(len(k)):
            self.set_infos_color(l[i],k[i])

    def set_infos_color(self,value,label_name):
        match value:
            case 1:
                getattr(self.ui,label_name).setStyleSheet(style.QLabel_red)
            case 0:
                getattr(self.ui, label_name).setStyleSheet(style.QLabel_black)
            case -1:
                getattr(self.ui, label_name).setStyleSheet(style.QLabel_green)


    def init_bkzj_selection(self):
        """初始化板块资金流选择功能"""
        try:
            # 板块资金流类型名称
            self.bkzj_type_names = ["地域资金流", "行业资金流", "板块资金流"]
            self.bkzj_time_names = ["今日排行", "5日排行", "10日排行"]

            # 当前选中的类型和时间
            self.current_bkzj_type = 0  # 默认选中地域资金流
            self.current_bkzj_time = 0  # 默认选中今日排行

            # 初始化类型标签
            self.init_bkzj_type_labels()

            # 初始化时间RadioButton
            self.init_bkzj_time_buttons()

            # 加载初始数据
            self.load_bkzj_data(self.current_bkzj_type, self.current_bkzj_time)

        except Exception as e:
            print(f"初始化板块资金流选择功能失败: {e}")

    def init_bkzj_type_labels(self):
        """初始化板块资金流类型标签"""
        try:
            # 设置类型标签文本和点击事件
            for i in range(1, 4):
                label = getattr(self.ui, f"bkzj_type_{i}")
                if i <= len(self.bkzj_type_names):
                    label.setText(self.bkzj_type_names[i-1])
                    # 绑定点击事件
                    label.mousePressEvent = lambda event, idx=i-1: self.on_bkzj_type_selected(idx, event)

            # 设置默认样式
            self.update_bkzj_type_styles()

        except Exception as e:
            print(f"初始化板块资金流类型标签失败: {e}")

    def init_bkzj_time_buttons(self):
        """初始化板块资金流时间RadioButton"""
        try:
            # 创建QButtonGroup确保RadioButton互斥
            self.bkzj_time_group = QButtonGroup()

            # 设置RadioButton文本和添加到ButtonGroup
            for i in range(1, 4):
                radio_button = getattr(self.ui, f"bkzj_time_{i}")
                if i <= len(self.bkzj_time_names):
                    radio_button.setText(self.bkzj_time_names[i-1])
                    self.bkzj_time_group.addButton(radio_button, i-1)  # 添加到组，ID从0开始

            # 连接信号
            self.bkzj_time_group.buttonClicked.connect(self.on_bkzj_time_selected)

            # 默认选中第一个
            self.ui.bkzj_time_1.setChecked(True)

        except Exception as e:
            print(f"初始化板块资金流时间按钮失败: {e}")

    def on_bkzj_type_selected(self, type_index: int, event):
        """处理板块资金流类型选择事件"""
        try:
            self.current_bkzj_type = type_index
            self.update_bkzj_type_styles()
            self.load_bkzj_data( self.current_bkzj_type ,self.current_bkzj_time )

            print(f"选中板块资金流类型索引: {type_index}")

        except Exception as e:
            print(f"处理板块资金流类型选择失败: {e}")

    def on_bkzj_time_selected(self, button):
        """处理板块资金流时间选择事件"""
        try:
            time_index = self.bkzj_time_group.id(button)
            self.current_bkzj_time = time_index
            self.load_bkzj_data(self.current_bkzj_type, self.current_bkzj_time)

            print(f"选中板块资金流时间索引: {time_index}")

        except Exception as e:
            print(f"处理板块资金流时间选择失败: {e}")

    def update_bkzj_type_styles(self):
        """更新板块资金流类型标签样式"""
        try:
            # 更新所有类型标签样式
            for i in range(1, 4):
                label = getattr(self.ui, f"bkzj_type_{i}")
                if i-1 == self.current_bkzj_type:
                    label.setStyleSheet(style.selected_style)
                else:
                    label.setStyleSheet(default_style)

        except Exception as e:
            print(f"更新板块资金流类型样式失败: {e}")

    #板块资金流向
    def load_bkzj_data(self,type_index,key_index):
        """加载板块资金流数据"""
        self.worker_thread_2 = get_bkzj_data(type_index,key_index)
        self.worker_thread_2.finished.connect(self.task_finished_bkzj_data)
        self.worker_thread_2.start()

    def load_chart(self,):
        """加载图表HTML模板"""
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # html_path = os.path.join(current_dir, r'\src\bar_chart_template.html')
        html_path = r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\modules\FundHome2\src\bar_chart_template.html"
        # result=
        if os.path.exists(html_path):
            self.ui.bkzj_web.setUrl(QUrl.fromLocalFile(html_path))
            self.ui.bkzj_web.loadFinished.connect(self.on_load_finished)
        else:
            print(f"HTML模板文件不存在: {html_path}")

    def on_load_finished(self,):
        """页面加载完成后注入数据"""
        data_json = json.dumps(DataHandle.trans_bkzj_data(self.bk_name_,self.bk_value_), ensure_ascii=False)
        js_code = f'updateChart({data_json});'
        self.ui.bkzj_web.page().runJavaScript(js_code,)

    def task_finished_bkzj_data(self,bk_name, bk_value ):
        self.bk_name_=bk_name
        self.bk_value_=bk_value
        self.load_chart()
        print(bk_name[:10])
        print("渲染完成")














