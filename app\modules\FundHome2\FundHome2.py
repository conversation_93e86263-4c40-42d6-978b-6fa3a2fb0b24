from app.modules.FundHome2.api.get_news_list import get_news_list
from main_w import Ui_MainWindow
from PyQt6.QtCore import Qt, QStringListModel, QTimer, QUrl
import random
from PyQt6.QtGui import QColor, QPixmap, QImage
from PyQt6.QtWidgets import QHeaderView, QTableWidgetItem, QWidget, QHBoxLayout, QPushButton, QLineEdit, QMessageBox, \
    QTextEdit, QLabel
from PyQt6.QtNetwork import QNetworkAccessManager, QNetworkRequest, QNetworkReply
from app.common.api.code_name import FundCodeName
from .api.get_news_article import get_news_article
from app.common.api.network_to_img import NetworkToImg
from .src import style
from .DataHandle import DataHandle

class FundHome2:
    def __init__(self, ui: Ui_MainWindow):
        self.ui = ui
        self.init_setup()


    def init_setup(self):
        self.article_status=False
        self.ui.index_news_widget.hide()
        self.ui.news_return.mousePressEvent=self.toggle_article_news_list
        self.ui.long_kx_lb.mousePressEvent=self.stretch_kx
        self.ui.short_kx_lb.mousePressEvent=self.shorten_kx
        self.ui.zs_comboBox_1.currentIndexChanged.connect(self.show_zs_zj_img)
        self.ui.zs_comboBox_2.currentIndexChanged.connect(self.show_zs_kx_img)
        self.unitWidth=-6

        # 指数名称和代码列表（与UI中的标签文本保持一致）
        self.index_names = ["上证指数","深成指数","创业指数","深证100","沪深300","上证50","B股指数","科创50指数"]
        self.index_codes = ["000001","399001","399006","399330","000300","000016","000003","000688"]

        # 初始化指数选择功能
        self.init_index_selection()

        #加载新闻
        # self.load_news_list()

        self.show_zs_zj_img(0)
        self.show_zs_kx_img(0)


    def toggle_article_news_list(self,event):
        if self.article_status:
            self.article_status=False
            self.ui.index_news_widget.hide()
            self.ui.home_news_listWidget.clearSelection()
        else:
            self.article_status=True
            self.ui.index_news_widget.show()



    def load_news_list(self):
        self.worker_thread = get_news_list()
        self.worker_thread.finished.connect(self.task_finished_news_list)
        self.worker_thread.start()

    def task_finished_news_list(self, news_url, news_time, news_media, news_title):
        self.news_url=news_url
        self.news_time=news_time
        self.news_media=news_media
        self.news_title=news_title
        self.ui.home_news_listWidget.clear()

        for i in range(len(news_url)):
            self.ui.home_news_listWidget.addItem(f"{self.news_title[i]}")

        self.ui.home_news_listWidget.itemClicked.connect(self.goto_news_article)

    def goto_news_article(self,item):
        title=item.text()
        index=self.news_title.index(title)
        self.ui.news_title.setText(title)
        self.ui.news_info.setText(f"{self.news_time[index]}       {self.news_media[index]}")
        self.load_news_article(self.news_url[index])
        self.article_status=True
        self.ui.index_news_widget.show()


    #文章内容获取
    def load_news_article(self,url):
        self.worker_thread_1 = get_news_article(url)
        self.worker_thread_1.finished.connect(self.task_finished_news_article)
        self.worker_thread_1.start()

    def task_finished_news_article(self, article_content):
        self.ui.plainTextEdit.setPlainText(article_content)

    def init_index_selection(self):
        """初始化指数选择功能"""
        try:
            # 设置指数名称并绑定点击事件
            for i in range(1, 9):
                label = getattr(self.ui, f"hs_{i}")
                if i <= len(self.index_names):
                    # 更新标签文本为指定的指数名称
                    label.setText(self.index_names[i-1])
                    # 绑定点击事件
                    label.mousePressEvent = lambda event, idx=i-1: self.on_index_selected(idx, event)

            # 默认选中第一个指数
            self.selected_index = 0
            self.update_index_styles()

        except Exception as e:
            print(f"初始化指数选择功能失败: {e}")

    def on_index_selected(self, index: int, event):
        """处理指数选择事件"""
        try:
            self.selected_index = index
            self.update_index_styles()

            # 输出对应的指数代码
            selected_code = self.index_codes[index]
            selected_name = self.index_names[index]
            print(f"选中指数: {selected_name}, 代码: {selected_code}")
            self.show_zs_zj_img( self.ui.zs_comboBox_1.currentIndex())
            self.show_zs_kx_img( self.ui.zs_comboBox_2.currentIndex())

            # 调用DataHandle处理数据
            DataHandle.handle_index_selection(selected_name, selected_code)

        except Exception as e:
            print(f"处理指数选择失败: {e}")

    def update_index_styles(self):
        """更新指数标签样式"""
        try:
            for i in range(1, 9):
                label = getattr(self.ui, f"hs_{i}")
                if i-1 == self.selected_index:
                    # 选中状态
                    label.setStyleSheet(style.zs_selected_yes)
                else:
                    # 未选中状态
                    label.setStyleSheet(style.zs_selected_no)

        except Exception as e:
            print(f"更新指数样式失败: {e}")

    #沪深指数设置
    def show_zs_zj_img(self,index):
        self.network_manager = QNetworkAccessManager()
        self.network_img_loader = NetworkToImg(self.network_manager, self.ui)
        img_url=DataHandle.return_img_url(self.index_codes[self.selected_index],index)
        print(img_url)

        # 使用封装的类加载图片
        self.network_img_loader.load_image(img_url,"index_hs_png_1")

    def show_zs_kx_img(self,index):#k线
        self.network_manager_1 = QNetworkAccessManager()
        self.network_img_loader_1 = NetworkToImg(self.network_manager_1, self.ui)
        img_url=DataHandle.return_img_url_2(self.index_codes[self.selected_index],index,self.unitWidth)
        print(img_url)


        # 使用封装的类加载图片
        self.network_img_loader.load_image(img_url,"index_hs_png_2")

    def stretch_kx(self,event):
        if self.unitWidth<0:
            self.unitWidth+=1
            self.show_zs_kx_img( self.ui.zs_comboBox_2.currentIndex())

    def shorten_kx(self,event):
        if self.unitWidth>-8:
            self.unitWidth-=1
            self.show_zs_kx_img( self.ui.zs_comboBox_2.currentIndex())









