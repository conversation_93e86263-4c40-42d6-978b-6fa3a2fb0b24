def init_left_zfb_widget_status(self):
    self.left_bang_index = 1  # 左边行情排行榜left_label索引
    self.left_hy_status = 1  # 默认左边行情排行榜箭头向上
    self.left_table = ["", "行业涨幅榜", "行业跌幅榜", "概念涨幅榜", "概念跌幅榜", "地域涨幅榜", "地域跌幅榜"]

def change_l_l_bang(self,event):
    if self.left_bang_index in [3,4]:
        self.l_l_zfb.setStyleSheet("color:#CDCDCD")
        self.left_bang_index -= 2
    elif 2 < self.left_bang_index < 7:
        self.l_l_zfb.setStyleSheet("""QLabel{color:black}QLabel:hover { color: #FF4400; }""")
        self.l_r_zfb.setStyleSheet("""QLabel{color:black}QLabel:hover { color: #FF4400; }""")
        self.left_bang_index-=2
    self.l_center_lb.setText(self.left_table[self.left_bang_index])

def change_l_r_bang(self,event):
    if self.left_bang_index in [3,4]:
        self.l_r_zfb.setStyleSheet("color:#CDCDCD")
        self.left_bang_index += 2
    elif 0 < self.left_bang_index < 5:
        self.l_l_zfb.setStyleSheet("""QLabel{color:black}QLabel:hover { color: #FF4400; }""")
        self.l_r_zfb.setStyleSheet("""QLabel{color:black}QLabel:hover { color: #FF4400; }""")
        self.left_bang_index += 2
    self.l_center_lb.setText(self.left_table[self.left_bang_index])
