import requests
from bs4 import BeautifulSoup
from PyQt6.QtCore import QThread, pyqtSignal

class get_other_article(QThread):
    finished = pyqtSignal(list)
    def __init__(self, type, url_page):
        super().__init__()
        self.time_, self.href_, self.title_ = [], [], []
        self.header = {
          "Host": "fund.eastmoney.com",
          "Connection": "keep-alive",
          "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Microsoft Edge\";v=\"139\", \"Chromium\";v=\"139\"",
          "sec-ch-ua-mobile": "?0",
          "sec-ch-ua-platform": "\"Windows\"",
          "Upgrade-Insecure-Requests": "1",
          "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
          "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
          "Sec-Fetch-Site": "same-origin",
          "Sec-Fetch-Mode": "navigate",
          "Sec-Fetch-User": "?1",
          "Sec-Fetch-Dest": "document",
          "Referer": "https://fund.eastmoney.com/a/csmjj_2.html",
          "Accept-Encoding": "gzip, deflate, br, zstd",
          "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        }
        match (type):
            case "jjgd":
                self.base_url = "https://fund.eastmoney.com/a/cjjgd_{}.html".format(url_page)
            case "jjxx":
                self.base_url = "https://fund.eastmoney.com/a/cjjxx_{}.html".format(url_page)
            case "jjyw":
                self.base_url = "https://fund.eastmoney.com/a/cjjyw_{}.html".format(url_page)
            case "tzcl":
                self.base_url = "https://fund.eastmoney.com/a/cjjtzcl_{}.html".format(url_page)
            case "smzx":
                self.base_url = "https://fund.eastmoney.com/a/csmjj_{}.html".format(url_page)

    def request_data(self):
        try:
            data = requests.get(self.base_url).text
            soup = BeautifulSoup(data, "html.parser")
            infos_div = soup.find("div", class_="infos")
            page_div = soup.find("div", class_="Page")
            max_page = 1
            
            if infos_div:
                li_items = infos_div.find_all("li")
                for li in li_items:
                    time = li.find("span").text.strip()
                    a_tag = li.find("a")
                    href = "https://fund.eastmoney.com/a/" + a_tag["href"]
                    title = a_tag["title"]
                    self.time_.append(time)
                    self.href_.append(href)
                    self.title_.append(title)
                    
            if page_div:
                a_tags = page_div.find_all("a")
                page_numbers = []
                for a in a_tags:
                    text = a.text.strip()
                    if text.isdigit():
                        page_numbers.append(int(text))
                if page_numbers:
                    max_page = max(page_numbers)
                    
            return [self.time_, self.href_, self.title_, max_page]
        except Exception as e:
            print(f"请求数据失败: {e}")
            return [[], [], [], 1]

    def run(self):
        result = self.request_data()
        self.finished.emit(result)
