import asyncio
import time
from datetime import datetime
from functools import partial

from PyQt6.QtCore import Qt, QDate, QUrl, QEvent, QEasingCurve, QPropertyAnimation
from PyQt6.QtGui import QPixmap, QDesktopServices
from PyQt6.QtWidgets import QTableWidgetItem, QMessageBox, QListWidgetItem, QWidget, QLineEdit, QApplication, \
    QGraphicsDropShadowEffect

from app.modules.FundInformation.FundSearch import FundSearch
from app.modules.FundInformation.api.get_m_infos import url_infos
from app.modules.FundInformation.api.get_m_infos_1 import url_infos_1
from app.modules.FundInformation.api.get_other_article import get_other_article
from app.modules.FundInformation.api.get_search_article import get_search_title
from app.modules.FundInformation.api.get_search_code import get_search_c
from app.modules.FundInformation.api.get_search_data import search_data
from app.modules.FundInformation.api.get_title_data import get_title
from main_w import Ui_MainWindow


class FundInformation():
    def __init__(self, ui:Ui_MainWindow):
        self.ui = ui
        self.init_setup()

    def init_setup(self):
        self.img_path= r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\img"
        self.collect_path= r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\modules\FundInformation\src\collect"
        self.search_history_path= r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\modules\FundInformation\src\search_history"
        self.viewed_history_path= r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\modules\FundInformation\src\view_history"

        self.info_detail_status=False
        self.info_detail_status_2=False
        self.info_detail_status_3=False

        self.info_history_status=False
        self.info_collect_status=False
        self.info_search_status=False
        self.hot_start = 0
        self.search_widget_status=False
        self.ui.infomation_widget.hide()
        self.ui.info_search_detail_widget.hide()
        # 滚动区域的内容 Widget
        self.load_new_n = 0
        self.news_list = []
        self.info_index_start=0
        # 初始化动画
        self.ui.info_up_img.mousePressEvent=self.smooth_scroll_to_top
        self.ui.info_up_lb.mousePressEvent=self.smooth_scroll_to_top
        self.animation = QPropertyAnimation(self.ui.scrollArea_3.verticalScrollBar(), b"value")
        self.animation.setEasingCurve(QEasingCurve.Type.OutCubic)  # 设置缓动曲线
        self.animation.setDuration(500)  # 设置动画时长（毫秒）
        self.animation1 = QPropertyAnimation(self.ui.scrollArea_4.verticalScrollBar(), b"value")
        self.animation1.setEasingCurve(QEasingCurve.Type.OutCubic)  # 设置缓动曲线
        self.animation1.setDuration(500)  # 设置动画时长（毫秒）

        #文章详情
        self.ui.info_detail_widget.hide()
        # self.info_detail_return_lb.mousePressEvent=
        self.ui.info_detail_return_lb.mousePressEvent=partial(self.toggle_info_detail,index=-1)
        # self.info_collect_lb.setPixmap(QPixmap(self.img_path + r'\sc_no.png').scaled(30, 30))

        self.ui.info_collect_lb_2.setPixmap(QPixmap(self.img_path + r'\copy_link_1.png').scaled(32, 32))
        self.ui.info_collect_lb_3.setPixmap(QPixmap(self.img_path + r'\web3.png').scaled(32, 32))
        self.ui.info_other_collect_lb_1.setPixmap(QPixmap(self.img_path + r'\web3.png').scaled(32, 32))
        self.ui.info_other_collect_lb_2.setPixmap(QPixmap(self.img_path + r'\copy_link_1.png').scaled(32, 32))
        self.ui.info_search_collect_lb_1.setPixmap(QPixmap(self.img_path + r'\web3.png').scaled(32, 32))
        self.ui.info_search_collect_lb_2.setPixmap(QPixmap(self.img_path + r'\copy_link_1.png').scaled(32, 32))
        self.ui.info_history_widget.hide()
        self.ui.info_collect_widget.hide()
        self.ui.info_search_widget.hide()
        self.ui.info_viewed_list.mousePressEvent=self.toggle_info_history_list
        self.ui.info_collect_list.mousePressEvent=self.toggle_info_collect_list

        # self.infomation_widget.mousePressEvent=self.hide_search_widget
        # QApplication.instance().installEventFilter(self)
        self.ui.label_99.setPixmap(QPixmap(self.img_path + r'\fire.png').scaled(25, 25))
        self.ui.label_100.setPixmap(QPixmap(self.img_path + r'\zs.png').scaled(25, 25))
        self.ui.info_search_img_1.setPixmap(QPixmap(self.img_path + r'\link.png').scaled(25, 25))
        self.ui.info_search_img_2.setPixmap(QPixmap(self.img_path + r'\sq.png').scaled(30, 30))
        self.ui.label_136.setPixmap(QPixmap(self.img_path + r'\history-info.png').scaled(25, 25))
        self.ui.label_136.setPixmap(QPixmap(self.img_path + r'\history-info.png').scaled(25, 25))
        self.ui.label_143.setPixmap(QPixmap(self.img_path + r'\delete_info.png').scaled(25, 25))
        self.ui.label_143.mousePressEvent =self.clear_history
        self.ui.info_search_widget.setGraphicsEffect(self.createShadowEffect())
        self.collect_img = QPixmap(self.img_path + r'\sc.png').scaled(32, 32)
        self.no_collect_img = QPixmap(self.img_path + r'\sc_no.png').scaled(32, 32)
        self.ui.info_detail_return_lb_2.mousePressEvent =self.hide_info_detail
        self.ui.info_detail_return_lb_3.mousePressEvent =self.hide_info_detail_his#从历史记录返回
        # self.top_transaction.mousePressEvent =self.show_info_other
        self.ui.info_other_widget.hide()
        self.ui.info_other_add_page.mousePressEvent=self.add_page_info_other
        self.ui.info_other_sub_page.mousePressEvent=self.sub_page_info_other
        self.ui.info_other_first_page.mousePressEvent=self.first_page_info_other
        self.ui.info_other_tail_page.mousePressEvent=self.tail_page_info_other
        self.info_current_page = 1
        # self.info_other_widget_index=0
        self.ui.jj_info_lb.mousePressEvent=self.open_infomation_widget
        self.ui.jjgd_lb.mousePressEvent=partial(self.change_info_other_index,index=0)
        self.ui.jjxx_lb.mousePressEvent=partial(self.change_info_other_index,index=1)
        self.ui.jjyw_lb.mousePressEvent=partial(self.change_info_other_index,index=2)
        self.ui.tzcl_lb.mousePressEvent=partial(self.change_info_other_index,index=3)
        self.ui.smzx_lb.mousePressEvent=partial(self.change_info_other_index,index=4)

        """搜索"""
        self.ui.info_search_textEdit.textChanged.connect(self.get_search_c_result)
        self.ui.info_search_img_2.mousePressEvent=self.close_search_widget
        self.ui.info_search_return_lb.mousePressEvent=self.return_info_
        self.ui.update_hot.mousePressEvent=self.toggle_hot_start
        self.ui.info_search_textEdit.mousePressEvent = self.info_mouse_press_event
        self.ui.info_search_btn.mousePressEvent=self.search_info
        self.ui.info_search_btn_2.mousePressEvent=self.search_info_2


    "基金搜索"
    def return_info_(self, event):
        if self.info_search_status == True:
            for i in range(1, 11):
                getattr(self.ui, f"info_search_content_{i}").setHtml("")
                getattr(self.ui, f"info_search_title_{i}").setHtml("")
                getattr(self.ui, f"info_search_url_{i}").setText("")
            self.ui.info_search_detail_tip_lb.setText("")
            self.ui.info_search_detail_title_lb.setText("")
            self.ui.info_detail_webBrowser_2.setHtml("")
            self.ui.info_search_detail_widget.hide()
            self.info_search_status = False
            self.load_info_search_listWidget()


    def close_search_widget(self, event):
        self.ui.info_search_widget.hide()


    def get_search_c_result(self):
        try:
            if self.ui.info_search_textEdit.text() == "":
                self.ui.info_search_c_widget.hide()
            else:
                self.ui.info_search_c_widget.show()
                self.ui.info_search_listWidget.clear()
                keyword = self.ui.info_search_textEdit.text()
                # print(keyword)
                self.stock_list, self.stock_name = get_search_c.return_code_l(keyword=keyword)
                for i in self.stock_list:
                    self.ui.info_search_listWidget.addItem(i)
                self.ui.show_all_search.setText(f"查看全部 {keyword} 的搜索结果")
        except Exception as e:
            print(e)


    def search_info(self, event):
        if self.ui.info_search_textEdit.text() != "":
            self.search_scroll_count = 0
            self.info_search_keyword = self.ui.info_search_textEdit.text()
            self.ui.info_search_textEdit_2.setText(self.info_search_keyword)
            self.info_search_page = 1
            self.ui.scrollArea_4.verticalScrollBar().valueChanged.connect(self.on_search_scroll)
            self.info_search_status = True
            try:
                self.update_info_search_data(self.info_search_keyword)  # 记录搜索记录
                self.load_info_search_title(self.info_search_keyword, self.info_search_page)
            except Exception as e:
                print(e)
        else:
            QMessageBox.information(self.ui.infomation_widget, "搜索失败", "搜索内容不能为空！")


    def search_info_keyword(self, event, keyword):
        try:
            self.search_scroll_count = 0
            self.info_search_keyword = keyword
            self.ui.info_search_textEdit_2.setText(keyword)
            self.info_search_page = 1
            self.ui.scrollArea_4.verticalScrollBar().valueChanged.connect(self.on_search_scroll)
            self.info_search_status = True

            self.update_info_search_data(self.info_search_keyword)  # 记录搜索记录
            print("kkk")
            self.load_info_search_title(self.info_search_keyword, self.info_search_page)
            print("ppp")
        except Exception as e:
            print(e)


    def search_info_2(self, event):
        if self.ui.info_search_textEdit != "":
            self.search_scroll_count = 0
            self.info_search_keyword = self.ui.info_search_textEdit_2.text()
            self.info_search_page = 1
            self.ui.scrollArea_4.verticalScrollBar().valueChanged.connect(self.on_search_scroll)
            self.info_search_status = True
            try:
                self.update_info_search_data(self.info_search_keyword)  # 记录搜索记录
                self.load_info_search_title(self.info_search_keyword, self.info_search_page)
            except Exception as e:
                print(e)
        else:
            QMessageBox.information(self.ui.infomation_widget, "搜索失败", "搜索内容不能为空！")


    def load_info_search_title(self, keyword, page):
        try:

            self.info_s_time = time.time()
            self.ui.info_search_detail_widget.show()
            self.ui.info_search_load_lb.setText("搜索中...")
            self.worker_thread_info_search = get_search_title(keyword=keyword, page=page)
            self.worker_thread_info_search.finished.connect(self.task_finished_info_search)
            self.worker_thread_info_search.start()
        except Exception as e:
            print(f" load_info_search_title error:{e}")


    def task_finished_info_search(self, search_data_list):
        self.info_search_date = search_data_list[0]
        self.info_search_url = search_data_list[1]
        self.info_search_title = search_data_list[2]
        self.info_search_title_or = search_data_list[-1]
        self.info_search_content = search_data_list[3]
        self.info_search_source = search_data_list[4]

        self.info_e_time = time.time()
        # 遍历显示
        self.load_search_()
        self.load_info_search_detail(self.info_search_title_or[0], self.info_search_url[0])
        for i in range(1, 11):
            getattr(self.ui, f"info_search_url_{i}").mousePressEvent = partial(self.on_click_search_index, index=i - 1)
        # 更新列表对应点击，收藏列表，浏览历史
        # self.update_info_other_list()
        self.ui.info_search_load_lb.setText("本次搜索共耗时:{:.2f}s".format(self.info_e_time - self.info_s_time))


    def on_click_search_index(self, event, index):
        url = self.info_search_url
        title = self.info_search_title_or
        self.load_info_search_detail(title[index], url[index])
        self.ui.info_search_collect_lb_1.mousePressEvent = partial(self.open_url, url=url[index])
        self.ui.info_search_collect_lb_2.mousePressEvent = partial(self.copy_to_clipboard, url=url[index])


    def load_info_search_detail(self, title, url):
        a = []
        a.append(url)
        if "fund" in url:
            article_info = asyncio.run(url_infos_1(a).main())[0]
        else:
            article_info = asyncio.run(url_infos(a).main())[0]
        article_tip = "发布时间： " + article_info[0] + "  来源： " + article_info[1]
        article_content = article_info[2]
        self.ui.info_search_detail_tip_lb.setText(article_tip)
        self.ui.info_search_detail_title_lb.setText(title)
        self.ui.info_detail_webBrowser_2.setHtml(article_content)
        self.check_collect(title=title, url=url)
        self.update_view_history(title=title, url=url)


    def load_search_(self):
        for i in range(1, 11):
            getattr(self.ui, f"info_search_content_{i}").setHtml(self.info_search_content[i - 1])
            getattr(self.ui, f"info_search_title_{i}").setHtml(self.info_search_title[i - 1])
            getattr(self.ui, f"info_search_url_{i}").setText(self.info_search_url[i - 1])


    def on_search_scroll(self):
        scroll_bar = self.ui.scrollArea_4.verticalScrollBar()
        if self.search_scroll_count == 50:
            self.ui.info_search_load_lb.setText(f"已加载{self.info_search_keyword}全部搜索结果")
        if scroll_bar.value() == scroll_bar.maximum() and self.search_scroll_count < 49:  # 滚动到底部
            self.info_search_page += 1
            self.search_scroll_count += 1
            self.load_info_search_title(self.info_search_keyword, self.info_search_page)
            self.smooth_scroll_to_top(event=None)


    def toggle_hot_start(self, event):
        if self.hot_start == 0:
            self.hot_start += 10
            self.load_search_data()
        else:
            self.hot_start = 0
            self.load_search_data()


    def load_search_data(self):
        try:
            self.ui.hot_listWidget_1.clear()
            self.ui.hot_listWidget_2.clear()
            self.ui.stock_listWidget_1.clear()
            self.ui.stock_listWidget_2.clear()
            self.hot_l, self.hot_or, self.stock_l, self.stock_or = search_data.return_search_init()

            for i in range(self.hot_start, self.hot_start + 10):
                if i < 5 or 9 < i < 15:
                    self.ui.hot_listWidget_1.addItem(self.hot_l[i])
                else:
                    self.ui.hot_listWidget_2.addItem(self.hot_l[i])
            for j in range(self.hot_start, self.hot_start + 10):
                if j < 5 or 9 < j < 15:
                    self.ui.stock_listWidget_1.addItem(self.stock_l[j])
                else:
                    self.ui.stock_listWidget_2.addItem(self.stock_l[j])
            self.ui.hot_listWidget_1.itemClicked.connect(self.bind_info_hot_search)
            self.ui.hot_listWidget_2.itemClicked.connect(self.bind_info_hot_search)
            self.ui.stock_listWidget_1.itemClicked.connect(self.bind_info_hot_stock_search)
            self.ui.stock_listWidget_2.itemClicked.connect(self.bind_info_hot_stock_search)
        except Exception as e:
            print(e)


    def show_search_widget(self):
        try:
            self.ui.info_search_widget.show()
            if len(self.ui.info_search_textEdit.text()) == 0:
                self.ui.info_search_c_widget.hide()
            self.load_search_data()
            self.load_info_search_listWidget()
            self.search_widget_status = True
            self.ui.info_search_listWidget.itemClicked.connect(self.bind_info_search_stock)
        except Exception as e:
            print(e)


    def load_info_search_listWidget(self):
        self.ui.info_se_listWidget.clear()
        with open(self.search_history_path, "r", encoding="utf8") as f:
            self.info_search_history = f.readlines()
        for i in self.info_search_history[::-1]:
            max_l = 10 if len(i) > 10 else len(i)
            if max_l < 10:
                self.ui.info_se_listWidget.addItem(i[:max_l].strip())
            else:
                self.ui.info_se_listWidget.addItem(i[:max_l].strip() + "...")
        self.ui.info_se_listWidget.itemClicked.connect(self.bind_info_search_data)


    def bind_info_search_data(self, item):
        try:

            self.info_search_history_list = []
            with open(self.search_history_path, "r", encoding="utf8") as f:
                self.info_search_history = f.readlines()
            for i in self.info_search_history:
                max_l = 10 if len(i) > 10 else len(i)
                if max_l < 10:
                    self.info_search_history_list.append(i[:max_l].strip())
                else:
                    self.info_search_history_list.append(i[:max_l].strip() + "...")
            self.info_search_item_index = self.info_search_history_list.index(item.text())
            print(self.info_search_item_index)
            # self.info_search_textEdit.setText(self.info_search_history[ self.info_search_item_index].strip())
            self.ui.info_search_textEdit_2.setText(self.info_search_history[self.info_search_item_index].strip())
            self.search_info_keyword(event=None, keyword=self.info_search_history[self.info_search_item_index].strip())
        except Exception as e:
            print(e)


    def bind_info_search_stock(self, item):
        try:
            self.info_search_stock_list = []
            print(self.stock_name)
            for i in self.stock_list:
                self.info_search_stock_list.append(i)
            print(self.info_search_stock_list)
            self.info_stock_item_index = self.info_search_stock_list.index(item.text())
            # self.info_search_textEdit.setText(self.stock_name[ self.info_stock_item_index])
            self.ui.info_search_textEdit_2.setText(self.stock_name[self.info_stock_item_index])
            self.search_info_keyword(event=None, keyword=self.stock_name[self.info_stock_item_index])
        except Exception as e:
            print(e)


    def bind_info_hot_search(self, item):
        try:

            self.info_hot_item_index = self.hot_l.index(item.text())
            print(888)
            self.ui.info_search_textEdit_2.setText(self.hot_l[self.info_hot_item_index])
            print("999")
            self.search_info_keyword(event=None, keyword=self.hot_or[self.info_hot_item_index])
            print("777")
        except Exception as e:
            print(f"bind_info_hot_search error:{e}")


    def bind_info_hot_stock_search(self, item):
        try:
            self.info_hot_stock_item_index = self.stock_l.index(item.text())
            self.ui.info_search_textEdit_2.setText(self.stock_l[self.info_hot_stock_item_index])
            self.search_info_keyword(event=None, keyword=self.stock_or[self.info_hot_stock_item_index])
        except Exception as e:
            print(e)


    def update_info_search_data(self, query):
        with open(self.search_history_path, "r", encoding="utf8") as f:
            view_history = f.readlines()
            f.close()
        data = query + "\n"
        if len(view_history) > 10:
            view_history.pop(0)
        if data not in set(view_history):
            view_history.append(data)
        else:
            view_history.remove(data)
            view_history.append(data)
        with open(self.search_history_path, "w", encoding="utf8") as f1:
            f1.writelines(view_history)
            f1.close()

    """资讯其他文章+页面"""

    def clear_history(self, event):
        self.ui.info_se_listWidget.clear()
        with open(self.search_history_path, "w",
                  encoding="utf8") as f:
            f.write("")

    def change_info_other_index(self,event,index):
        self.info_other_widget_index =index
        self.show_info_other()

    def isdisable_info_page(self, page_name, status):
        if status:
            getattr(self.ui, f"info_other_{page_name}_page").setStyleSheet("""
                             QLabel {
                            border: 1px solid gray; /* 边框宽度和颜色 */
                            border-radius: 10px;   /* 边框圆角 */
                            padding: 10px;        /* 内边距 */
                          color:   gray
                        }
                    """)
        else:
            getattr(self.ui, f"info_other_{page_name}_page").setStyleSheet("""
                                          QLabel {
                        border: 1px solid #5E548E; /* 边框宽度和颜色 */
                        border-radius: 10px;   /* 边框圆角 */
                        padding: 10px;        /* 内边距 */
                        color:   #5E548E}
                      QLabel:hover {
                     color: white; 
                    background-color:#5E548E}
                                    }
                                """)

    def load_info_title(self,type,page):
        self.s = time.time()
        self.ui.info_other_load_lb.setText("加载中...")
        # 创建并启动子线程
        self.worker_thread_info_other = get_other_article(type=type, url_page=page)
        if type=="tzcl":
            self.worker_thread_info_other.finished.connect(self.task_finished_info_other_tzcl)
        else:
            self.worker_thread_info_other.finished.connect(self.task_finished_info_other)
        self.worker_thread_info_other.start()

    def task_finished_info_other_tzcl(self,title_data_l):
        try:
            #传入1个页面的40条数据
            self.info_other_time = title_data_l[0]
            self.info_other_href = title_data_l[1]
            self.info_other_title = title_data_l[2]
            self.info_other_max_page = title_data_l[3]
            self.e = time.time()

            self.ui.info_other_tail_page.setText(str(self.info_other_max_page))
            self.load_info_other_detail(self.info_other_title[0],self.info_other_href[0])
            self.ui.info_detail_listWidget_2.itemClicked.connect(self.load_info_other_list_detail)
            self.update_info_other_list()
            self.update_page_info_other()
            self.ui.info_other_load_lb.setText("本次加载共耗时:{:.2f}s".format(self.e - self.s))
            #加载第一篇文章
        except Exception as e:
            print(e)

    def task_finished_info_other(self, title_data_l):
        try:
            #传入1个页面的40条数据
            self.info_other_time = title_data_l[0]
            self.info_other_href = title_data_l[1]
            self.info_other_title = title_data_l[2]
            self.info_other_max_page = title_data_l[3]
            self.e = time.time()
            self.ui.info_other_tail_page.setText(str(self.info_other_max_page))
            self.load_info_other_detail(self.info_other_title[0],self.info_other_href[0])
            self.ui.info_detail_listWidget_2.itemClicked.connect(self.load_info_other_list_detail)
            self.update_info_other_list()
            self.update_page_info_other()
            self.ui.info_other_load_lb.setText("本次加载共耗时:{:.2f}s".format(self.e - self.s))
            #加载第一篇文章
        except Exception as e:
            print(e)

    def sub_page_info_other(self,event):
        if self.info_current_page >1:
            self.info_current_page -= 1
            self.load_info_title(self.info_other_list[self.info_other_widget_index], self.info_current_page)
            self.update_page_info_other()

    def add_page_info_other(self,event):
        if self.info_current_page<self.info_other_max_page:
            self.info_current_page+=1
            self.load_info_title(self.info_other_list[self.info_other_widget_index],self.info_current_page)
            self.update_page_info_other()

    def first_page_info_other(self,event):
        self.info_current_page=1
        self.load_info_title(self.info_other_list[self.info_other_widget_index], self.info_current_page)
        self.update_page_info_other()

    def tail_page_info_other(self,event):
        self.info_current_page =self.info_other_max_page
        self.load_info_title(self.info_other_list[self.info_other_widget_index], self.info_current_page)
        self.update_page_info_other()
    def update_page_info_other(self,):
        if self.info_current_page==1 and self.info_other_max_page==1:
            self.isdisable_info_page("sub",True)
            self.isdisable_info_page("add",True)
        elif self.info_current_page==1 and self.info_current_page<self.info_other_max_page:
            self.isdisable_info_page("sub", True)
            self.isdisable_info_page("add", False)
        elif 1<self.info_current_page<self.info_other_max_page:
            self.isdisable_info_page("sub", False)
            self.isdisable_info_page("add", False)
        elif self.info_current_page==self.info_other_max_page:
            self.isdisable_info_page("sub", False)
            self.isdisable_info_page("add", True)
        self.ui.info_other_c_page.setText("第{}页".format(str( self.info_current_page)))

    def load_info_other_list_detail(self,item):
        #加载指定的数据
        try:
            self.info_other_title_list_1=[]
            for i in self.info_other_title_list:
                max_l = 27 if len(i) > 27 else len(i)
                if max_l < 27:
                    self.info_other_title_list_1.append(i[:max_l].strip())
                else:
                    self.info_other_title_list_1.append(i[:max_l].strip() + "...")
            self.info_other_item_index=self.info_other_title_list_1.index(item.text())
            article_title=self.info_other_title_list[ self.info_other_item_index]
            article_url=self.info_other_href_list[self.info_other_item_index]
            self.load_info_other_detail(article_title,article_url)
            self.ui.info_other_collect_lb_1.mousePressEvent = partial(self.open_url, url=article_url)
            self.ui.info_other_collect_lb_2.mousePressEvent = partial(self.copy_to_clipboard, url=article_url)
            self.update_detail_collect_list()
        except Exception as e:
            print(e)

    def load_info_other_detail(self,title,url):
        a=[]
        a.append(url)
        if "fund" in url:
            article_info = asyncio.run(url_infos_1(a).main())[0]
        else:
            article_info = asyncio.run(url_infos(a).main())[0]
        article_tip = "发布时间： " + article_info[0] + "  来源： " + article_info[1]
        article_content = article_info[2]
        self.ui.info_detail_tip_lb_2.setText(article_tip)
        self.ui.info_detail_title_lb_2.setText(title)
        self.ui.info_other_webView.setHtml(article_content)
        self.check_collect(title=title, url=url)

    def update_info_other_list(self,):
        self.ui.info_detail_listWidget_2.clear()
        start=0
        end=20
        self.info_other_title_list = self.info_other_title[start:end]
        self.info_other_href_list = self.info_other_href[start:end]
        for i in self.info_other_title_list:
            max_l = 27 if len(i) > 27 else len(i)
            if max_l < 27:
                self.ui.info_detail_listWidget_2.addItem(i[:max_l].strip())
            else:
                self.ui.info_detail_listWidget_2.addItem(i[:max_l].strip() + "...")
        for i in range(0, 20):
            getattr(self.ui, f"info_other_time_{i + 1}").setText(self.info_other_time[i])

    def show_info_other(self,):
        try:
            self.ui.info_other_widget.show()
            self.ui.info_detail_listWidget_2.clear()
            self.info_other_list=["jjgd","jjxx","jjyw","tzcl","smzx"]
            self.load_info_title(self.info_other_list[self.info_other_widget_index], 1)
            self.info_current_page=1
            #向listwidget添加内容
        except Exception as e:
            print(e)


    def open_url(self,event,url):
        url = QUrl(url)  # 指定网址
        QDesktopServices.openUrl(url)  # 打开浏览器

    def copy_to_clipboard(self,event,url):
        """复制文本到剪贴板并弹出消息提示框"""
        clipboard = QApplication.clipboard()  # 获取剪贴板
        clipboard.setText(url)  # 设置剪贴板内容
        QMessageBox.information(self.ui.infomation_widget, "复制成功", "文本已复制到剪贴板！")

    def createShadowEffect(self):
        shadow = QGraphicsDropShadowEffect(self.ui.infomation_widget)
        shadow.setBlurRadius(15)  # 阴影模糊半径
        shadow.setColor(Qt.GlobalColor.lightGray)  # 阴影颜色
        shadow.setOffset(3, 4)  # 阴影偏移量
        return shadow

    def eventFilter(self, obj, event):
        if event.type() == QEvent.Type.MouseButtonPress:  # 使用 QEvent.Type.MouseButtonPress
            if not self.ui.info_search_widget.geometry().contains(event.globalPosition().toPoint()):  # 使用 globalPosition()
                self.ui.info_search_widget.hide()  # 隐藏弹出窗口
        return super().eventFilter(obj, event)

    # def on_information_item_clicked(self,item):
    #     self.info_item_index=self.info_item_list_name.index(item.text())


    def toggle_collect_lb(self,current_url_collect_status):
        if current_url_collect_status:
            self.ui.info_collect_lb.setPixmap(self.collect_img)
            self.ui.info_other_collect_lb_3.setPixmap(self.collect_img)
            self.ui.info_search_collect_lb_3.setPixmap(self.collect_img)
        else:
            self.ui.info_collect_lb.setPixmap(self.no_collect_img)
            self.ui.info_other_collect_lb_3.setPixmap(self.no_collect_img)
            self.ui.info_search_collect_lb_3.setPixmap(self.no_collect_img)

    def update_collect(self,event,title,url):
        try:
            data = title + "|" + url + "\n"
            with open(self.collect_path, "r", encoding="utf8") as f:
                collect_ = f.readlines()
            if self.current_url_collect_status:#已收藏
                collect_url_index=collect_.index(data)
                collect_.pop(collect_url_index)
                with open(self.collect_path, "w", encoding="utf8") as f1:
                    f1.writelines(collect_)
                self.load_collect()
                self.toggle_collect_lb(False)
                self.current_url_collect_status = False
            else:
                with open(self.collect_path, "a+", encoding="utf8") as f2:
                    if data not in set(collect_):
                        f2.write(data)
                        self.toggle_collect_lb(True)
                        self.current_url_collect_status=True
                self.load_collect()
            # self.update_collect_list()
            # self.update_detail_collect_list()
        except Exception as e:
            print(e)

        # if data not in set(collect_):
        #     self.current_url_collect_status = False
        #     self.info_collect_lb.setPixmap(self.no_collect_img)
        # else:
        #     self.current_url_collect_status = True
        #     self.info_collect_lb.setPixmap(self.collect_img)


    def check_collect(self,title,url):
        with open(self.collect_path, "r", encoding="utf8") as f:
            collect_ = f.readlines()
        # with open(r"api/widget/information\collect", "a+", encoding="utf8") as f:
            data = title + "|" + url + "\n"
        if data not in set(collect_):
            self.current_url_collect_status = False
            self.ui.info_collect_lb.setPixmap(self.no_collect_img)
            self.ui.info_other_collect_lb_3.setPixmap(self.no_collect_img)
            self.ui.info_search_collect_lb_3.setPixmap(self.no_collect_img)
        else:
            self.current_url_collect_status = True
            self.ui.info_collect_lb.setPixmap(self.collect_img)
            self.ui.info_other_collect_lb_3.setPixmap(self.collect_img)
            self.ui.info_search_collect_lb_3.setPixmap(self.collect_img)
        self.ui.info_collect_lb.mousePressEvent = partial(self.update_collect, title=title,
                                                       url=url)
        self.ui.info_other_collect_lb_3.mousePressEvent = partial(self.update_collect, title=title,
                                                       url=url)
        self.ui.info_search_collect_lb_3.mousePressEvent = partial(self.update_collect, title=title,
                                                               url=url)

    def load_collect(self):
        self.ui.info_collect_widget.clear()
        with open(self.collect_path, "r", encoding="utf8") as f:
            self.collect_list = f.readlines()
        for i in self.collect_list [::-1]:
            self.ui.info_collect_widget.addItem(i.split("|")[0])
        self.update_collect_list()
    # def check_collect


    def smooth_scroll_to_top(self,event):
        current_value = self.ui.scrollArea_3.verticalScrollBar().value()
        current_value1 = self.ui.scrollArea_4.verticalScrollBar().value()
        self.animation.setStartValue(current_value)
        self.animation1.setStartValue(current_value1)
        self.animation.setEndValue(0)
        self.animation1.setEndValue(0)
        self.animation.start()
        self.animation1.start()

    def open_infomation_widget(self,event):#删除event
        self.ui.infomation_widget.show()
        self.ui.info_search_detail_widget.hide()
        self.ui.info_load_lb.setText("加载中")
        # 监听滚动条事件
        self.ui.scrollArea_3.verticalScrollBar().valueChanged.connect(self.on_scroll)
        self.ui.info_up_img.setPixmap(QPixmap(self.img_path + r'\return_up.png').scaled(32, 32))
        self.info_collect_item_list_name=[]
        self.load_collect()
        self.load_info_data()#开启线程
        self.update_collect_list()
        self.update_history_list()#更新历史记录列表供使用
        self.ui.info_collect_widget.itemClicked.connect(self.toggle_info_collect_list_detail)
        self.ui.info_history_widget.itemClicked.connect(self.toggle_info_history_list_detail)


    def load_info_data(self):
        self.worker_thread_info = get_title()
        self.worker_thread_info.finished.connect(self.task_finished_info)
        self.worker_thread_info.start()

    def show_detail_widget(self,title,tip,content):
        self.ui.info_detail_widget.show()
        self.ui.info_detail_webBrowser.setHtml(content)
        self.ui.info_detail_title_lb.setText(title)
        self.ui.info_detail_tip_lb.setText(tip)

    def load_news(self, count):
        try:
            self.load_new_n = 0
            """加载指定数量的新闻"""
            # print(self.info_url[self.info_index_start:self.info_index_start+count])
            self.info= asyncio.run(
                url_infos(self.info_url[self.info_index_start:self.info_index_start + count]).main())
            # print(self.info)
            tip_init=[]
            article_init=[]
            for i in range(self.info_index_start,self.info_index_start+count):
                getattr(self.ui,f"info_title_{i-10*self.load_new_n+1}").setText(self.info_title_l[i])
                getattr(self.ui, f"info_img_{i -10*self.load_new_n+ 1}").setHtml(f'<img src="{self.info_img_url[i]}" width="100%" height="100%">')
                getattr(self.ui,f"info_date1_{i-10*self.load_new_n+1}").setText(self.info[i-10*self.load_new_n][0])
                getattr(self.ui,f"info_source_{i-10*self.load_new_n+1}").setText(self.info[i-10*self.load_new_n][1])
                getattr(self.ui, f"info_title_{i - 10 * self.load_new_n + 1}").mousePressEvent = partial(
                    self.toggle_info_detail, index=i - 10 * self.load_new_n)
                tip_init.append("发布时间： "+self.info[i-10*self.load_new_n][0]+"  来源： "+self.info[i-10*self.load_new_n][1])
                article_init.append(self.info[i-10*self.load_new_n][2])
            self.title_l=self.info_title_l[self.info_index_start:self.info_index_start+count]
            self.tip_l=tip_init
            self.article_l=article_init

            # print(self.title_l)
            # print(self.tip_l)
            # self.info_title_1.mousePressEvent=partial(self.toggle_info_detail,index=0)
            # getattr(self,f"info_tag_{i+1}").setText(f"<b>{random.choice(["精选","推荐","优质",])}</b>")
            # img_url=self.info_img_url[i]
            # date=self.info[i].split("|")[0]
            # source=self.info[i].split("|")[1]
            # article=self.info[i].split("|")[2]
            # article=article_data.return_article_data(url=self.info_url[i])

        except Exception as e:
            print(e)
    def task_finished_info(self,data_l):
        self.info_title_l,self.info_img_url,self.info_url=data_l
        self.load_news(10)
        self.load_new_n += 1
        self.ui.info_load_lb.setText("加载完成")

        # self.info_index_start+=10

    def on_scroll(self):
        """滚动条事件处理"""
        scroll_bar = self.ui.scrollArea_3.verticalScrollBar()
        if scroll_bar.value() == scroll_bar.maximum() and self.info_index_start <=49:  # 滚动到底部
            self.info_index_start+=10
            self.load_news(10)  # 加载更多新闻
            self.load_new_n += 1
            self.smooth_scroll_to_top(event=None)
            self.ui.info_load_lb.setText("刷新成功")

        elif self.info_index_start >=49:
            self.ui.info_load_lb.setText("已全部加载")

    def info_mouse_press_event(self, event):
        super(QLineEdit, self.ui.info_search_textEdit).mousePressEvent(event)  # 调用父类方法，确保正常行为
        self.show_search_widget()#调用搜索框





    def load_info_detail(self,index):
        # self.info_index=10 * (self.load_new_n - 1) + index
        article_title = self.title_l[index]
        article_url = self.info_url[10 * (self.load_new_n - 1) + index]
        self.show_detail_widget(self.title_l[index], self.tip_l[index],
                                self.article_l[index])
        self.update_view_history(self.title_l[index],
                                 self.info_url[10 * (self.load_new_n - 1) + index])
        self.ui.info_collect_lb_3.mousePressEvent = partial(self.open_url, url=article_url)
        self.ui.info_collect_lb_2.mousePressEvent = partial(self.copy_to_clipboard, url=article_url)
        self.check_collect(title=article_title, url=article_url)
        self.info_detail_status = True

        self.update_info_list()
        self.ui.info_detail_listWidget.itemClicked.connect(self.load_info_list_detail)


    def toggle_info_detail(self,event,index):#[0-9]
        try:
            if self.info_detail_status:
                self.ui.info_detail_widget.hide()
                self.info_detail_status=False
                self.load_view_history()
            else:
                self.ui.info_detail_return_lb_2.hide()
                self.ui.info_detail_return_lb_3.hide()
                self.ui.info_detail_return_lb.show()
                self.ui.info_detail_widget.show()
                self.load_info_detail(index)
                self.ui.info_list_lb.setText("最新新闻")
        except Exception as e:
            print(e)


    def update_collect_list(self):
        self.info_collect_item_list_name = []
        for i in range(self.ui.info_collect_widget.count()):
            item = self.ui.info_collect_widget.item(i)
            self.info_collect_item_list_name.append(item.text())

    def update_history_list(self):
        self.info_history_item_list_name = []
        for i in range(self.ui.info_history_widget.count()):
            item = self.ui.info_history_widget.item(i)
            self.info_history_item_list_name.append(item.text())

    def toggle_info_collect_list_detail(self,item):
        try:
                self.info_collect_item_index = self.info_collect_item_list_name.index(item.text())
                with open(self.collect_path, "r", encoding="utf8") as f:
                    collect_list = f.readlines()[::-1]
                article_title = collect_list[self.info_collect_item_index].split("|")[0].strip()
                article_url = collect_list[self.info_collect_item_index].split("|")[1].replace("\n", "")
                a = []
                a.append(article_url)
                article_info = asyncio.run(url_infos(a).main())[0]
                article_tip = "发布时间： " + article_info[0] + "  来源： " + article_info[1]
                article_content = article_info[2]
                self.ui.info_detail_widget.show()
                self.ui.info_detail_return_lb_2.show()
                self.ui.info_detail_return_lb.hide()
                self.ui.info_detail_return_lb_3.hide()
                self.show_detail_widget(article_title,article_tip,article_content)
                # self.update_view_history(article_title,self.info_url[10*self.load_new_n+self.info_index])
                self.check_collect(title=article_title,url=article_url)
                self.info_detail_status_2 = True
                self.ui.info_list_lb.setText("全部收藏")
                self.update_detail_collect_list()
                # self.update_detail_viewed_list()
                self.ui.info_detail_listWidget.itemClicked.connect(self.load_collect_list_detail)
                self.ui.info_collect_lb_3.mousePressEvent = partial(self.open_url, url=article_url)
                self.ui.info_collect_lb_2.mousePressEvent = partial(self.copy_to_clipboard, url=article_url)
        except Exception as e:
            print(e)

    def toggle_info_history_list_detail(self,item):
        try:
            self.info_history_item_index = self.info_history_item_list_name.index(item.text())
            with open(self.viewed_history_path, "r", encoding="utf8") as f:
                collect_list = f.readlines()[::-1]
            article_title = collect_list[self.info_history_item_index].split("|")[0].strip()
            article_url = collect_list[self.info_history_item_index].split("|")[1].replace("\n", "")
            a = []
            a.append(article_url)
            article_info = asyncio.run(url_infos(a).main())[0]
            article_tip = "发布时间： " + article_info[0] + "  来源： " + article_info[1]
            article_content = article_info[2]
            self.ui.info_detail_widget.show()
            self.ui.info_detail_return_lb_3.show()
            self.ui.info_detail_return_lb_2.hide()
            self.ui.info_detail_return_lb.hide()
            self.show_detail_widget(article_title,article_tip,article_content)
            # self.update_view_history(article_title,self.info_url[10*self.load_new_n+self.info_index])
            self.check_collect(title=article_title,url=article_url)
            self.ui.info_collect_lb_3.mousePressEvent = partial(self.open_url, url=article_url)
            self.ui.info_collect_lb_2.mousePressEvent = partial(self.copy_to_clipboard, url=article_url)
            self.update_collect_list()
            self.info_detail_status_3 = True
            self.ui.info_list_lb.setText("全部浏览历史")
            self.update_detail_viewed_list()
            self.ui.info_detail_listWidget.itemClicked.connect(self.load_viewed_list_detail)
        except Exception as e:
            print(e)

    def load_info_list_detail(self,item):#从首页进入文章详情页面的
        try:
            self.info_item_index = self.info_title_l.index(item.text())
            article_title = self.info_title_l[self.info_item_index].strip()
            article_url = self.info_url[self.info_item_index].strip()
            a = []
            a.append(article_url)
            article_info = asyncio.run(url_infos(a).main())[0]
            article_tip = "发布时间： " + article_info[0] + "  来源： " + article_info[1]
            article_content = article_info[2]
            self.show_detail_widget(article_title,article_tip,article_content)
            self.check_collect(title=article_title,url=article_url)
        except Exception as e:
            print(e)

    def load_viewed_list_detail(self,item):#从首页进入文章详情页面的
        try:
            self.info_item_index = self.view_history_title.index(item.text())
            article_title = self.view_history_title[self.info_item_index].strip()
            article_url = self.view_history_url[self.info_item_index].strip()
            a = []
            a.append(article_url)
            article_info = asyncio.run(url_infos(a).main())[0]
            article_tip = "发布时间： " + article_info[0] + "  来源： " + article_info[1]
            article_content = article_info[2]
            self.show_detail_widget(article_title,article_tip,article_content)
            self.check_collect(title=article_title,url=article_url)
        except Exception as e:
            print(e)

    def load_collect_list_detail(self,item):#从首页进入文章详情页面的
        try:
            self.info_item_index = self.collect_title.index(item.text())
            article_title = self.collect_title[self.info_item_index].strip()
            article_url = self.collect_url[self.info_item_index].strip()
            a = []
            a.append(article_url)
            article_info = asyncio.run(url_infos(a).main())[0]
            article_tip = "发布时间： " + article_info[0] + "  来源： " + article_info[1]
            article_content = article_info[2]
            self.show_detail_widget(article_title,article_tip,article_content)
            self.check_collect(title=article_title,url=article_url)
        except Exception as e:
            print(e)

    def update_info_list(self):#刷新最新新闻列表项
        self.ui.info_detail_listWidget.clear()
        for i in range(len(self.info_title_l)):
            self.ui.info_detail_listWidget.addItem(self.info_title_l[i])

    def update_detail_viewed_list(self):#刷新最新新闻列表项
        self.ui.info_detail_listWidget.clear()
        with open(self.viewed_history_path, "r", encoding="utf8") as f:
            view_history = f.readlines()
        self.view_history_title=[i.split("|")[0] for i in view_history]
        self.view_history_url=[i.split("|")[1] for i in view_history]
        for i in view_history [::-1]:
            self.ui.info_detail_listWidget.addItem(i.split("|")[0])

    def update_detail_collect_list(self):  # 刷新最新新闻列表项
        self.ui.info_detail_listWidget.clear()
        with open(self.collect_path, "r", encoding="utf8") as f:
            collect_ = f.readlines()
        self.collect_title = [i.split("|")[0] for i in collect_]
        self.collect_url = [i.split("|")[1] for i in collect_]
        for i in collect_[::-1]:
            self.ui.info_detail_listWidget.addItem(i.split("|")[0])

    def hide_info_detail(self,event):
        self.ui.info_detail_widget.hide()
        self.info_detail_status_2 = False
        self.load_view_history()

    def hide_info_detail_his(self,event):
        self.ui.info_detail_widget.hide()
        self.info_detail_status_3 = False
        self.load_view_history()

    def info_move_to(self,status):
        if status:
            self.ui.scrollArea_3.move(30, 210)
            self.ui.info_load_lb.move(1370,890)
            self.ui.info_up_lb.move(1365,830)
            self.ui.info_up_img.move(1370,790)
            self.ui.info_search_textEdit.move(380, 80)
            self.ui.info_search_btn.move(900, 81)
            self.ui.info_search_widget.move(380,121)

        else:
            self.ui.scrollArea_3.move(200, 210)
            self.ui.info_load_lb.move(1540, 890)
            self.ui.info_up_lb.move(1535, 830)
            self.ui.info_up_img.move(1540, 790)
            self.ui.info_search_textEdit.move(550,80)
            self.ui.info_search_btn.move(1070,81)
            self.ui.info_search_widget.move(550,121)

    def update_view_history(self,title,url):
        with open(self.viewed_history_path, "r", encoding="utf8")as f:
            view_history=f.readlines()
        with open(self.viewed_history_path, "a+",
                  encoding="utf8") as f:
            data=title+"|"+url+"\n"
            if data not in set(view_history):
                f.write(title+"|"+url+"\n")
            f.close()

        # self.load_view_history()

    def load_view_history(self):
        self.ui.info_history_widget.clear()
        with open(self.viewed_history_path, "r", encoding="utf8")as f:
            self.view_history=f.readlines()
            f.close()
        for i in self.view_history[::-1]:
            self.ui.info_history_widget.addItem(i.split("|")[0])
        self.update_history_list()

    def toggle_info_history_list(self,event):
        if self.info_history_status:
            self.info_history_status = False
            self.ui.info_history_widget.hide()
            # self.info_collect_widget.hide()
            self.info_move_to(False)
        else:
            self.info_collect_status = False
            self.info_history_status = True
            self.ui.info_history_widget.show()
            self.ui.info_collect_widget.hide()
            self.info_move_to(True)
            self.load_view_history()

    def toggle_info_collect_list(self, event):
        if self.info_collect_status:
            self.info_collect_status = False
            # self.info_history_widget.hide()
            self.ui.info_collect_widget.hide()
            self.info_move_to(False)
        else:
            self.info_collect_status = True
            self.info_history_status = False
            self.ui.info_collect_widget.show()
            self.ui.info_history_widget.hide()
            self.info_move_to(True)
            self.load_collect()
