
class NewsWidget(QWidget):
        def __init__(self, title,img_url, parent=None):
            try:
                super().__init__(parent)
                # self.title_p=""
                # self.info_detail_status=False
                self.setFixedHeight(164)  # 设置固定高度
                # self.setFixedWidth(600)  # 设置固定高度
                # 左边显示图片
                self.web_view_img = QWebEngineView(self)
                self.web_view_img.setHtml(f"""<img src="{img_url}" width="100%" height="100%">""")
                self.web_view_img.setGeometry(0, 0, 210, 154)  # 设置图片的位置和大小
                # 右边显示新闻标题
                self.title_label = QLabel(f"<b>{title}</b>", self)
                self.title_label.setStyleSheet("QLabel{font-size: 25px;padding-top:10px;background-color: white; border: 0}QLabel:hover { color: #FF4400; }")
                self.title_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignTop)
                self.title_label.setGeometry(220, 0, 1500, 50)

                tag=["精选","推荐","优质",]
                self.tag_label = QLabel(f"<b>{random.choice(tag)}</b>", self)
                self.tag_label.setStyleSheet("font-size: 16px;background-color: white ;color:#FF4400;border: 1 solid #FF4400;padding:5px;border-radius:10px ;")
                self.tag_label.setGeometry(220, 70, 55, 35)
                #来源
                # self.source_label = QLabel(f"来源：{source}", self)
                # self.source_label.setStyleSheet("font-size: 14px;background-color: white ;border: 0;")
                # self.source_label.setGeometry(220, 115, 1200, 50)
                # self.time_label = QLabel(f"{time}", self)
                # self.time_label.setStyleSheet("font-size: 14px;background-color: white ;color:gray;border: 0;")
                # self.time_label.setGeometry(1200, 115, 1500, 50)
            except Exception as e:
                print(e)

