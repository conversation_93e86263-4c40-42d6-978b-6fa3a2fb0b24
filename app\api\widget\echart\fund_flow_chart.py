import sys
import os
import json
import random

import requests
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
from PyQt6.QtWebEngineWidgets import QWebEngineView
from PyQt6.QtCore import QUrl

class FundFlowChartWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('主力资金流向图表')
        self.setGeometry(100, 100, 1200, 700)
        
        # 模拟数据
        self.url = "https://data.eastmoney.com/dataapi/bkzj/getbkzj?key=f62&code=m%3A90%2Bt%3A2"
        self.headers = {
            "Host": "data.eastmoney.com",
            "Connection": "keep-alive",
            "sec-ch-ua-platform": "\"Windows\"",
            "X-Requested-With": "XMLHttpRequest",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "Accept": "*/*",
            "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
            "sec-ch-ua-mobile": "?0",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://data.eastmoney.com/bkzj/hy.html",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Cookie": "_qimei_uuid42=1971f092f20100c8520e923a54ec5194e79fca7ae3; st_nvi=FC7DZFrbPA0oIgj5rXYEv82ef; _qimei_fingerprint=2cf2be01d9c2ffcda61387b8e25138d3; _qimei_i_3=2ddb57d3c45f5189c996aa63088773b3f7ebadf4415c02d0b7862c5c26c7293a323161943c89e2bd95b0; _qimei_h38=; nid=0be1cbd9749ac36e1ca59b9716dbc216; nid_create_time=1753926453005; gvi=IaOd6G-zLK4Ce1YihFYxU3fb5; gvi_create_time=1753926453006; EMFUND1=null; EMFUND2=null; EMFUND3=null; nid_id=568160114; qgqp_b_id=a907f2dbaff1a7c5ebe17be718de468c; EMFUND0=null; EMFUND4=07-31%2012%3A45%3A43@%23%24%u666F%u987A%u957F%u57CE%u5185%u9700%u589E%u957F%u6DF7%u5408A@%23%24260104; EMFUND5=07-31%2016%3A04%3A23@%23%24%u534E%u590F%u5927%u76D8%u7CBE%u9009%u6DF7%u5408A@%23%24000011; EMFUND6=07-31%2012%3A42%3A56@%23%24%u534E%u590F%u503A%u5238A/B@%23%24001001; EMFUND7=08-03%2018%3A14%3A19@%23%24%u5FB7%u90A6%u7A33%u76C8%u589E%u957F%u7075%u6D3B%u914D%u7F6E%u6DF7%u5408C@%23%24018463; EMFUND8=08-04%2012%3A14%3A30@%23%24%u5609%u5B9E%u4E92%u878D%u7CBE%u9009%u80A1%u7968A@%23%24006603; EMFUND9=08-04 12:35:13@#$%u7533%u4E07%u83F1%u4FE1%u6D88%u8D39%u589E%u957F%u6DF7%u5408A@%23%24310388; st_si=29313470527222; st_asi=delete; fullscreengg=1; fullscreengg2=1; _qimei_i_1=78df2afdce5b; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=16; st_psi=20250807140836345-113300300820-5630491407"
        }
        self.mock_data = self.generate_mock_data()


        
        # 创建主布局
        central_widget = QWidget()
        layout = QVBoxLayout()
        
        # 添加刷新按钮
        refresh_btn = QPushButton("刷新数据")
        refresh_btn.clicked.connect(self.refresh_data)
        layout.addWidget(refresh_btn)
        
        # 创建Web视图
        self.browser = QWebEngineView()
        layout.addWidget(self.browser)
        
        central_widget.setLayout(layout)
        self.setCentralWidget(central_widget)
        
        # 加载图表
        self.load_chart()

    def request_data(self):
        """获取真实的板块资金流向数据"""
        try:
            response = requests.get(self.url, headers=self.headers, verify=False)
            data = response.json()
            
            # 获取diff数据列表
            diff_list = data["data"]["diff"]
            
            bk_name = []
            bk_value = []
            
            for item in diff_list:
                # 获取板块名称
                bk_name.append(item["f14"])
                
                # 将f62从元转换为亿元，保留2位小数
                value_in_yi = round(item["f62"] / 100000000, 1)
                bk_value.append(value_in_yi)
            
            return bk_name, bk_value
            
        except Exception as e:
            print(f"请求数据失败: {e}")
            return [], []

    def generate_mock_data(self):
        """生成模拟的资金流向数据"""
        categories ,bk_value= self.request_data()
        # categories = [
        #     '人工智能', '芯片', '减肥药', '破净股', '机器人', 'CPO', 'CRO',
        #     '电力', '汽车整车', '存储器', '存储芯片', '花园生物', '铜缆高频',
        #     '消费电子', '同花顺', '恒瑞医药', '体外诊断', 'WIFI6', '蓝牙',
        #     '大飞机', '增现实', 'AI芯片', '毫米波', '海南', '黄金概念',
        #     '新能源', '光伏', '风电', '锂电池', '储能', '氢能源', '核电',
        #     '5G', '物联网', '云计算', '大数据', '区块链', '虚拟现实',
        #     '生物医药', '医疗器械', '疫苗', '创新药', '仿制药', '中药',
        #     '白酒', '食品饮料', '农业', '种业', '化肥', '农药',
        #     '房地产', '建筑', '水泥', '钢铁', '有色金属', '煤炭',
        #     '银行', '保险', '券商', '信托', '租赁', '多元金融',
        #     '航空', '机场', '港口', '物流', '快递', '铁路运输',
        #     '纺织', '服装', '家具', '家电', '消费电子', '珠宝',
        #     '旅游', '酒店', '餐饮', '影视', '游戏', '教育',
        #     '环保', '水务', '固废处理', '大气治理', '土壤修复'
        # ]

        # 将每个分类名称的字符间添加换行符，实现垂直显示
        vertical_categories = ['\n'.join(list(category)) for category in categories]

        # 生成随机数据（包含正负数）
        net_flow_data = []
        for _ in range(len(categories)):
            # 60%概率为正数（净流入），40%概率为负数（净流出）
            if random.random() < 0.6:
                net_flow_data.append(round(random.uniform(0.1, 30), 2))
            else:
                net_flow_data.append(round(random.uniform(-25, -0.1), 2))
        net_flow_data=bk_value
        # 创建分类和数据的配对，然后按数据从高到低排序
        paired_data = list(zip(vertical_categories, net_flow_data))
        paired_data.sort(key=lambda x: x[1], reverse=True)

        # 分离排序后的分类和数据
        sorted_categories = [item[0] for item in paired_data]
        sorted_data = [item[1] for item in paired_data]
        print(sorted_data)

        return {
            'categories': sorted_categories,
            'data': sorted_data
        }

    def load_chart(self):
        """加载图表HTML模板"""
        current_dir = os.path.dirname(os.path.abspath(__file__))
        html_path = os.path.join(current_dir, 'bar_chart_template.html')

        if os.path.exists(html_path):
            self.browser.setUrl(QUrl.fromLocalFile(html_path))
            self.browser.loadFinished.connect(self.on_load_finished)
        else:
            print(f"HTML模板文件不存在: {html_path}")

    def on_load_finished(self):
        """页面加载完成后注入数据"""
        data_json = json.dumps(self.mock_data, ensure_ascii=False)
        js_code = f'updateChart({data_json});'
        self.browser.page().runJavaScript(js_code, self.js_callback)

    def js_callback(self, result):
        """JavaScript执行回调"""
        print(f'图表更新结果: {result}')

    def refresh_data(self):
        """刷新数据"""
        self.mock_data = self.generate_mock_data()
        data_json = json.dumps(self.mock_data, ensure_ascii=False)
        js_code = f'updateChart({data_json});'
        self.browser.page().runJavaScript(js_code, self.js_callback)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = FundFlowChartWindow()
    window.show()
    sys.exit(app.exec())



