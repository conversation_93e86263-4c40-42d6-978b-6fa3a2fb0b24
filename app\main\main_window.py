# 数据api

from app.api.widget.calculator.exchange import get_exchange
from app.api.widget.calculator.get_bank_ck import get_bank_ck_data
from app.api.widget.calculator.get_bank_wh import get_bank_wh_data
from app.api.widget.calculator.get_exchange_html import get_exchange_html

from app.api.widget.hbjj.get_hbjj import get_hbjj_data
from app.api.widget.hbjj.jj_company_code import get_jj_company
from app.api.widget.index.get_table_data import table_data
from app.api.widget.dtjj.get_dt_data import dt_web
from app.api.widget.information.get_m_infos_1 import url_infos_1
from app.api.widget.information.get_other_article import get_other_title
from app.api.widget.information.get_search_article import get_search_title
from app.api.widget.information.get_search_code import get_search_c
from app.api.widget.kf_hb_jj.get_kfjj_data_2 import kfjj_data_params
from app.api.widget.kf_hb_jj.get_kfjj_data import kfjj_data
from app.api.widget.kf_hb_jj.kfjj_ import CustomDelegate
from app.api.widget.information.get_title_data import get_title
from app.api.widget.information.get_m_infos import url_infos
from app.api.widget.information.get_search_data import search_data
from app.common.api.code_company import FundCompany
from app.main.showZJLX import ZJPHWindow
from app.modules.FundCompany_.FundCompany_ import FundCompany_
from app.modules.FundCompare.FundCompare import FundCompare
from app.modules.FundFilter.FundFilter import FundFilter
from app.modules.FundHome1.FundHome1 import FundHome1
from app.modules.FundHome2.FundHome2 import FundHome2
from app.modules.FundNewFound.FundNewFound import FundNewFound
from app.modules.FundSelection.FundSelection import FundSelection
from app.modules.FundSubject.FundSubject import FundSubject
from app.modules.Fundhb.FundHb import FundHb
# from showZJLX import ZJPHWindow
from main_w import Ui_MainWindow
from app.api.other.wrapp import calculate_time
from app.api.widget.index.hot_search.function import toggle_lz_hot_widget
from app.api.widget.index.news_l.function import toggle_list_news_widget
from app.api.widget.index.H_top_table.function import toggle_table_index_widget
from app.api.widget.dtjj.function import toggle_dt_index_widget
from app.api.widget.index.left_zfb.function import change_l_r_bang,change_l_l_bang
from app.api.widget.index.right_zfb.function import change_r_l_bang,change_r_r_bang,r_table_index
from app.api.widget.chat.function import toggle_send_stop,toggle_send_stop_img,thread_finished,change_chat_model,clear_context,send_question,stop_response,add_user_message,add_ai_message_stream
from PyQt6.QtCore import Qt
#基础模块
import asyncio, time
from datetime import datetime, timedelta, date
from functools import partial
import concurrent.futures
#函数
from PyQt6.QtNetwork import *
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *
from PyQt6.QtWidgets import QGraphicsDropShadowEffect
# from PyQt6.QtWebEngineCore import QWebEnginePage
import urllib3
urllib3.disable_warnings()


# class MainWindow(Ui_MainWindow,QMainWindow):
#     def __init__(self, parent=None, ):
#         super(Ui_MainWindow, self).__init__(parent)
#         self.setupUi(self)
#         self.resize(1800, 1000)
        # self.showMaximized()
class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.ui: Ui_MainWindow = Ui_MainWindow()  # 添加类型注释
        self.ui.setupUi(self)
        self.showMaximized()

        # init_hot_widget_status(self)
        # init_news_widget_status(self)
        # init_top_table_widget_status(self)
        # init_dtjj_widget_status(self)
        # init_left_zfb_widget_status(self)
        # init_right_zfb_widget_status(self)
        # init_chatai_widget_status(self)
        #
        # self.info_detail_status=False
        # self.info_detail_status_2=False
        # self.info_detail_status_3=False
        # self.index_kf_status=False
        # self.kf_cal_status=False
        # self.info_history_status=False
        # self.info_collect_status=False
        # self.info_search_status=False
        # self.kf_sort = "desc"
        # self.dt_jj_index=0
        # self.kf_jj_index=0#开放基金top索引
        # self.kf_jj_sub_index=0#开放基金对应子索引
        # self.hot_start = 0
        # self.search_widget_status=False
        # self.top_widget_index=0#顶部widget切换索引
        # self.show_hide_top_widget(self.top_widget_index)
        # self.img_path = r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\img"  # 图片地址
        #
        # """
        # 初始化显示图片
        # """
        # self.label_38.setPixmap(QPixmap(self.img_path + r'\red_yuan.png').scaled(20, 20))  # 右侧123圆底图片
        # self.label_39.setPixmap(QPixmap(self.img_path + r'\or_yuan.png').scaled(20, 20))
        # self.label_40.setPixmap(QPixmap(self.img_path + r'\yellow_yuan.png').scaled(20, 20))
        # for i in range(41, 48): getattr(self, f'label_{i}').setPixmap(
        #     QPixmap(self.img_path + r'\gray_yuan1.png').scaled(20, 20))
        # # self.label_88.setPixmap(QPixmap(self.img_path + r'\kl6.png').scaled(240, 135)) #领涨图
        # self.label_90.setPixmap(QPixmap(self.img_path + r'\kl3.png').scaled(188, 135))  # 领涨概念对应图
        # self.hot_bg.setPixmap(QPixmap(self.img_path + r'\red_yuan.png').scaled(20, 20))  # 热搜界面右侧123圆底图片 index_top_hot
        # self.hot_bg_2.setPixmap(QPixmap(self.img_path + r'\or_yuan.png').scaled(20, 20))
        # self.hot_bg_3.setPixmap(QPixmap(self.img_path + r'\yellow_yuan.png').scaled(20, 20))
        # for i in range(4, 21): getattr(self, f'hot_bg_{i}').setPixmap(
        #     QPixmap(self.img_path + r'\gray_yuan1.png').scaled(20, 20))
        # for i in range(1,5):getattr(self,f"top_img_{i}").setPixmap(QPixmap(self.img_path+fr'\top{i}.png'))# top_bar 左边图片+文字
        # self.top_m_png.setPixmap(QPixmap(self.img_path + r'\line_plot_transparent.png').scaled(300, 100))#top_moneny 收益图片
        #
        #     # index_tab
        # self.index_hs_png_1.setPixmap(QPixmap(self.img_path + r'\kl.png').scaled(578, 276))#行情
        # self.index_hs_png_2.setPixmap(QPixmap(self.img_path + r'\KL5.png').scaled(520, 283))
        # self.label_107.setPixmap(QPixmap(self.img_path + '/green_down.png').scaled(15, 25))#默认箭头
        #
        # self.tz_compute_widget.hide()  # 投资计算器隐藏
        # self.zx_widget.hide()  # 资讯页面隐藏
        # self.chatai_widget.hide()  # 顶部chatai隐藏
        # self.hot_widget.hide()  # 热搜隐藏
        # self.search_widget.hide()  # 搜索界面
        # self.index_news_widget.hide()  # 新闻详情页面
        # self.total_tb_widget.hide()#top表格隐藏
        # # self.chat_widget.hide()#chat界面隐藏
        # self.top_dtph_widget.hide()
        # self.kfjj_widget.hide()
        # self.kf_qdii_tab_widget.hide()
        # self.kf_zs_tab_widget.hide()
        # self.kf_zq_tab_widget.hide()
        # self.calendarWidget.hide()
        # self.infomation_widget.hide()
        #
        #
        # """
        # label，textEdit绑定点击功能
        # """
        # self.enter_hot.mousePressEvent= self.toggle_lz_hot_widget#进入热搜
        # self.retuen_lz.mousePressEvent = self.toggle_lz_hot_widget#返回出热搜
        # self.news_return.mousePressEvent = self.toggle_list_news_widget#返回出新闻页面
        # self.textEdit.mousePressEvent = self.custom_mouse_press_event#点击搜索下拉
        # self.textEdit.textChanged.connect(self.quick_s)
        #
        #
        # self.label_107.mousePressEvent = self.toggle_left_hy_arrow #切换左行情箭头
        # self.l_l_zfb.mousePressEvent = self.change_l_l_bang#切换左行情索引
        # self.l_r_zfb.mousePressEvent = self.change_l_r_bang
        # self.r_l_zfb.mousePressEvent = self.change_r_l_bang # 切换右行情索引
        # self.r_r_zfb.mousePressEvent = self.change_r_r_bang
        # # self.l_center_lb.mousePressEvent = self.change_tabel_jjjz
        #
        # self.r_table_ph.currentIndexChanged.connect(self.r_table_index)#资金今日排行切换
        # self.index_new_lb_1.mousePressEvent=self.toggle_list_news_widget#新闻列表点击进入新闻详情
        # self.left_widget_change.mousePressEvent=self.change_left_widget
        # self.right_widget_change.mousePressEvent=self.change_right_widget
        # # 连接日历的点击信号
        # self.calendarWidget.clicked.connect(self.on_calendar_clicked)
        #
        # #top_table_label绑定
        #
        # # 绑定事件处理函数
        # # top_table
        # for name in ["zs", "qz", "qq","bk","ph","xg","cyb","kcb","jj","gg","hs","mg","qh","hj","wh"]:  # 添加你需要的所有名称
        #     label = getattr(self, f"index_top_{name}")
        #     label.mousePressEvent = partial(self.handle_mouse_pressself, name=name)
        # self.top_table_return.mousePressEvent=self.toggle_table_index_widget
        # self.before_page_lb.mousePressEvent=self.sub_page
        # self.next_page_lb.mousePressEvent=self.add_page
        # self.tail_page_lb.mousePressEvent=self.tail_page
        # self.first_page_lb.mousePressEvent=self.first_page
        # self.goto_page_lb.mousePressEvent=self.goto_page
        # #top_dt_web
        # self.dt_web_return.mousePressEvent = self.toggle_dt_index_widget
        # self.before_page_lb_dt.mousePressEvent = self.sub_page_dt
        # self.next_page_lb_dt.mousePressEvent = self.add_page_dt
        # self.tail_page_lb_dt.mousePressEvent = self.tail_page_dt
        # self.first_page_lb_dt.mousePressEvent = self.first_page_dt
        # self.goto_page_lb_dt.mousePressEvent = self.goto_page_dt
        # self.top_img_2.mousePressEvent = self.toggle_dt_index_widget
        # #top_kfjj
        # self.top_img_1.mousePressEvent = self.toggle_kf_index_widget
        # self.kfjj_retuen_lb.mousePressEvent = self.toggle_kf_index_widget
        # for i in range(1,8):
        #     getattr(self, f"kf_top_lb_{i}").clicked.connect(partial(self.load_kfjj_widget, i - 1))
        # self.before_page_lb_kf.mousePressEvent = self.sub_page_kf
        # self.next_page_lb_kf.mousePressEvent = self.add_page_kf
        # self.tail_page_lb_kf.mousePressEvent = self.tail_page_kf
        # self.first_page_lb_kf.mousePressEvent = self.first_page_kf
        # self.goto_page_lb_kf.mousePressEvent = self.goto_page_kf
        # self.pushButton.clicked.connect(self.kf_query_cal)
        # self.kf_cal_img.setPixmap(QPixmap(self.img_path + r'\cal1.png').scaled(38, 42))
        # self.kf_cal_img.mousePressEvent=self.toggle_cal_widget
        #
        # """初始化样式"""
        # self.scrollArea.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)#index_scroll，
        # self.scrollArea_2.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)#chat_scroll，
        #
        # """直接从网址转化图片"""
        # self.network_manager = QNetworkAccessManager()
        # self.network_manager.finished.connect(self.on_image_downloaded)
        # self.load_image("https://webquotepic.eastmoney.com/GetPic.aspx?nid=90.BK0478&imageType=rs&0.25487243381954694")
        #
        # """获取数据"""
        # self.initial_message = Message.get_message()
        # self.message = self.initial_message + " " * 50
        # self.textEdit_2.setPlainText(self.message)
        #
        # """测试"""
        # self.timer = QTimer(self)
        # self.timer.timeout.connect(self.scroll_text)
        # self.timer.start(200)
        # self.start_date_c=None
        # self.end_date_c=None
        #
        #
        # # 滚动区域的内容 Widget
        # self.load_new_n = 0
        # self.top_img_4.mousePressEvent=self.open_infomation_widget
        # # self.news_list = []
        # self.info_index_start=0
        # # 初始化动画
        # self.info_up_img.mousePressEvent=self.smooth_scroll_to_top
        # self.info_up_lb.mousePressEvent=self.smooth_scroll_to_top
        # self.animation = QPropertyAnimation(self.scrollArea_3.verticalScrollBar(), b"value")
        # self.animation.setEasingCurve(QEasingCurve.Type.OutCubic)  # 设置缓动曲线
        # self.animation.setDuration(500)  # 设置动画时长（毫秒）
        # self.animation1 = QPropertyAnimation(self.scrollArea_4.verticalScrollBar(), b"value")
        # self.animation1.setEasingCurve(QEasingCurve.Type.OutCubic)  # 设置缓动曲线
        # self.animation1.setDuration(500)  # 设置动画时长（毫秒）
        #
        # #文章详情
        # self.info_detail_widget.hide()
        # # self.info_detail_return_lb.mousePressEvent=
        # self.info_detail_return_lb.mousePressEvent=partial(self.toggle_info_detail,index=-1)
        # # self.info_collect_lb.setPixmap(QPixmap(self.img_path + r'\sc_no.png').scaled(30, 30))
        #
        # self.info_collect_lb_2.setPixmap(QPixmap(self.img_path + r'\share_n.png').scaled(30, 30))
        # self.info_collect_lb_3.setPixmap(QPixmap(self.img_path + r'\web1.png').scaled(30, 30))
        # self.info_other_collect_lb_1.setPixmap(QPixmap(self.img_path + r'\web1.png').scaled(30, 30))
        # self.info_other_collect_lb_2.setPixmap(QPixmap(self.img_path + r'\share_n.png').scaled(30, 30))
        # self.info_search_collect_lb_1.setPixmap(QPixmap(self.img_path + r'\web1.png').scaled(30, 30))
        # self.info_search_collect_lb_2.setPixmap(QPixmap(self.img_path + r'\share_n.png').scaled(30, 30))
        # self.info_history_widget.hide()
        # self.info_collect_widget.hide()
        # self.info_search_widget.hide()
        # self.info_viewed_list.mousePressEvent=self.toggle_info_history_list
        # self.info_collect_list.mousePressEvent=self.toggle_info_collect_list
        # self.update_hot.mousePressEvent=self.toggle_hot_start
        # self.info_search_textEdit.mousePressEvent = self.info_mouse_press_event
        # self.info_search_btn.mousePressEvent=self.search_info
        # self.info_search_btn_2.mousePressEvent=self.search_info_2
        # # self.infomation_widget.mousePressEvent=self.hide_search_widget
        # # QApplication.instance().installEventFilter(self)
        # self.label_99.setPixmap(QPixmap(self.img_path + r'\fire.png').scaled(25, 25))
        # self.label_100.setPixmap(QPixmap(self.img_path + r'\zs.png').scaled(25, 25))
        # self.info_search_img_1.setPixmap(QPixmap(self.img_path + r'\link.png').scaled(25, 25))
        # self.info_search_img_2.setPixmap(QPixmap(self.img_path + r'\sq.png').scaled(30, 30))
        # self.label_136.setPixmap(QPixmap(self.img_path + r'\history-info.png').scaled(25, 25))
        # self.label_136.setPixmap(QPixmap(self.img_path + r'\history-info.png').scaled(25, 25))
        # self.label_143.setPixmap(QPixmap(self.img_path + r'\delete_info.png').scaled(25, 25))
        # self.label_143.mousePressEvent =self.clear_history
        # self.info_search_widget.setGraphicsEffect(self.createShadowEffect())
        # self.collect_img = QPixmap(self.img_path + r'\sc.png').scaled(30, 30)
        # self.no_collect_img = QPixmap(self.img_path + r'\sc_no.png').scaled(30, 30)
        # self.info_detail_return_lb_2.mousePressEvent =self.hide_info_detail
        # self.info_detail_return_lb_3.mousePressEvent =self.hide_info_detail_his#从历史记录返回
        # # self.top_transaction.mousePressEvent =self.show_info_other
        # self.info_other_widget.hide()
        # self.info_other_add_page.mousePressEvent=self.add_page_info_other
        # self.info_other_sub_page.mousePressEvent=self.sub_page_info_other
        # self.info_other_first_page.mousePressEvent=self.first_page_info_other
        # self.info_other_tail_page.mousePressEvent=self.tail_page_info_other
        # self.info_current_page = 1
        # # self.info_other_widget_index=0
        # self.jj_info_lb.mousePressEvent=self.open_infomation_widget
        # self.jjgd_lb.mousePressEvent=partial(self.change_info_other_index,index=0)
        # self.jjxx_lb.mousePressEvent=partial(self.change_info_other_index,index=1)
        # self.jjyw_lb.mousePressEvent=partial(self.change_info_other_index,index=2)
        # self.tzcl_lb.mousePressEvent=partial(self.change_info_other_index,index=3)
        #
        # self.info_search_textEdit.textChanged.connect(self.get_search_c_result)
        # self.info_search_img_2.mousePressEvent=self.close_search_widget
        #
        # self.info_search_return_lb.mousePressEvent=self.return_info_
        # #计算器
        # self.top_compute_ck.mousePressEvent = partial(self.change_calculator_index, index=0)
        # self.top_compute_sw.mousePressEvent = partial(self.change_calculator_index, index=1)
        # self.top_compute_wh.mousePressEvent = partial(self.change_calculator_index, index=2)
        # self.top_compute_jj.mousePressEvent = partial(self.change_calculator_index, index=3)
        # self.top_compute_dk.mousePressEvent = partial(self.change_calculator_index, index=4)
        # self.top_compute_zq.mousePressEvent = partial(self.change_calculator_index, index=5)
        # self.close_calculator_widget()
        #


        #基金比较
        # self.fund_compare=FundCompare(self.ui)
        # self.fund_selection=FundSelection(self.ui)
        # self.fund_filter=FundFilter(self.ui)
        # self.fund_new_found=FundNewFound(self.ui)
        # self.fund_subject=FundSubject(self.ui)
        # self.fund_company=FundCompany_(self.ui)

        #首页设计
        self.fund_home_1=FundHome1(self.ui)
        self.fund_home_2=FundHome2(self.ui)

        # 改写
        # self.fund_hb=FundHb(self.ui)


    """基金比较"""

    """货币基金排行榜"""
    # def show_hbjj_widget(self,event):
    #     #默认.txt
    #     self.hbjj_intCompany="0"
    #     self.hbjj_MinsgType=""
    #     self.hbjj_company_listWidget.hide()
    #     self.MinsgType_filter = ["", "a", "b"]  # 全部,100万以下，100万起
    #     self.load_hbjj_data(intCompany=self.hbjj_intCompany,MinsgType=self.hbjj_MinsgType,strSortCol="SYL_1N",orderType="desc")
    #     self.show_hbjj_status = True
    #     self.show_hbjj_company_status=False
    #
    #     for i in range(1,4):
    #         getattr(self,f"hbjj_filter_lb_{i}").mousePressEvent=partial(self.hbjj_filter_index,index=i-1)
    #     self.hbjj_filter_lb_1.setStyleSheet("background-color:#6A5ACD;color:white;border-radius:7px;")
    #     self.hbjj_select_lineEdit.mousePressEvent=self.custom_mouse_press_hbjj
    #     self.hbjj_company_listWidget.itemClicked.connect(self.select_hbjj_code)
    #     self.hbjj_reset_btn.mousePressEvent=self.hbjj_filter_reset
    #
    # def hbjj_filter_reset(self,event):
    #     self.hbjj_intCompany = "0"
    #     self.hbjj_MinsgType = ""
    #     self.load_hbjj_data(intCompany=self.hbjj_intCompany, MinsgType=self.hbjj_MinsgType, strSortCol="SYL_1N",
    #                         orderType="desc")
    #     for i in range(1, 4):
    #         getattr(self, f"hbjj_filter_lb_{i}").setStyleSheet("background-color:none;color:black;border-radius:7px;")
    #     self.hbjj_filter_lb_1.setStyleSheet("background-color:#6A5ACD;color:white;border-radius:7px;")
    #     self.hbjj_select_lineEdit.setText("")
    #     self.hbjj_company_listWidget.hide()
    #
    #
    # def custom_mouse_press_hbjj(self,event):
    #     try:
    #         super(QLineEdit,self.hbjj_select_lineEdit).mousePressEvent(event)  # 调用父类方法，确保正常行为
    #         self.show_hbjj_listWidget()
    #     except Exception as e:
    #         print(e)
    #
    # def show_hbjj_listWidget(self,):
    #     try:
    #         if not self.show_hbjj_company_status:
    #             get_jj_company#获取基金公司数据
    #             self.show_hbjj_company_status = True
    #         self.hbjj_select_lineEdit.textChanged.connect(self.get_hbjj_company)
    #         self.hbjj_company_listWidget.show()
    #         self.get_hbjj_company("")
    #         self.hbjj_select_lineEdit.setText("")
    #     except Exception as e:
    #         print(e)
    #
    # def hbjj_filter_index(self,event,index):
    #     for i in range(1, 4):
    #         getattr(self, f"hbjj_filter_lb_{i}").setStyleSheet("background-color:none;color:black;border-radius:7px;")
    #     self.hbjj_MinsgType=self.MinsgType_filter[index]
    #     getattr(self, f"hbjj_filter_lb_{index+1}").setStyleSheet("background-color:#6A5ACD;color:white;border-radius:7px;")
    #     self.hbjj_MinsgType = self.MinsgType_filter[index]
    #     self.load_hbjj_data(intCompany=self.hbjj_intCompany,MinsgType=self.hbjj_MinsgType ,strSortCol="SYL_1N",orderType="desc")
    #
    # def get_hbjj_company(self,text):
    #     try:
    #         self.hbjj_company_listWidget.clear()
    #         data_list=get_jj_company.check_query(query=text)
    #         for i in data_list:
    #             self.hbjj_company_listWidget.addItem(i)
    #     except Exception as e:
    #         print(e)
    #
    # def select_hbjj_code(self,item):
    #     try:
    #         item_name=item.text()
    #         print(item_name)
    #         print("**")
    #         self.hbjj_intCompany=item_name.split("[")[1].strip("]")
    #         self.load_hbjj_data(intCompany=self.hbjj_intCompany, MinsgType=self.hbjj_MinsgType, strSortCol="SYL_1N",orderType="desc")
    #         self.hbjj_company_listWidget.hide()
    #         self.hbjj_select_lineEdit.setText(item_name)
    #     except Exception as e:
    #         print(e)
    #
    #
    # def toggle_hbjj_index(self,event):
    #     if self.show_hbjj_status:
    #         self.hbjj_widget.hide()
    #         self.show_hbjj_status = False
    #     else:
    #         self.hbjj_widget.show()
    #         self.show_hbjj_status = True
    #
    # def load_hbjj_data(self,intCompany,MinsgType,strSortCol,orderType):
    #     try:
    #         self.hbjj_st=time.time()
    #         self.hbjj_widget.show()
    #         self.hbjj_load_lb.setText("正在加载")
    #         self.worker_thread_hbjj = get_hbjj_data(intCompany=intCompany,MinsgType=MinsgType,strSortCol=strSortCol,orderType=orderType)
    #         self.worker_thread_hbjj.finished.connect(self.task_finished_hbjj)
    #         self.worker_thread_hbjj.start()
    #     except Exception as e:
    #         print(e)
    #
    # def task_finished_hbjj(self,hbjj_data_list):
    #     self.hbjj_et = time.time()
    #     self.hbjj_load_lb.setText(f"加载完成，本次耗时{self.hbjj_et -self.hbjj_st:.2f}s")
    #     self.load_hbjj_table(hbjj_data_list)
    #
    # def load_hbjj_table(self,data):
    #     headers = ["基金代码", "基金简称", "日期", "万份收益","7日年化",
    #                "14日年化","28日年化","近1月","3月","近6月","近1年","近2年","近3年","近5年","今年来","成立来"]
    #     self.hbjj_tableWidget.setColumnCount(len(headers))
    #     self.hbjj_tableWidget.setRowCount(len(data))
    #     self.hbjj_tableWidget.setHorizontalHeaderLabels(headers)
    #     for row in range(len(data)):
    #         for col in range(len(headers)):
    #             item = QTableWidgetItem(f"{data[row][col]}")
    #             item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
    #             if col in [0,1]:
    #                 item.setForeground(QColor("#294EBC"))
    #             elif col in [7,8,9,10,11,12,13,14,15]:
    #                 if data[row][col] !="--":
    #                     item.setForeground(QColor("#FF0018"))
    #
    #             self.hbjj_tableWidget.setItem(row, col, item)
    #     table_width = self.hbjj_tableWidget.width()-20  # 获取表格宽度
    #     column_count = self.hbjj_tableWidget.columnCount()  # 获取列数
    #     if column_count > 0:
    #         column_width = table_width // column_count  # 计算每列的宽度
    #         for col in range(column_count):
    #             self.hbjj_tableWidget.setColumnWidth(col, column_width)
    #     self.hbjj_tableWidget.horizontalHeader().setStyleSheet("""
    #                                         QHeaderView::section {
    #     background-color:#E6E6FA;  /* 背景色 */
    #     color: black;              /* 文字颜色 */
    #     padding: 5px;              /* 内边距 */
    #      /* 边框 */
    # }
    #                                     """)


    """计算器实现"""
    def change_calculator_index(self,event,index):
        self.calculator_widget_index = index
        self.show_calculator_widget(self.calculator_widget_index)

    def show_calculator_widget(self,index):
        try:
            self.plainTextEdit_2.hide()
            self.calculator_type_list = ["ck", "sw","wh","jj","dk","zq"]
            self.calculator_type_listWidget.clear()
            widget_name = self.calculator_type_list[index]
            for i in self.calculator_type_list:
                getattr(self, f"{i}_total_widget").hide()
            self.wh_time_widget.hide()
            match (index):
                case 0:#存款
                    getattr(self,f"{widget_name}_total_widget").show()
                    self.sub_listWidget_list=["活期储蓄计算器","整存整取计算器","零存整取计算器","整存零取计算器","存本取息计算器","定活两便计算器","通知存款计算器","教育储蓄计算器"]
                    self.show_ck_widget()
                    self.calculator_type_lb.setText("存款类计算器")
                case 1:  # 税务
                    getattr(self, f"{widget_name}_total_widget").show()
                    self.sub_listWidget_list = ["个人所得税计算器", "购房相关税费计算器"]
                    self.show_sw_widget()
                    self.calculator_type_lb.setText("税务类计算器")
                case 2:  # 外汇
                    getattr(self, f"{widget_name}_total_widget").show()
                    self.sub_listWidget_list = ["外汇兑换计算器"]
                    self.show_wh_widget()
                    self.calculator_type_lb.setText("外汇类计算器")
                case 3:  # 基金
                    getattr(self, f"{widget_name}_total_widget").show()
                    self.sub_listWidget_list = ["申(认)购费用计算器", "赎回费用计算器", "收益计算器", "持有期计算器"]
                    self.show_jj_widget()
                    self.calculator_type_lb.setText("基金类计算器")
                case 4:  # 贷款
                    getattr(self, f"{widget_name}_total_widget").show()
                    self.sub_listWidget_list = ["个人贷款计算器", ]
                    self.show_dk_widget()
                    self.calculator_type_lb.setText("贷款类计算器")
                case 5:  #债券
                    getattr(self, f"{widget_name}_total_widget").show()
                    self.sub_listWidget_list = ["债券购买收益率计算器", "债券出售收益率计算器", "债券持有期间收益率计算器", "国债买卖计算器",
                                                "国债收益计算器", ]
                    self.show_zq_widget()
                    self.calculator_type_lb.setText("债券类计算器")
            # 向listwidget添加内容
            for i in self.sub_listWidget_list:
                self.calculator_type_listWidget.addItem(i)
        except Exception as e:
            print(e)
    def show_ck_widget(self):
        self.ck_type = ["hq", "zczq", "lczq","zclq","cbqx","dhlb","tzck","jycx"]
        self.ck_total_widget.show()
        #绑定左侧列表
        self.calculator_type_listWidget.itemClicked.connect(self.bind_ck_sub_)
        self.show_ck_hq_widget()
        self.clear_result(["元","元","元"])
        self.sub_type_widget_(self.ck_type, self.sub_listWidget_list[0])

    def show_sw_widget(self):
        self.sw_type = ["gr", "gf", ]
        self.sw_total_widget.show()
        # 绑定左侧列表
        self.calculator_type_listWidget.itemClicked.connect(self.bind_sw_sub_)
        self.show_sw_gr_widget()
        self.clear_result(["%", "元", "元"])
        self.sub_type_widget_(self.sw_type, self.sub_listWidget_list[0])

    def show_wh_widget(self):
        # self.wh_type = ["whdh", "whcx"]
        self.wh_total_widget.show()
        # 绑定左侧列表
        # self.calculator_type_listWidget.itemClicked.connect(self.bind_wh_sub_)
        self.show_wh_whdh_widget()
        self.clear_result(["元", "", ""])
        # self.sub_type_widget_(self.wh_type, self.sub_listWidget_list[0])
    def show_jj_widget(self):
        self.jj_type = ["jjsg", "jjsh","jjsy","jjcyq"]
        self.jj_total_widget.show()
        self.calculator_type_listWidget.itemClicked.connect(self.bind_jj_sub_)
        self.show_jj_jjsg_widget()
        self.clear_result(["元", "份","元"])
        self.sub_type_widget_(self.jj_type, self.sub_listWidget_list[0])

    def show_dk_widget(self):
        self.dk_total_widget.show()
        self.show_dk_grdk_widget()
        self.clear_result(["元", "元","元"])


    def show_zq_widget(self):
        self.zq_type = ["zqgm", "zqcs","zqcyq","gzmm","gzsy"]
        self.zq_total_widget.show()
        # 绑定左侧列表
        self.calculator_type_listWidget.itemClicked.connect(self.bind_zq_sub_)
        self.show_zq_zqgm_widget()
        self.clear_result(["%", "",""])
        self.sub_type_widget_(self.zq_type, self.sub_listWidget_list[0])

    def clear_result(self,p_l):
        for i in range(1, 4):
            getattr(self, f"ck_result_{i}").setText("")
            getattr(self, f"ck_result_p_{i}").setText(p_l[i-1])
        #显示对应的lb+result_lb

    def bind_ck_sub_(self,item):
        item_name=item.text()
        match (item_name):
            case "活期储蓄计算器":
                self.show_ck_hq_widget()
            case "整存整取计算器":
                self.show_ck_zczq_widget()
            case "零存整取计算器":
                self.show_ck_lczq_widget()
            case "整存零取计算器":
                self.show_ck_zclq_widget()
            case "存本取息计算器":
                self.show_ck_cbqx_widget()
            case "定活两便计算器":
                self.show_ck_dhlb_widget()
            case "通知存款计算器":
                self.show_ck_tzck_widget()
            case "教育储蓄计算器":
                self.show_ck_jycx_widget()
        self.sub_type_widget_(self.ck_type,item_name)

    def sub_type_widget_(self,type_list,item_name):
        for i in range(len(type_list)):
            if i==self.sub_listWidget_list.index(item_name):
                getattr(self,f"calculator_{type_list[i]}_widget").show()
            else:getattr(self,f"calculator_{type_list[i]}_widget").hide()

    # def bind_wh_sub_(self,item):
    #     item_name = item.text()
    #     match (item_name):
    #         case "外汇兑换计算器":
    #             self.show_wh_whdh_widget()
    #         case "外汇储蓄计算器":
    #             pass
    #             # self.show_wh_whcx_widget()
    #     self.sub_type_widget_(self.wh_type, item_name)

    def bind_sw_sub_(self,item):
        item_name = item.text()
        match (item_name):
            case "个人所得税计算器":
                self.show_sw_gr_widget()
            case "购房相关税费计算器":
                self.show_sw_gf_widget()
        self.sub_type_widget_(self.sw_type,item_name)

    def bind_jj_sub_(self,item):
        item_name = item.text()
        match (item_name):
            case "申(认)购费用计算器":
                self.show_jj_jjsg_widget()
            case "赎回费用计算器":
                self.show_jj_jjsh_widget()
            case "收益计算器":
                self.show_jj_jjsy_widget()
            case "持有期计算器":
                self.show_jj_jjcyq_widget()
        self.sub_type_widget_(self.jj_type, item_name)

    def bind_zq_sub_(self,item):
        item_name = item.text()
        match (item_name):
            case "债券购买收益率计算器":
                self.show_zq_zqgm_widget()
            case "债券出售收益率计算器":
                self.show_zq_zqcs_widget()
            case "债券持有期间收益率计算器":
                self.show_zq_zqcyq_widget()
            case "国债买卖计算器":
                self.show_zq_gzmm_widget()
            case "国债收益计算器":
                self.show_zq_gzsy_widget()
        self.sub_type_widget_(self.zq_type, item_name)
    def on_calculator_cal_clicked(self, input_name: str, date: QDate):
        try:
            input_widget = getattr(self, input_name)
            input_widget.setText(date.toString('yyyy-MM-dd'))
        except Exception as e:
            print(f"Error: {e}")

    def check_calculator_cal_(self,start_date:str,end_date:str,mode):
        print(mode,start_date,end_date)
        if mode=="1":
            try:
                if start_date == "" or end_date == "":
                    QMessageBox.information(self, "日期异常", "日期不能为空")
                    return False
                start_date = datetime.strptime(start_date, "%Y-%m-%d")
                end_date = datetime.strptime(end_date, "%Y-%m-%d")
                if end_date > start_date:
                    return int(str((end_date-start_date).days))
                else:
                    QMessageBox.information(self, "日期异常", "结束日期必须大于开始日期")
                    return False
            except ValueError:
                QMessageBox.information(self, "日期异常", "日期格式不合法")
                return False
        else:
            try:
                if start_date == "":
                    QMessageBox.information(self, "单日期异常", "日期不能为空")
                    return False
                start_date = datetime.strptime(start_date, "%Y-%m-%d")
                return str(start_date)
            except ValueError:
                QMessageBox.information(self, "单日期异常", "日期格式不合法")
                return False

    def check_calculator_num(self,input_str:str):
        try:
            if len(input_str)==0:
                QMessageBox.information(self, "数据异常", "数字不能为空")
                return False
            if float(input_str)>0:
                return True
            else:
                QMessageBox.information(self, "数据异常", "数字必须是正数")
                return False
        except Exception as e:
            QMessageBox.information(self, "日期异常", "数字不合法")
            return False

    def toggle_calculator_cal(self, event, status_name: str, cal_name: str):
        try:
            current_status = getattr(self, status_name)  # 获取当前状态
            if current_status:
                getattr(self, cal_name).hide()  # 隐藏控件
                setattr(self, status_name, False)  # 设置状态为 False
            else:
                getattr(self, cal_name).show()  # 显示控件
                setattr(self, status_name, True)  # 设置状态为 True
        except Exception as e:
            print(e)

    def hide_cal(self):
        self.ck_hq_calendarWidget_1.hide()
        self.ck_hq_calendarWidget_2.hide()
        self.ck_zczq_calendarWidget_1.hide()
        self.ck_lczq_calendarWidget_1.hide()
        self.ck_zclq_calendarWidget_1.hide()
        self.ck_cbqx_calendarWidget_1.hide()
        self.ck_dhlb_calendarWidget_1.hide()
        self.ck_dhlb_calendarWidget_2.hide()
        self.ck_tzck_calendarWidget_1.hide()
        self.ck_tzck_calendarWidget_2.hide()
        self.ck_jycx_calendarWidget_1.hide()
        self.jj_jjcyq_calendarWidget_1.hide()
        self.jj_jjcyq_calendarWidget_2.hide()

    def show_wh_whdh_widget(self):
        try:
            # self.wh_new_value=0
            self.wh_webEngineView.hide()
            self.wh_time_widget.show()
            self.hide_whdh_widget("whdh_widget")
            self.hide_whdh_widget("whdh_widget_3")
            self.wh_type_1 = "美元"
            self.wh_type_2 = "人民币"
            self.wh_code_1 = "usd"
            self.wh_code_2 = "cny"
            self.length_ = 2
            self.unit_ = "day"
            self.wh_whdh_input_2.setText("USD - 美元")
            self.wh_whdh_input_3.setText("CNY - 人民币")
            self.update_result_lb(["兑换回的货币数量：", "", ""])
            self.wh_img_1.load(QUrl("http://tool.huiruisoft.com/country/images/48x48/us.png"))
            self.wh_img_2.load(QUrl("http://tool.huiruisoft.com/country/images/48x48/cn.png"))
            self.clear_result(["元", "", ""])
            self.calculator_result_btn.mousePressEvent = self.return_result_wh_whdh
            self.wh_whdh_input_2.mousePressEvent = self.custom_mouse_press_whdh#点
            self.wh_whdh_input_2.textChanged.connect(partial(self.show_whdh_listWidget,listWidget_name="whdh_listWidget",input_name="wh_whdh_input_2"))#点
            self.wh_whdh_input_3.mousePressEvent = self.custom_mouse_press_whdh_2  # 点
            self.wh_whdh_input_3.textChanged.connect(partial(self.show_whdh_listWidget,listWidget_name="whdh_listWidget_3",input_name="wh_whdh_input_3"))  # 点
            self.whdh_listWidget.itemClicked.connect(partial(self.insert_input,widget_name="whdh_widget",input_name="wh_whdh_input_2",img_name="wh_img_1",type_name="wh_type_1",wh_code="wh_code_1"))
            self.whdh_listWidget_3.itemClicked.connect(partial(self.insert_input,widget_name="whdh_widget_3",input_name="wh_whdh_input_3",img_name="wh_img_2",type_name="wh_type_2",wh_code="wh_code_2"))
            self.load_wh_web(source=self.wh_code_1,
                             target=self.wh_code_2, length=self.length_, unit= self.unit_)
            def clear_wh_whdh(event):
                self.show_wh_whdh_widget()
                self.wh_whdh_input_1.setText("")
            self.calculator_reset_btn.mousePressEvent = clear_wh_whdh
            self.length_unit_lsit=["2-day","7-day","30-day","6-month","1-year","5-year"]
            self.wh_time_step_lb_1.setStyleSheet(""" QLabel{background-color:#483D8B;color:white}QLabel:hover{
                                            background-color:#E6E6FA;
                                            }""")
            for i in range(1, 7):
                getattr(self, f"wh_time_step_lb_{i}").setStyleSheet(""" 
                                                 QLabel{background-color:none;}
                                                QLabel:hover{
                                                background-color:#E6E6FA;
                                                color:black;
                                                }""")
            self.wh_time_step_lb_1.setStyleSheet(""" QLabel{background-color:#483D8B;color:white}QLabel:hover{
                                                        background-color:#E6E6FA;
                                                        color:black
                                                        }""")
            for i in range(1,7):
                getattr(self,f"wh_time_step_lb_{i}").mousePressEvent=partial(self.select_time_step,index=i-1)
        except Exception as e:
            print(e)

    def select_time_step(self,event,index):
        for i in range(1, 7):
            getattr(self, f"wh_time_step_lb_{i}").setStyleSheet(""" 
                                             QLabel{background-color:none;}
                                            QLabel:hover{
                                            background-color:#E6E6FA;
                                            color:black;
                                            }""")
        self.length_=int(self.length_unit_lsit[index].split("-")[0])
        self.unit_=self.length_unit_lsit[index].split("-")[1]
        getattr(self, f"wh_time_step_lb_{index+1}").setStyleSheet(""" QLabel{background-color:#483D8B;color:white}QLabel:hover{
                                            background-color:#E6E6FA;
                                             color:black
                                            }""")
        self.load_wh_web(source=get_exchange.extraction_code(self.wh_code_1),
                         target=get_exchange.extraction_code(self.wh_code_2), length=self.length_, unit=self.unit_)



    def insert_input(self,item: QListWidgetItem,widget_name:QWidget,input_name:str,img_name:str,type_name,wh_code:str):
        item_name=item.text()
        # if "1" in img_name:
        #     self.wh_code_1=item_name
        # elif "2" in img_name:
        #     self.wh_code_2 = item_name
        getattr(self,f"{input_name}").setText(item_name)
        setattr(self,f"{wh_code}",item_name)
        self.hide_whdh_widget(widget_name)
        url="http://tool.huiruisoft.com/country/images/48x48/"+get_exchange.extraction_code(item_name)[:2]+".png"
        getattr(self,f"{img_name}").load(QUrl(url))
        setattr(self,f"{type_name}",get_exchange.extraction_name(item_name))
        self.wh_tip_lb.setText(f"当前 {self.wh_type_1} 兑换 { self.wh_type_2} 汇率:")
        self.load_wh_web(source=get_exchange.extraction_code(self.wh_code_1),target=get_exchange.extraction_code(self.wh_code_2),length=self.length_,unit=self.unit_)

    def load_wh_web(self,source:str,target:str,length,unit):
        try:
            self.worker_thread_wh=get_exchange_html(source=source,target=target,length=length,unit=unit)
            self.worker_thread_wh.finished.connect(self.task_finished_wh)
            self.worker_thread_wh.start()
            self.wh_tip_text.setText("正在获取最新汇率")
            self.wh_webEngineView.hide()
        except Exception as e:
            print(e)


    def task_finished_wh(self,web_data,new_value):
        # self.wh_new_value=0.1
        self.wh_webEngineView.setHtml(web_data)
        self.wh_new_value=new_value
        self.wh_tip_text.setText(
            f"1 {get_exchange.extraction_code(self.wh_code_1).upper()} = {self.wh_new_value} {get_exchange.extraction_code(self.wh_code_2).upper()}")
        self.wh_webEngineView.show()

    def custom_mouse_press_whdh(self,event):
        super(QLineEdit, self.wh_whdh_input_2).mousePressEvent(event)  # 调用父类方法，确保正常行为
        self.whdh_widget.show()
        self.show_whdh_listWidget("", "whdh_listWidget", "wh_whdh_input_2")

    def custom_mouse_press_whdh_2(self,event):
        super(QLineEdit, self.wh_whdh_input_3).mousePressEvent(event)  # 调用父类方法，确保正常行为
        self.whdh_widget_3.show()
        self.show_whdh_listWidget("", "whdh_listWidget_3", "wh_whdh_input_3")

    def show_whdh_listWidget(self,query,listWidget_name,input_name):
        try:
            getattr(self,f"{listWidget_name}").clear()
            if query=="":
                match_list = get_exchange.match_code_letter(query)
                for i in match_list:
                    getattr(self,f"{listWidget_name}").addItem(i)
            elif query in get_exchange.get_currency_list():
                pass
            else:
                if get_exchange.input_type(query)=="code":
                    match_list=get_exchange.match_code_letter(query)
                    for i in match_list:
                        getattr(self,f"{listWidget_name}").addItem(i)
                elif get_exchange.input_type(query)=="text":
                    match_list = get_exchange.match_code_text(query)
                    for i in match_list:
                        getattr(self, f"{listWidget_name}").addItem(i)
                else:
                    print(query)
                    QMessageBox.information(self, "输入有误", "输入只能是字母或汉字")
                    getattr(self, f"{input_name}").setText("")
        except Exception as e:
            print(e)

    def hide_whdh_widget(self,widget_name):
        getattr(self,f"{widget_name}").hide()

    def return_result_wh_whdh(self,event):
        money = self.wh_whdh_input_1.text()
        if self.check_calculator_num(money):
            self.ck_result_1.setText("{:.2f}".format(float(money)*self.wh_new_value))

    def show_zq_zqgm_widget(self):
        try:
            self.update_result_lb(["债券收益率：", "", ""])
            self.clear_result(["%", "", ""])
            self.calculator_result_btn.mousePressEvent = self.return_result_zq_zqgm
            def clear_zq_zqgm(event):
                self.clear_result(["%", "", ""])
                for i in range(1,5):
                    getattr(self,f"zq_zqgm_input_{i}").setText("")
            self.calculator_reset_btn.mousePressEvent = clear_zq_zqgm
        except Exception as e:
            print(e)

    def return_result_zq_zqgm(self, event):
        try:
            zq_money = self.zq_zqgm_input_1.text()
            buy_money = self.zq_zqgm_input_2.text()  # 单位净值
            date_ = self.zq_zqgm_input_3.text()  # 单位净值
            rate = self.zq_zqgm_input_4.text()  # 单位净值
            if self.check_calculator_num(zq_money) and self.check_calculator_num(buy_money) and self.check_calculator_num(rate):
                buy_money=float(buy_money)
                zq_money=float(zq_money)
                rate=float(rate)/100
                date_=int(date_)
                ins=(zq_money*rate*(date_/365)+(zq_money-buy_money))/buy_money*100
                result1 =round(ins,2)
                self.ck_result_1.setText(str(result1))
        except Exception as e:
            print(e)

    def show_zq_zqcs_widget(self):
        try:
            self.update_result_lb(["债券出售收益率：", "", ""])
            self.clear_result(["%", "", ""])
            self.calculator_result_btn.mousePressEvent = self.return_result_zq_zqcs
            def clear_zq_zqcs(event):
                self.clear_result(["%", "", ""])
                for i in range(1,5):
                    getattr(self,f"zq_zqcs_input_{i}").setText("")
            self.calculator_reset_btn.mousePressEvent = clear_zq_zqcs
        except Exception as e:
            print(e)

    def return_result_zq_zqcs(self, event):
        try:
            public_money = self.zq_zqcs_input_1.text()
            sell_money = self.zq_zqcs_input_2.text()  # 单位净值
            date_ = self.zq_zqcs_input_3.text()  # 单位净值
            rate = self.zq_zqcs_input_4.text()  # 单位净值
            if self.check_calculator_num(public_money) and self.check_calculator_num(sell_money) and self.check_calculator_num(rate):
                public_money=float(public_money)
                sell_money=float(sell_money)
                rate=float(rate)/100
                date_=int(date_)
                # 计算单利利息（按实际天数/365）
                interest = public_money * rate * (date_/ 365)
                # 计算资本利得
                capital_gain = sell_money - public_money
                # 计算总收益和持有期收益率
                total_return = interest + capital_gain
                holding_period_yield = total_return / public_money
                annualized_return = round(holding_period_yield/ (date_ / 365)*100,2)
                self.ck_result_1.setText(str(annualized_return))
        except Exception as e:
            print(e)
    def show_zq_zqcyq_widget(self):
        try:
            self.update_result_lb(["债券持有期间收益率：", "", ""])
            self.clear_result(["%", "", ""])
            self.calculator_result_btn.mousePressEvent = self.return_result_zq_zqcyq
            def clear_zq_zqcyq(event):
                self.clear_result(["%", "", ""])
                for i in range(1,5):
                    getattr(self,f"zq_zqcyq_input_{i}").setText("")
            self.calculator_reset_btn.mousePressEvent = clear_zq_zqcyq
        except Exception as e:
            print(e)

    def return_result_zq_zqcyq(self, event):
        try:
            face_value = self.zq_zqcyq_input_1.text()
            buy_price = self.zq_zqcyq_input_2.text()
            sell_price = self.zq_zqcyq_input_3.text()  # 单位净值
            rate = self.zq_zqcyq_input_4.text()  # 单位净值
            date_ = self.zq_zqcyq_input_5.text()  # 单位净值
            if self.check_calculator_num(face_value) and self.check_calculator_num(buy_price) and self.check_calculator_num(sell_price):
                face_value=float(face_value)
                buy_price=float(buy_price)
                sell_price=float(sell_price)
                rate=float(rate)/100
                date_=int(date_)
                simple_interest = face_value * rate * (date_ / 365)
                capital_gain = sell_price - buy_price
                total_return_simple = simple_interest + capital_gain
                holding_period_yield_simple = total_return_simple / buy_price  # HPY（持有期收益率）
                annualized_return_simple = f"{holding_period_yield_simple / (date_ / 365)*100}"
                self.ck_result_1.setText(str(annualized_return_simple))
        except Exception as e:
            print(e)

    def show_zq_gzmm_widget(self):
        try:
            self.update_result_lb(["金额：", "", ""])
            self.clear_result(["元", "", ""])
            self.calculator_result_btn.mousePressEvent = self.return_result_zq_gzmm
            def clear_zq_gzmm(event):
                self.clear_result(["元", "", ""])
                for i in range(3,5):
                    getattr(self,f"zq_gzmm_input_{i}").setText("")
            self.calculator_reset_btn.mousePressEvent = clear_zq_gzmm
        except Exception as e:
            print(e)

    def return_result_zq_gzmm(self, event):
        try:
            price = self.zq_gzmm_input_3.text()
            num = self.zq_gzmm_input_4.text()
            if self.check_calculator_num(price) and self.check_calculator_num(num):
                self.ck_result_1.setText(str(float(num)*float(price)))
        except Exception as e:
            print(e)

    def show_zq_gzsy_widget(self):
        try:
            self.update_result_lb(["国债利息：", "本息合计：", ""])
            self.clear_result(["元", "元", ""])
            self.zq_gzsy_input_1.currentTextChanged.connect(self.gzsy_combox_select)
            self.calculator_result_btn.mousePressEvent = self.return_result_zq_gzsy
            def clear_zq_gzsy(event):
                self.clear_result(["元", "元", ""])
                for i in range(2,4):
                    getattr(self,f"zq_gzsy_input_{i}").setText("")
            self.calculator_reset_btn.mousePressEvent = clear_zq_gzsy
        except Exception as e:
            print(e)

    def return_result_zq_gzsy(self, event):
        try:
            if self.zq_gzsy_input_1.currentIndex()==0:
                self.gzsy_time_ = 2
            money = self.zq_gzsy_input_2.text()
            rate= self.zq_gzsy_input_3.text()
            if self.check_calculator_num(money) and self.check_calculator_num(rate):
                result1=float(money)*float(rate)/100*self.gzsy_time_
                self.ck_result_1.setText(str(result1))
                self.ck_result_2.setText(str(result1+float(money)))
        except Exception as e:
            print(e)

    def gzsy_combox_select(self, text):
        match (text):
            case "2年期":
                self.gzsy_time_=2
            case "3年期":
                self.gzsy_time_ = 3
            case "5年期":
                self.gzsy_time_ = 5
            case "10年期":
                self.gzsy_time_ = 10

    def show_dk_grdk_widget(self):
        try:
            self.plainTextEdit_2.hide()
            self.update_result_lb(["每月支付本息：", "累计支付利息：", "累计还款总额："])
            self.clear_result(["元", "元", "元"])
            self.dk_grdk_input_3.currentTextChanged.connect(self.grdk_combox_select)
            self.calculator_result_btn.mousePressEvent = self.return_result_dk_grdk
            self.dk_grdk_input_4.setText("6.15")
            def clear_dk_grdk(event):
                self.clear_result(["元", "元", "元"])
                self.dk_grdk_input_1.setText("")
                self.dk_grdk_input_2.setText("")
                self.dk_grdk_input_4.setText("6.15")
                self.plainTextEdit_2.hide()
            self.calculator_reset_btn.mousePressEvent = clear_dk_grdk
        except Exception as e:
            print(e)

    def return_result_dk_grdk(self, event):
        try:
            result = []
            money = self.dk_grdk_input_1.text()
            year_ = self.dk_grdk_input_2.text()
            rate = self.dk_grdk_input_4.text()
            if self.dk_grdk_input_3.currentIndex()==0:
                self.repayment_type = "bx"
            if self.check_calculator_num(money) and self.check_calculator_num(year_) and self.check_calculator_num(rate):
                """
                    计算等额本息还款的相关信息
                    :param P: 贷款本金
                    :param i: 年利率
                    :param n: 还款总月数
                    :return: 每月还款额，累计支付利息，累计还款总额
                    """
                if self.repayment_type=="bx":
                    self.plainTextEdit_2.hide()
                    # 计算月利率
                    r = float(rate) / 1200
                    n=int(year_)*12
                    # 计算每月还款额
                    A = float(money) * r * (1 + r) ** n / ((1 + r) ** n - 1)
                    # 计算累计还款总额
                    result3 = A * n
                    # 计算累计支付利息
                    result2 = result3 - float(money)
                    result1=A
                    result = [result1, result2, result3]
                    for i in range(1, 4):
                        getattr(self, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
                else:
                    """
                        计算等额本金还款的每月支付本息、累计支付利息和累计还款总额
                        :param P: 贷款本金
                        :param annual_interest_rate: 年利率
                        :param years: 还款年限
                        """
                    self.plainTextEdit_2.show()
                    str1=""
                    # 计算月利率
                    monthly_interest_rate = float(rate)/ 1200
                    # 计算还款总月数
                    total_months = int(year_) * 12
                    # 计算每月偿还本金
                    monthly_principal = float(money) / total_months
                    total_interest_paid = 0
                    total_repayment = 0

                    # 遍历每个月
                    for month in range(1, total_months + 1):
                        # 计算当月利息
                        monthly_interest = (float(money)  - monthly_principal * (month - 1)) * monthly_interest_rate
                        # 计算当月还款额
                        monthly_repayment = monthly_principal + monthly_interest
                        # 累计支付利息和累计还款总额
                        total_interest_paid += monthly_interest
                        total_repayment += monthly_repayment
                        # 输出当月信息
                        str1+=f"{month}月:{monthly_repayment:.2f}元，累计支付利息:{total_interest_paid:.2f}元.\n"
                    result1=str1
                    result2=total_interest_paid
                    result3=total_repayment
                    result = [result1, result2, result3]
                    self.plainTextEdit_2.setPlainText(result1)
                    for i in range(2, 4):
                        getattr(self, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
        except Exception as e:
            print(e)

    def grdk_combox_select(self, text):
        match (text):
            case "等额本息还款":
                self.repayment_type="bx"
            case "等额本金还款":
                self.repayment_type = "bj"


    def show_jj_jjsg_widget(self):
        try:
            self.update_result_lb(["手续费：", "成交份额：", "准申购金额："])
            self.clear_result(["元", "份", "元"])
            self.calculator_result_btn.mousePressEvent = self.return_result_jj_jjsg
            def clear_jj_jjsg(event):
                self.clear_result(["元", "份", "元"])
                self.jj_jjsg_input_1.setText("0")
                self.jj_jjsg_input_2.setText("0")
                self.jj_jjsg_input_3.setText("0")
            self.calculator_reset_btn.mousePressEvent = clear_jj_jjsg
        except Exception as e:
            print(e)

    def return_result_jj_jjsg(self, event):
        try:
            result = []
            money = self.jj_jjsg_input_1.text()
            dwjz = self.jj_jjsg_input_2.text()#单位净值
            rate = self.jj_jjsg_input_3.text()#单位净值
            if self.check_calculator_num(money) and self.check_calculator_num(dwjz) and self.check_calculator_num(rate):
                result3= float(money)/(1+float(rate)/100)#净申购金额
                result1 = (float(money) - result3)  # 手续费
                result2 = float(result3) /float(dwjz)  # 成交份额
                result = [result1, result2,result3 ]
            for i in range(1, 4):
                getattr(self, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
        except Exception as e:
            print(e)

    def show_jj_jjsh_widget(self):
        try:
            self.update_result_lb(["手续费：", "确认金额:", ""])
            self.clear_result(["元", "份", ""])
            self.calculator_result_btn.mousePressEvent = self.return_result_jj_jjsh
            def clear_jj_jjsh(event):
                self.clear_result(["元", "份", ""])
                self.jj_jjsh_input_1.setText("0")
                self.jj_jjsh_input_2.setText("0")
                self.jj_jjsh_input_3.setText("0")
            self.calculator_reset_btn.mousePressEvent = clear_jj_jjsh
        except Exception as e:
            print(e)

    def return_result_jj_jjsh(self, event):
        try:
            p_num = self.jj_jjsh_input_1.text()
            dwjz = self.jj_jjsh_input_2.text()  # 单位净值
            rate = self.jj_jjsh_input_3.text()  # 单位净值
            if self.check_calculator_num(p_num) and self.check_calculator_num(dwjz) and self.check_calculator_num(rate):
                result1 = float(p_num)*float(dwjz)*float(rate)/100# 手续费
                result2 =float(p_num)*float(dwjz)-result1 # 确认金额
                result = [result1, result2]
                for i in range(1, 3):
                    getattr(self, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
        except Exception as e:
            print(e)

    def show_jj_jjsy_widget(self):
        try:
            self.update_result_lb(["持有期总收益率：", "持有期年化收益率：", ""])
            self.clear_result(["%", "%", ""])
            self.calculator_result_btn.mousePressEvent = self.return_result_jj_jjsy
            def clear_jj_jjsy(event):
                self.clear_result(["%", "%", ""])
                self.jj_jjsy_input_1.setText("0")
                self.jj_jjsy_input_2.setText("0")
                self.jj_jjsy_input_3.setText("0")
            self.calculator_reset_btn.mousePressEvent = clear_jj_jjsy
        except Exception as e:
            print(e)

    def return_result_jj_jjsy(self, event):
        try:
            s_money = self.jj_jjsy_input_1.text()
            e_money = self.jj_jjsy_input_2.text()  # 单位净值
            date_ = self.jj_jjsy_input_3.text()  # 单位净值
            if self.check_calculator_num(s_money) and self.check_calculator_num(e_money) and self.check_calculator_num(date_):
                result1 = (float(e_money)-float(s_money))/float(s_money)*100
                result2 = result1*365/float(date_)
                result = [result1, result2]
                for i in range(1, 3):
                    getattr(self, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
        except Exception as e:
            print(e)

    def show_jj_jjcyq_widget(self):
        try:
            self.update_result_lb(["持有天数：", "持有月数", "持有年数"])
            self.clear_result(["天", "月", "年"])
            self.jj_jjcyq_cal_1_status = False
            self.jj_jjcyq_cal_2_status = False
            self.jj_jjcyq_cal1.setPixmap(QPixmap(self.img_path + r'\cal1.png').scaled(42, 42))
            self.jj_jjcyq_cal2.setPixmap(QPixmap(self.img_path + r'\cal1.png').scaled(42, 42))
            self.jj_jjcyq_cal1.mousePressEvent = partial(self.toggle_calculator_cal,
                                                         status_name="jj_jjcyq_cal_1_status",
                                                         cal_name="jj_jjcyq_calendarWidget_1")
            self.jj_jjcyq_cal2.mousePressEvent = partial(self.toggle_calculator_cal,
                                                         status_name="jj_jjcyq_cal_2_status",
                                                         cal_name="jj_jjcyq_calendarWidget_2")
            self.jj_jjcyq_calendarWidget_1.clicked.connect(
                lambda date: self.on_calculator_cal_clicked("jj_jjcyq_input_1", date)
            )
            self.jj_jjcyq_calendarWidget_2.clicked.connect(
                lambda date: self.on_calculator_cal_clicked("jj_jjcyq_input_2", date)
            )
            self.hide_cal()
            self.jj_inner_list = [datetime.today().strftime("%Y-%m-%d"), ""]
            for i in range(1,3):
                getattr(self, f"jj_jjcyq_input_{i}").setText(self.jj_inner_list[i - 1])
            self.calculator_result_btn.mousePressEvent = self.return_result_jj_jjcyq
            def clear_jj_jjcyq(event):
                self.clear_result(["天", "月", "年"])
                self.jj_jjcyq_input_1.setText(datetime.today().strftime("%Y-%m-%d"))
                self.jj_jjcyq_input_2.setText("")
            self.calculator_reset_btn.mousePressEvent = clear_jj_jjcyq
        except Exception as e:
            print(e)

    def return_result_jj_jjcyq(self, event):
        try:
            start_date = self.jj_jjcyq_input_1.text()
            end_date = self.jj_jjcyq_input_2.text()
            if self.check_calculator_cal_(start_date,end_date,"1") and  self.check_calculator_cal_(start_date,end_date,"1"):
                result1 = self.check_calculator_cal_(start_date,end_date,"1")
                result2 =self.check_calculator_cal_(start_date,end_date,"1")/30
                result3=self.check_calculator_cal_(start_date,end_date,"1")/365
                result = [result1, result2,result3]
                for i in range(1, 4):
                    getattr(self, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
        except Exception as e:
            print(e)

    def show_sw_gf_widget(self):
        try:
            self.sw_gz_status=True
            self.update_result_lb (["房款总价：", "印花税：", "契税："])
            self.clear_result(["元", "元", "元"])
            self.sw_inner_list = ["",""]
            for i in range(1,3):
                getattr(self,f"sw_gf_input_{i}").setText(self.sw_inner_list[i-1])
            self.calculator_result_btn.mousePressEvent=self.return_result_sw_gf
            def clear_sw_gf(event):
                self.clear_result(["元", "元", "元"])
                self.sw_gf_input_1.setText("")
                self.sw_gf_input_2.setText("")
            self.calculator_reset_btn.mousePressEvent=clear_sw_gf
        except Exception as e:
            print(e)

    def return_result_sw_gf(self,event):
        try:
            result=[]
            money = self.sw_gf_input_1.text()
            area=self.sw_gf_input_2.text()
            if self.check_calculator_num(money) and self.check_calculator_num(area):
                result1=float(money)*float(area)
                result2=result1*0.0005
                result3=result1*0.015
                result = [result1, result2, result3]
            for i in range(1, 4):
                getattr(self, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
        except Exception as e:
            print(e)

    def show_sw_gr_widget(self):
        try:
            self.sw_rate = 3
            self.sw_gz_status=True
            self.update_result_lb (["适用税率:", "应交税款：", "实得收入："])
            self.clear_result(["%", "元", "元"])
            self.sw_inner_list = ["","0","5000",""]
            for i in range(1,4):
                getattr(self,f"sw_gr_input_{i}").setText(self.sw_inner_list[i-1])
            self.calculator_result_btn.mousePressEvent=self.return_result_sw_gr
            def clear_sw_gr(event):
                self.clear_result(["%", "元", "元"])
                self.sw_gr_input_1.setText("")
                self.sw_gr_input_2.setText("0")
            self.calculator_reset_btn.mousePressEvent=clear_sw_gr
        except Exception as e:
            print(e)

    def return_result_sw_gr(self,event):
        try:
            result=[]
            money = self.sw_gr_input_1.text()
            sb_money=self.sw_gr_input_2.text()
            start_money=self.sw_gr_input_3.text()
            self.sw_gr_input_4.currentTextChanged.connect(self.gr_combox_select)
            if self.check_calculator_num(money) and self.sw_gz_status:
                result1 = self.sw_rate
                if float(money)>float(start_money):
                    result2 = self.sw_rate / 100 * (float(money)-float(start_money)-float(sb_money))
                else:
                    result1=0
                    result2=0
                result3 = float(money) - result2-float(sb_money)
                result = [result1, result2, result3]
            elif self.check_calculator_num(money) and self.sw_gz_status==False:
                result1 = self.sw_rate
                result2 = self.sw_rate/100*float(money)
                result3=float(money)-result2
                result = [result1, result2, result3]
            for i in range(1, 4):
                getattr(self, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
        except Exception as e:
            print(e)

    def hide_show_sw_gr(self,status):
        if status:
            self.sw_item_lb_2.show()
            self.sw_item_lb_3.show()
            self.sw_gr_input_2.show()
            self.sw_gr_input_3.show()
        else:
            self.sw_item_lb_2.hide()
            self.sw_item_lb_3.hide()
            self.sw_gr_input_2.hide()
            self.sw_gr_input_3.hide()

    def gr_combox_select(self,text):
        match (text):
            case "工资、薪金所得":
                self.sw_rate = 3
                self.sw_gz_status=True
                self.hide_show_sw_gr(True)
            case "个体工商户生产、经营所得":
                self.sw_gz_status = False
                self.sw_rate = 5
                self.hide_show_sw_gr(False)
            case "对企事业单位的承包经营、承租经营所得":
                self.sw_gz_status = False
                self.sw_rate = 35
                self.hide_show_sw_gr(False)
            case "劳务报酬所得":
                self.sw_gz_status = False
                self.sw_rate = 20
                self.hide_show_sw_gr(False)
            case "稿酬所得":
                self.sw_gz_status = False
                self.sw_rate = 14
                self.hide_show_sw_gr(False)
            case "特许权使用所得":
                self.sw_gz_status = False
                self.sw_rate = 20
                self.hide_show_sw_gr(False)
            case "利息、股息、红利所得":
                self.sw_gz_status = False
                self.sw_rate = 20
                self.hide_show_sw_gr(False)
            case "财产租赁所得":
                self.sw_gz_status = False
                self.sw_rate = 20
                self.hide_show_sw_gr(False)
            case "财产转让所得":
                self.sw_gz_status = False
                self.sw_rate = 20
                self.hide_show_sw_gr(False)
            case "偶然所得(如: 中奖、中彩)":
                self.sw_gz_status = False
                self.sw_rate = 20
                self.hide_show_sw_gr(False)
            case "个人拍卖所得":
                self.sw_gz_status = False
                self.hide_show_sw_gr(False)
                self.sw_rate = 20

    def show_ck_hq_widget(self):
        try:
            self.update_result_lb (["实得本息总额：", "存入本金总额：", "扣除利息税金额："])
            self.ck_hq_cal_1_status=False
            self.ck_hq_cal_2_status=False
            self.clear_result(["元", "元", "元"])
            self.ck_hq_cal1.setPixmap(QPixmap(self.img_path + r'\cal1.png').scaled(42, 42))
            self.ck_hq_cal2.setPixmap(QPixmap(self.img_path + r'\cal1.png').scaled(42, 42))
            self.ck_hq_cal1.mousePressEvent = partial(self.toggle_calculator_cal, status_name="ck_hq_cal_1_status",
                                                      cal_name="ck_hq_calendarWidget_1")
            self.ck_hq_cal2.mousePressEvent = partial(self.toggle_calculator_cal, status_name="ck_hq_cal_2_status",
                                                      cal_name="ck_hq_calendarWidget_2")
            self.ck_hq_calendarWidget_1.clicked.connect(
                lambda date: self.on_calculator_cal_clicked("ck_hq_input_1", date)
            )
            self.ck_hq_calendarWidget_2.clicked.connect(
                lambda date: self.on_calculator_cal_clicked("ck_hq_input_4", date)
            )
            self.hide_cal()
            self.ck_hq_widget.show()
            self.ck_inner_list = [datetime.today().strftime("%Y-%m-%d"),"0","0.35",""]
            for i in range(1,5):
                getattr(self,f"ck_hq_input_{i}").setText(self.ck_inner_list[i-1])
            self.calculator_result_btn.mousePressEvent=self.return_result_ck_hq
            def clear_ck_hq(event):
                self.clear_result(["元", "元", "元"])
                self.ck_hq_input_2.setText("")
                self.ck_hq_input_4.setText("")
            self.calculator_reset_btn.mousePressEvent=clear_ck_hq
        except Exception as e:
            print(e)

    def return_result_ck_hq(self,event):
        try:
            start_date = self.ck_hq_input_1.text()
            money = self.ck_hq_input_2.text()
            rate = self.ck_hq_input_3.text()
            end_date = self.ck_hq_input_4.text()
            if self.check_calculator_cal_(start_date, end_date,"1")!=False and self.check_calculator_num(money) and self.check_calculator_num(rate):
                day_num = self.check_calculator_cal_(start_date, end_date,"1")
                result1 = day_num * float(rate) / 36500 * float(money) + float(money)
                result2 =  float(money)
                result3 = 0
                result = [result1, result2, result3, ]
                for i in range(1, 4):
                    getattr(self, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
        except Exception as e:
            print(e)

    def update_result_lb(self,result_lb_list:list):
        for i in range(1, len(result_lb_list) + 1):
            getattr(self, f"ck_result_lb_{i}").setText(result_lb_list[i - 1])

    def show_ck_zczq_widget(self):
        try:
            self.zczq_time_d = 3
            self.clear_result(["元", "元", ""])
            self.update_result_lb (["到期本息总额:", "扣除利息税金额:",""])
            self.ck_zczq_cal_1_status=False
            self.ck_zczq_cal1.setPixmap(QPixmap(self.img_path + r'\cal1.png').scaled(42, 42))
            self.ck_zczq_cal1.mousePressEvent = partial(self.toggle_calculator_cal, status_name="ck_zczq_cal_1_status",
                                                    cal_name="ck_zczq_calendarWidget_1")
            self.ck_zczq_calendarWidget_1.clicked.connect(
                lambda date: self.on_calculator_cal_clicked("ck_zczq_input_1", date)
            )
            self.hide_cal()
            self.ck_inner_list = [datetime.today().strftime("%Y-%m-%d"),"0","1.1",]
            for i in range(1,4):
                getattr(self,f"ck_zczq_input_{i}").setText(self.ck_inner_list[i-1])
            self.calculator_result_btn.mousePressEvent=self.return_result_ck_zczq
            def clear_ck_hq(event):
                self.clear_result(["元", "元", ""])
                self.ck_hq_input_2.setText("")
            self.calculator_reset_btn.mousePressEvent=clear_ck_hq
        except Exception as e:
            print(e)
    def return_result_ck_zczq(self,event):
        try:
            start_date = self.ck_zczq_input_1.text()
            money = self.ck_zczq_input_2.text()
            rate = self.ck_zczq_input_3.text()
            self.ck_zczq_input_4.currentTextChanged.connect(self.zczq_combox_select)
            if self.check_calculator_cal_(start_date,"",0) and self.check_calculator_num(money) and self.check_calculator_num(rate):
                result1 = float(rate) / 1200 *self.zczq_time_d* float(money) + float(money)
                result2 =0
                result = [result1, result2, ]
                for i in range(1, 3):
                    getattr(self, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
        except Exception as e:
            print(e)

    def zczq_combox_select(self,text):
        match (text):
            case "三个月":
                self.zczq_time_d=3
            case "半年":
                self.zczq_time_d = 6
            case "一年":
                self.zczq_time_d = 12
            case "二年":
                self.zczq_time_d= 24
            case "三年":
                self.zczq_time_d = 36
            case "五年":
                self.zczq_time_d = 60

    def show_ck_lczq_widget(self):
        try:
            self.lczq_time_d = 12
            self.clear_result(["元", "元", ""])
            self.update_result_lb(["到期本息总额:", "扣除利息税金额:", ""])
            self.ck_lczq_cal_1_status = False
            self.ck_lczq_cal1.setPixmap(QPixmap(self.img_path + r'\cal1.png').scaled(42, 42))
            self.ck_lczq_cal1.mousePressEvent = partial(self.toggle_calculator_cal, status_name="ck_lczq_cal_1_status",
                                                        cal_name="ck_lczq_calendarWidget_1")
            self.ck_lczq_calendarWidget_1.clicked.connect(
                lambda date: self.on_calculator_cal_clicked("ck_lczq_input_1", date)
            )
            self.hide_cal()
            self.ck_inner_list = [datetime.today().strftime("%Y-%m-%d"), "0", "1.35", ]
            for i in range(1, 4):
                getattr(self, f"ck_lczq_input_{i}").setText(self.ck_inner_list[i - 1])
            self.calculator_result_btn.mousePressEvent = self.return_result_ck_lczq
            def clear_ck_lczq(event):
                self.clear_result(["元", "元", ""])
                self.ck_lczq_input_2.setText("")

            self.calculator_reset_btn.mousePressEvent = clear_ck_lczq
        except Exception as e:
            print(e)

    def return_result_ck_lczq(self, event):
        try:
            start_date = self.ck_lczq_input_1.text()
            money = self.ck_lczq_input_2.text()
            rate = self.ck_lczq_input_3.text()
            self.ck_lczq_input_4.currentTextChanged.connect(self.lczq_combox_select)
            if self.check_calculator_cal_(start_date, "", 0) and self.check_calculator_num(
                    money) and self.check_calculator_num(rate):
                annual_rate = float(rate)/100
                months = self.lczq_time_d
                monthly_amount = float(money)
                monthly_rate = annual_rate / 12  # 月利率
                total_interest = 0
                for month in range(1, months + 1):
                    total_interest += monthly_amount * monthly_rate * (months - month + 1)
                total_amount = monthly_amount * months + total_interest
                result1 = total_amount
                result2 = 0
                result = [result1, result2, ]
                for i in range(1, 3):
                    getattr(self, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
        except Exception as e:
            print(e)

    def lczq_combox_select(self, text):
        match (text):
            case "一年":
                self.lczq_time_d = 12
            case "三年":
                self.lczq_time_d = 36
            case "五年":
                self.lczq_time_d = 60

    def show_ck_zclq_widget(self):
        try:
            self.zclq_time_d = 12
            self.take_fq=12
            self.clear_result(["元", "元", "元"])
            self.update_result_lb(["每次支取金额:", "所得利息金额:","扣除利息税金额:"])
            self.ck_zclq_cal_1_status = False
            self.ck_zclq_cal1.setPixmap(QPixmap(self.img_path + r'\cal1.png').scaled(42, 42))
            self.ck_zclq_cal1.mousePressEvent = partial(self.toggle_calculator_cal, status_name="ck_zclq_cal_1_status",
                                                        cal_name="ck_zclq_calendarWidget_1")
            self.ck_zclq_calendarWidget_1.clicked.connect(
                lambda date: self.on_calculator_cal_clicked("ck_zclq_input_1", date)
            )
            self.hide_cal()
            self.ck_inner_list = [datetime.today().strftime("%Y-%m-%d"), "0", "2.85", ]
            for i in range(1, 4):
                getattr(self, f"ck_zclq_input_{i}").setText(self.ck_inner_list[i - 1])
            self.calculator_result_btn.mousePressEvent = self.return_result_ck_zclq
            def clear_ck_zclq(event):
                self.clear_result(["元", "元", "元"])
                self.ck_zclq_input_2.setText("0")
            self.calculator_reset_btn.mousePressEvent = clear_ck_zclq

            self.zclq_radioButton_1.setChecked(True)

        except Exception as e:
            print(e)

    def return_result_ck_zclq(self, event):
        try:
            def take_f(n):
                self.take_fq=n
            self.zclq_radioButton_1.toggled.connect(lambda:take_f(12) if self.zclq_radioButton_1.isChecked() else None)
            self.zclq_radioButton_2.toggled.connect(lambda:take_f(4) if self.zclq_radioButton_2.isChecked() else None)
            self.zclq_radioButton_3.toggled.connect(lambda:take_f(2) if self.zclq_radioButton_3.isChecked() else None)
            start_date = self.ck_zclq_input_1.text()
            money = self.ck_zclq_input_2.text()
            rate = self.ck_zclq_input_3.text()
            self.ck_zclq_input_4.currentTextChanged.connect(self.zclq_combox_select)
            if self.check_calculator_cal_(start_date, "", 0) and self.check_calculator_num(
                    money) and self.check_calculator_num(rate):
                """
                   计算分期存款利息
                   :param principal: 总本金
                   :param annual_rate: 年利率
                   :param months: 总月数
                   :param install_per_year: 每年存款次数(12=月,4=季,2=半年)
                   :return: (每次存款金额, 总利息)
                   """
                annual_rate=float(rate)/100
                installments = self.zclq_time_d * self.take_fq // 12
                deposit_per_install = float(money) / installments
                total_interest = float(money) * annual_rate * (installments + 1) / (2 * self.take_fq)
                result1 = deposit_per_install
                result2 = total_interest
                result = [result1, result2,0 ]
                for i in range(1, 4):
                    getattr(self, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
        except Exception as e:
            print(e)

    def zclq_combox_select(self, text):
        match (text):
            case "一年":
                self.zclq_time_d = 12
            case "三年":
                self.zclq_time_d = 36
            case "五年":
                self.zclq_time_d = 60

    def show_ck_cbqx_widget(self):
        try:
            self.cbqx_time_d = 12
            self.clear_result(["元", "元", "元"])
            self.update_result_lb(["每次支取利息金额:", "到期支取本息金额:","扣除利息税金额:"])
            self.ck_cbqx_cal_1_status = False
            self.ck_cbqx_cal1.setPixmap(QPixmap(self.img_path + r'\cal1.png').scaled(42, 42))
            self.ck_cbqx_cal1.mousePressEvent = partial(self.toggle_calculator_cal, status_name="ck_cbqx_cal_1_status",
                                                        cal_name="ck_cbqx_calendarWidget_1")
            self.ck_cbqx_calendarWidget_1.clicked.connect(
                lambda date: self.on_calculator_cal_clicked("ck_cbqx_input_1", date)
            )
            self.hide_cal()
            self.ck_inner_list = [datetime.today().strftime("%Y-%m-%d"), "0", "2.85", ]
            for i in range(1, 4):
                getattr(self, f"ck_cbqx_input_{i}").setText(self.ck_inner_list[i - 1])
            self.calculator_result_btn.mousePressEvent = self.return_result_ck_cbqx
            def clear_ck_cbqx(event):
                self.clear_result(["元", "元", "元"])
                self.ck_cbqx_input_2.setText("0")
            self.calculator_reset_btn.mousePressEvent = clear_ck_cbqx
        except Exception as e:
            print(e)

    def return_result_ck_cbqx(self, event):
        try:
            start_date = self.ck_cbqx_input_1.text()
            money = self.ck_cbqx_input_2.text()
            rate = self.ck_cbqx_input_3.text()
            self.ck_cbqx_input_4.currentTextChanged.connect(self.cbqx_combox_select)
            if self.check_calculator_cal_(start_date, "", 0) and self.check_calculator_num(
                    money) and self.check_calculator_num(rate):
                result1 = float(rate)/100/12 *float(money)
                result2 = float(money)+(result1*self.cbqx_time_d/12)
                result = [result1, result2,0 ]
                for i in range(1, 4):
                    getattr(self, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
        except Exception as e:
            print(e)

    def cbqx_combox_select(self, text):
        match (text):
            case "一年":
                self.cbqx_time_d = 12
            case "三年":
                self.cbqx_time_d = 36
            case "五年":
                self.cbqx_time_d = 60

    def show_ck_dhlb_widget(self):
        try:
            self.update_result_lb (["到期本息总额:", "扣除利息税金额:",""])
            self.ck_dhlb_cal_1_status=False
            self.ck_dhlb_cal_2_status=False
            self.clear_result(["元", "元", ""])
            self.ck_dhlb_cal1.setPixmap(QPixmap(self.img_path + r'\cal1.png').scaled(42, 42))
            self.ck_dhlb_cal2.setPixmap(QPixmap(self.img_path + r'\cal1.png').scaled(42, 42))
            self.ck_dhlb_cal1.mousePressEvent = partial(self.toggle_calculator_cal, status_name="ck_dhlb_cal_1_status",
                                                      cal_name="ck_dhlb_calendarWidget_1")
            self.ck_dhlb_cal2.mousePressEvent = partial(self.toggle_calculator_cal, status_name="ck_dhlb_cal_2_status",
                                                      cal_name="ck_dhlb_calendarWidget_2")
            self.ck_dhlb_calendarWidget_1.clicked.connect(
                lambda date: self.on_calculator_cal_clicked("ck_dhlb_input_1", date)
            )
            self.ck_dhlb_calendarWidget_2.clicked.connect(
                lambda date: self.on_calculator_cal_clicked("ck_dhlb_input_4", date)
            )
            self.hide_cal()
            # self.ck_hq_widget.show()
            self.ck_inner_list = [datetime.today().strftime("%Y-%m-%d"),"0","0.66",""]
            for i in range(1,5):
                getattr(self,f"ck_dhlb_input_{i}").setText(self.ck_inner_list[i-1])
            self.calculator_result_btn.mousePressEvent=self.return_result_ck_dhlb
            def clear_ck_dhlb(event):
                self.clear_result(["元", "元", ""])
                self.ck_dhlb_input_2.setText("0")
            self.calculator_reset_btn.mousePressEvent=clear_ck_dhlb
        except Exception as e:
            print(e)

    def return_result_ck_dhlb(self,event):
        try:
            start_date = self.ck_dhlb_input_1.text()
            money = self.ck_dhlb_input_2.text()
            rate = self.ck_dhlb_input_3.text()
            end_date = self.ck_dhlb_input_4.text()
            if self.check_calculator_cal_(start_date, end_date,"1")!=False and self.check_calculator_num(money) and self.check_calculator_num(rate):
                day_num = self.check_calculator_cal_(start_date, end_date,"1")
                result1 = day_num * float(rate) / 36500 * float(money) + float(money)
                result2 = 0
                result = [result1, result2,  ]
                for i in range(1, 3):
                    getattr(self, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
        except Exception as e:
            print(e)

    def show_ck_tzck_widget(self):
        try:
            self.update_result_lb (["到期本息总额:", "扣除利息税金额:",""])
            self.ck_tzck_cal_1_status=False
            self.ck_tzck_cal_2_status=False
            self.clear_result(["元", "元", ""])
            self.ck_tzck_cal1.setPixmap(QPixmap(self.img_path + r'\cal1.png').scaled(42, 42))
            self.ck_tzck_cal2.setPixmap(QPixmap(self.img_path + r'\cal1.png').scaled(42, 42))
            self.ck_tzck_cal1.mousePressEvent = partial(self.toggle_calculator_cal, status_name="ck_tzck_cal_1_status",
                                                      cal_name="ck_tzck_calendarWidget_1")
            self.ck_tzck_cal2.mousePressEvent = partial(self.toggle_calculator_cal, status_name="ck_tzck_cal_2_status",
                                                      cal_name="ck_tzck_calendarWidget_2")
            self.ck_tzck_calendarWidget_1.clicked.connect(
                lambda date: self.on_calculator_cal_clicked("ck_tzck_input_1", date)
            )
            self.ck_tzck_calendarWidget_2.clicked.connect(
                lambda date: self.on_calculator_cal_clicked("ck_tzck_input_4", date)
            )
            self.hide_cal()
            # self.ck_hq_widget.show()
            self.ck_inner_list = [datetime.today().strftime("%Y-%m-%d"),"0","0.8",""]
            for i in range(1,5):
                getattr(self,f"ck_tzck_input_{i}").setText(self.ck_inner_list[i-1])
            self.calculator_result_btn.mousePressEvent=self.return_result_ck_tzck
            def clear_ck_tzck(event):
                self.clear_result(["元", "元", ""])
                self.ck_tzck_input_2.setText("0")
            self.calculator_reset_btn.mousePressEvent=clear_ck_tzck
        except Exception as e:
            print(e)

    def return_result_ck_tzck(self,event):
        try:
            start_date = self.ck_tzck_input_1.text()
            money = self.ck_tzck_input_2.text()
            rate = self.ck_tzck_input_3.text()
            end_date = self.ck_tzck_input_4.text()
            self.ck_tzck_input_5.currentTextChanged.connect(self.tzck_combox_select)
            if self.check_calculator_cal_(start_date, end_date,"1")!=False and self.check_calculator_num(money) and self.check_calculator_num(rate):
                day_num = self.check_calculator_cal_(start_date, end_date,"1")
                result1 = day_num * float(rate) / 36500 * float(money) + float(money)
                result2 = 0
                result = [result1, result2,  ]
                for i in range(1, 3):
                    getattr(self, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
        except Exception as e:
            print(e)

    def tzck_combox_select(self, text):
        match (text):
            case "一天通知存款":
                self.ck_tzck_input_3.setText("0.8")
            case "七天通知存款":
                self.ck_tzck_input_3.setText("1.35")

    def show_ck_jycx_widget(self):
        try:
            # region Description
            self.update_result_lb (["到期本息总额:", "",""])
            self.ck_jycx_cal_1_status=False
            self.jycx_time_d = 12
            self.clear_result(["元", "", ""])
            self.ck_jycx_cal1.setPixmap(QPixmap(self.img_path + r'\cal1.png').scaled(42, 42))
            self.ck_jycx_cal1.mousePressEvent = partial(self.toggle_calculator_cal, status_name="ck_jycx_cal_1_status",
                                                      cal_name="ck_jycx_calendarWidget_1")
            self.ck_jycx_calendarWidget_1.clicked.connect(
                lambda date: self.on_calculator_cal_clicked("ck_jycx_input_1", date)
            )
            self.hide_cal()
            # self.ck_hq_widget.show()
            self.ck_inner_list = [datetime.today().strftime("%Y-%m-%d"),"0","3",""]
            for i in range(1,4):
                getattr(self,f"ck_jycx_input_{i}").setText(self.ck_inner_list[i-1])
            self.calculator_result_btn.mousePressEvent=self.return_result_ck_jycx
            def clear_ck_jycx(event):
                self.clear_result(["元", "", ""])
                self.ck_jycx_input_2.setText("0")
            self.calculator_reset_btn.mousePressEvent=clear_ck_jycx
            # endregion
        except Exception as e:
            print(e)

    def return_result_ck_jycx(self,event):
        try:
            start_date = self.ck_jycx_input_1.text()
            money = self.ck_jycx_input_2.text()
            rate = self.ck_jycx_input_3.text()
            self.ck_jycx_input_4.currentTextChanged.connect(self.jycx_combox_select)
            if self.check_calculator_cal_(start_date, "","0")!=False and self.check_calculator_num(money) and self.check_calculator_num(rate):
                annual_rate = float(rate) / 100
                months = self.jycx_time_d
                monthly_amount = float(money)
                monthly_rate = annual_rate / 12  # 月利率
                total_interest = 0
                for month in range(1, months + 1):
                    total_interest += monthly_amount * monthly_rate * (months - month + 1)
                total_amount = monthly_amount * months + total_interest
                result1 = total_amount
                result = [result1,  ]
                for i in range(1, 2): getattr(self, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
        except Exception as e:
            print(e)

    def jycx_combox_select(self, text):
        match (text):
            case "一年期":
                self.ck_jycx_input_3.setText("3")
                self.jycx_time_d=12
            case "三年期":
                self.ck_jycx_input_3.setText("4.25")
                self.jycx_time_d=36
            case "六年期":
                self.ck_jycx_input_3.setText("4.75")
                self.jycx_time_d=72

    def load_bank_wh_data(self,bank_name:str):
        try:
            self.worker_thread_bank_wh = get_bank_wh_data(bank_name=bank_name)
            self.worker_thread_bank_wh.finished.connect(self.task_finished_bank_wh)
            self.worker_thread_bank_wh.start()
            if self.ck_table_status==False:
                self.bank_ck_info = asyncio.run(get_bank_ck_data(search_name="", page=8).main())
                self.bank_ck_tableWidget.hide()
                self.bank_search_load.setText("正在加载")
                self.all_bank_ck_data = self.bank_ck_info
                self.ck_table_status = True
        except Exception as e:
            print(e)

    def task_finished_bank_wh(self, wh_data_list):
        self.bank_wh_data=wh_data_list
        self.bank_wh_table_load(data=self.bank_wh_data)
        # self.all_bank_ck_data=ck_data_list
        # 加载各银行存款汇率
        # print(bank_ck_info)
        self.bank_ck_table_load(self.bank_ck_info)
        self.bank_ck_tableWidget.show()

    def bank_wh_table_load(self,data):
        headers=["货币代码","货币名称","买入价","卖出价"]
        self.bank_wh_tableWidget.setColumnCount(4)
        self.bank_wh_tableWidget.setRowCount(len(data))
        self.bank_wh_tableWidget.setHorizontalHeaderLabels(headers)
        for row in range(len(data)):
            for col in range(4):
                item =QTableWidgetItem(f"{data[row].split("|")[col]}")
                item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.bank_wh_tableWidget.setItem(row, col, item)

    def open_calculator_widget(self):
        try:
            self.show_calculator_widget(0)
            self.calculator_widget.show()
            self.bank_wh_name_list=["boc","spdb","icbc","ecitic","cmbchina","cib","cebbank","ccb","bankcomm","abchina"]
            #加载牌价数据
            self.select_bank_name="boc"
            self.ck_table_status=False
            self.load_bank_wh_data(self.select_bank_name)
            self.bank_select.currentTextChanged.connect(self.bank_wh_combox_select)
            self.bank_time_lb.setText(f"(以100单位外币兑换人民币) 最新数据截至：{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}")
            #查询
            self.bank_search_btn.mousePressEvent=self.query_bank_name

        except Exception as e:
            print(e)

    def query_bank_name(self,event):
        search_name=self.bank_search_input.text()
        if search_name in ["","银行","银","行"]:
            # query_data=asyncio.run(get_bank_ck_data(search_name="", page=8).main())
            self.bank_ck_table_load(self.all_bank_ck_data)
        else:
            l=[]
            v_l=[]
            for i in range(len(self.all_bank_ck_data)):
                if search_name in self.all_bank_ck_data[i][0] or search_name==self.all_bank_ck_data[0]:
                    # self.load_bank_search_ck(search_name=search_name)
                    l.append(i)
                    for j in l:
                        v_l.append((self.all_bank_ck_data[j]))
            self.bank_ck_table_load(v_l)

    # def load_bank_search_ck(self,search_name:str):
    #     try:
    #         self.bank_search_load.setText("正在搜索中")
    #         self.worker_thread_bank_ck = get_search_bank_data(search_name=search_name)
    #         self.worker_thread_bank_ck.finished.connect(self.task_finished_bank_ck)
    #         self.worker_thread_bank_ck.start()
    #         self.bank_ck_tableWidget.hide()
    #     except Exception as e:
    #         self.bank_search_load.setText("暂无数据，请重新搜索其他银行！")
    #
    #
    # def task_finished_bank_ck(self, ck_data_list):
    #     self.bank_ck_table_load(ck_data_list)
    #     self.bank_ck_tableWidget.show()

    def bank_ck_table_load(self, data):
        headers = ["银行", "三个月", "半年", "一年","两年","三年","五年"]
        self.bank_ck_tableWidget.setColumnCount(7)
        self.bank_ck_tableWidget.setRowCount(len(data))
        self.bank_ck_tableWidget.setHorizontalHeaderLabels(headers)
        for row in range(len(data)):
            for col in range(7):
                item = QTableWidgetItem(f"{data[row][col]}")
                item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.bank_ck_tableWidget.setItem(row, col, item)
        self.bank_ck_tableWidget.setColumnWidth(0, 150)
        for i in range(1,7):
            self.bank_ck_tableWidget.setColumnWidth(i, 77)

    def bank_wh_combox_select(self,text):
        try:
            match (text):
                case "中国银行":
                    self.select_bank_name="boc"
                case "浦发银行":
                    self.select_bank_name = "spdb"
                case "中国工商银行":
                    self.select_bank_name = "icbc"
                case "中信银行":
                    self.select_bank_name = "ecitic"
                case "招商银行":
                    self.select_bank_name = "cmbchina"
                case "兴业银行":
                    self.select_bank_name = "cebbank"
                case "中国光大银行":
                    self.select_bank_name = "cib"
                case "中国建设银行":
                    self.select_bank_name = "cebbank"
                case "交通银行":
                    self.select_bank_name = "bankcomm"
                case "中国农业银行":
                    self.select_bank_name = "abchina"
            self.load_bank_wh_data(self.select_bank_name)
        except Exception as e:
            print(e)

    def close_calculator_widget(self):
        self.calculator_widget.hide()


    """资讯搜索"""
    def return_info_(self,event):
        if self.info_search_status==True:
            for i in range(1, 11):
                getattr(self, f"info_search_content_{i}").setHtml("")
                getattr(self, f"info_search_title_{i}").setHtml("")
                getattr(self, f"info_search_url_{i}").setText("")
            self.info_search_detail_tip_lb.setText("")
            self.info_search_detail_title_lb.setText("")
            self.info_detail_webBrowser_2.setHtml("")
            self.info_search_detail_widget.hide()
            self.info_search_status =False
            self.load_info_search_listWidget()

    def close_search_widget(self,event):
        self.info_search_widget.hide()

    def get_search_c_result(self):
        try:
            if self.info_search_textEdit.text()=="":
                self.info_search_c_widget.hide()
            else:
                self.info_search_c_widget.show()
                self.info_search_listWidget.clear()
                keyword = self.info_search_textEdit.text()
                # print(keyword)
                self.stock_list,self.stock_name=get_search_c.return_code_l(keyword=keyword)
                for i in self.stock_list:
                    self.info_search_listWidget.addItem(i)
                self.show_all_search.setText(f"查看全部 {keyword} 的搜索结果")
        except Exception as e:
            print(e)

    def clear_history(self, event):
        self.info_se_listWidget.clear()
        with open(r"api/widget/information\search_history", "w",
                  encoding="utf8") as f:
            f.write("")

    def search_info(self, event):
        if self.info_search_textEdit != "":
            self.search_scroll_count = 0
            self.info_search_keyword = self.info_search_textEdit.text()
            self.info_search_textEdit_2.setText(self.info_search_keyword)
            self.info_search_page = 1
            self.scrollArea_4.verticalScrollBar().valueChanged.connect(self.on_search_scroll)
            self.info_search_status = True
            try:
                self.update_info_search_data(self.info_search_keyword)  # 记录搜索记录
                self.load_info_search_title(self.info_search_keyword, self.info_search_page)
            except Exception as e:
                print(e)
        else:
            QMessageBox.information(self, "搜索失败", "搜索内容不能为空！")

    def search_info_keyword(self, event, keyword):
        self.search_scroll_count = 0
        self.info_search_keyword = keyword
        self.info_search_textEdit_2.setText(keyword)
        self.info_search_page = 1
        self.scrollArea_4.verticalScrollBar().valueChanged.connect(self.on_search_scroll)
        self.info_search_status = True
        try:
            self.update_info_search_data(self.info_search_keyword)  # 记录搜索记录
            self.load_info_search_title(self.info_search_keyword, self.info_search_page)
        except Exception as e:
            print(e)

    def search_info_2(self, event):
        if self.info_search_textEdit != "":
            self.search_scroll_count = 0
            self.info_search_keyword = self.info_search_textEdit_2.text()
            self.info_search_page = 1
            self.scrollArea_4.verticalScrollBar().valueChanged.connect(self.on_search_scroll)
            self.info_search_status = True
            try:
                self.update_info_search_data(self.info_search_keyword)  # 记录搜索记录
                self.load_info_search_title(self.info_search_keyword, self.info_search_page)
            except Exception as e:
                print(e)
        else:
            QMessageBox.information(self, "搜索失败", "搜索内容不能为空！")
    def load_info_search_title(self, keyword, page):
        self.info_s_time = time.time()
        self.info_search_detail_widget.show()
        self.info_search_load_lb.setText("搜索中...")
        self.worker_thread_info_search = get_search_title(keyword=keyword, page=page)
        self.worker_thread_info_search.finished.connect(self.task_finished_info_search)
        self.worker_thread_info_search.start()

    def task_finished_info_search(self, search_data_list):
        self.info_search_date = search_data_list[0]
        self.info_search_url = search_data_list[1]
        self.info_search_title = search_data_list[2]
        self.info_search_title_or = search_data_list[-1]
        self.info_search_content = search_data_list[3]
        self.info_search_source = search_data_list[4]

        self.info_e_time = time.time()
        # 遍历显示
        self.load_search_()
        self.load_info_search_detail(self.info_search_title_or[0], self.info_search_url[0])
        for i in range(1, 11):
            getattr(self, f"info_search_url_{i}").mousePressEvent = partial(self.on_click_search_index, index=i - 1)
        # 更新列表对应点击，收藏列表，浏览历史
        # self.update_info_other_list()
        self.info_search_load_lb.setText("本次搜索共耗时:{:.2f}s".format(self.info_e_time - self.info_s_time))

    def on_click_search_index(self, event, index):
        url = self.info_search_url
        title = self.info_search_title_or
        self.load_info_search_detail(title[index], url[index])
        self.info_search_collect_lb_1.mousePressEvent = partial(self.open_url, url=url[index])
        self.info_search_collect_lb_2.mousePressEvent = partial(self.copy_to_clipboard, url=url[index])

    def load_info_search_detail(self, title, url):
        a = []
        a.append(url)
        if "fund" in url:
            article_info = asyncio.run(url_infos_1(a).main())[0]
        else:
            article_info = asyncio.run(url_infos(a).main())[0]
        article_tip = "发布时间： " + article_info[0] + "  来源： " + article_info[1]
        article_content = article_info[2]
        self.info_search_detail_tip_lb.setText(article_tip)
        self.info_search_detail_title_lb.setText(title)
        self.info_detail_webBrowser_2.setHtml(article_content)
        self.check_collect(title=title, url=url)
        self.update_view_history(title=title, url=url)

    def load_search_(self):
        for i in range(1, 11):
            getattr(self, f"info_search_content_{i}").setHtml(self.info_search_content[i - 1])
            getattr(self, f"info_search_title_{i}").setHtml(self.info_search_title[i - 1])
            getattr(self, f"info_search_url_{i}").setText(self.info_search_url[i - 1])

    def on_search_scroll(self):
        scroll_bar = self.scrollArea_4.verticalScrollBar()
        if self.search_scroll_count == 50:
            self.info_search_load_lb.setText(f"已加载{self.info_search_keyword}全部搜索结果")
        if scroll_bar.value() == scroll_bar.maximum() and self.search_scroll_count < 49:  # 滚动到底部
            self.info_search_page += 1
            self.search_scroll_count += 1
            self.load_info_search_title(self.info_search_keyword, self.info_search_page)
            self.smooth_scroll_to_top(event=None)

    def toggle_hot_start(self, event):
        if self.hot_start == 0:
            self.hot_start += 10
            self.load_search_data()
        else:
            self.hot_start = 0
            self.load_search_data()

    def load_search_data(self):
        try:
            self.hot_listWidget_1.clear()
            self.hot_listWidget_2.clear()
            self.stock_listWidget_1.clear()
            self.stock_listWidget_2.clear()
            self.hot_l,self.hot_or, self.stock_l ,self.stock_or= search_data.return_search_init()
            for i in range(self.hot_start, self.hot_start + 10):
                if i < 5 or 9 < i < 15:
                    self.hot_listWidget_1.addItem(self.hot_l[i])
                else:
                    self.hot_listWidget_2.addItem(self.hot_l[i])
            for j in range(self.hot_start, self.hot_start + 10):
                if j < 5 or 9 < j < 15:
                    self.stock_listWidget_1.addItem(self.stock_l[j])
                else:
                    self.stock_listWidget_2.addItem(self.stock_l[j])
            self.hot_listWidget_1.itemClicked.connect(self.bind_info_hot_search)
            self.hot_listWidget_2.itemClicked.connect(self.bind_info_hot_search)
            self.stock_listWidget_1.itemClicked.connect(self.bind_info_hot_stock_search)
            self.stock_listWidget_2.itemClicked.connect(self.bind_info_hot_stock_search)
        except Exception as e:
            print(e)

    def show_search_widget(self):
        try:
            self.info_search_widget.show()
            if len(self.info_search_textEdit.text()) == 0:
                self.info_search_c_widget.hide()
            self.load_search_data()
            self.load_info_search_listWidget()
            self.search_widget_status = True
            self.info_search_listWidget.itemClicked.connect(self.bind_info_search_stock)
        except Exception as e:
            print(e)

    def load_info_search_listWidget(self):
        self.info_se_listWidget.clear()
        with open(r"api/widget/information/search_history", "r", encoding="utf8") as f:
            self.info_search_history = f.readlines()
        for i in self.info_search_history[::-1]:
            max_l = 10 if len(i) > 10 else len(i)
            if max_l < 10:
                self.info_se_listWidget.addItem(i[:max_l].strip())
            else:
                self.info_se_listWidget.addItem(i[:max_l].strip() + "...")
        self.info_se_listWidget.itemClicked.connect(self.bind_info_search_data)

    def bind_info_search_data(self, item):
        try:

            self.info_search_history_list = []
            with open(r"api/widget/information/search_history", "r", encoding="utf8") as f:
                self.info_search_history = f.readlines()
            for i in self.info_search_history:
                max_l = 10 if len(i) > 10 else len(i)
                if max_l < 10:
                    self.info_search_history_list.append(i[:max_l].strip())
                else:
                    self.info_search_history_list.append(i[:max_l].strip() + "...")
            self.info_search_item_index = self.info_search_history_list.index(item.text())
            print(self.info_search_item_index)
            # self.info_search_textEdit.setText(self.info_search_history[ self.info_search_item_index].strip())
            self.info_search_textEdit_2.setText(self.info_search_history[self.info_search_item_index].strip())
            self.search_info_keyword(event=None, keyword=self.info_search_history[self.info_search_item_index].strip())
        except Exception as e:
            print(e)

    def bind_info_search_stock(self, item):
        try:
            self.info_search_stock_list = []
            print(self.stock_name)
            for i in self.stock_list:
                self.info_search_stock_list.append(i)
            print(self.info_search_stock_list)
            self.info_stock_item_index = self.info_search_stock_list.index(item.text())
            # self.info_search_textEdit.setText(self.stock_name[ self.info_stock_item_index])
            self.info_search_textEdit_2.setText(self.stock_name[self.info_stock_item_index])
            self.search_info_keyword(event=None, keyword=self.stock_name[self.info_stock_item_index])
        except Exception as e:
            print(e)

    def bind_info_hot_search(self, item):
        try:
            self.info_hot_item_index = self.hot_l.index(item.text())
            self.info_search_textEdit_2.setText(self.hot_l[self.info_hot_item_index])
            self.search_info_keyword(event=None, keyword=self.hot_or[self.info_hot_item_index])
        except Exception as e:
            print(e)

    def bind_info_hot_stock_search(self, item):
        try:
            self.info_hot_stock_item_index = self.stock_l.index(item.text())
            self.info_search_textEdit_2.setText(self.stock_l[self.info_hot_stock_item_index])
            self.search_info_keyword(event=None, keyword=self.stock_or[self.info_hot_stock_item_index])
        except Exception as e:
            print(e)

    def update_info_search_data(self, query):
        with open(r"api/widget/information/search_history", "r", encoding="utf8") as f:
            view_history = f.readlines()
            f.close()
        data = query + "\n"
        if len(view_history) > 10:
            view_history.pop(0)
        if data not in set(view_history):
            view_history.append(data)
        else:
            view_history.remove(data)
            view_history.append(data)
        with open(r"api/widget/information/search_history", "w", encoding="utf8") as f1:
            f1.writelines(view_history)
            f1.close()

    """资讯其他文章+页面"""

    def change_info_other_index(self,event,index):
        self.info_other_widget_index =index
        self.show_info_other()

    def isdisable_info_page(self, page_name, status):
        if status:
            getattr(self, f"info_other_{page_name}_page").setStyleSheet("""
                             QLabel {
                            border: 1px solid gray; /* 边框宽度和颜色 */
                            border-radius: 10px;   /* 边框圆角 */
                            padding: 10px;        /* 内边距 */
                          color:   gray
                        }
                    """)
        else:
            getattr(self, f"info_other_{page_name}_page").setStyleSheet("""
                                          QLabel {
                        border: 1px solid #5E548E; /* 边框宽度和颜色 */
                        border-radius: 10px;   /* 边框圆角 */
                        padding: 10px;        /* 内边距 */
        			    color:   #5E548E}
                      QLabel:hover {
                     color: white; 
                    background-color:#5E548E}
                                    }
                                """)

    def load_info_title(self,type,page):
        self.s = time.time()
        self.info_other_load_lb.setText("加载中...")
        # 创建并启动子线程
        self.worker_thread_info_other = get_other_title(type=type, url_page=page)
        if type=="tzcl":
            self.worker_thread_info_other.finished.connect(self.task_finished_info_other_tzcl)
        else:
            self.worker_thread_info_other.finished.connect(self.task_finished_info_other)
        self.worker_thread_info_other.start()

    def task_finished_info_other_tzcl(self,title_data_l):
        try:
            #传入1个页面的40条数据
            self.info_other_time = title_data_l[0]
            self.info_other_href = title_data_l[1]
            self.info_other_title = title_data_l[2]
            self.info_other_max_page = title_data_l[3]
            self.e = time.time()

            self.info_other_tail_page.setText(str(self.info_other_max_page))
            self.load_info_other_detail(self.info_other_title[0],self.info_other_href[0])
            self.info_detail_listWidget_2.itemClicked.connect(self.load_info_other_list_detail)
            self.update_info_other_list()
            self.update_page_info_other()
            self.info_other_load_lb.setText("本次加载共耗时:{:.2f}s".format(self.e - self.s))
            #加载第一篇文章
        except Exception as e:
            print(e)

    def task_finished_info_other(self, title_data_l):
        try:
            #传入1个页面的40条数据
            self.info_other_time = title_data_l[0]
            self.info_other_href = title_data_l[1]
            self.info_other_title = title_data_l[2]
            self.info_other_max_page = title_data_l[3]
            self.e = time.time()
            self.info_other_tail_page.setText(str(self.info_other_max_page))
            self.load_info_other_detail(self.info_other_title[0],self.info_other_href[0])
            self.info_detail_listWidget_2.itemClicked.connect(self.load_info_other_list_detail)
            self.update_info_other_list()
            self.update_page_info_other()
            self.info_other_load_lb.setText("本次加载共耗时:{:.2f}s".format(self.e - self.s))
            #加载第一篇文章
        except Exception as e:
            print(e)

    def sub_page_info_other(self,event):
        if self.info_current_page >1:
            self.info_current_page -= 1
            self.load_info_title(self.info_other_list[self.info_other_widget_index], self.info_current_page)
            self.update_page_info_other()

    def add_page_info_other(self,event):
        if self.info_current_page<self.info_other_max_page:
            self.info_current_page+=1
            self.load_info_title(self.info_other_list[self.info_other_widget_index],self.info_current_page)
            self.update_page_info_other()

    def first_page_info_other(self,event):
        self.info_current_page=1
        self.load_info_title(self.info_other_list[self.info_other_widget_index], self.info_current_page)
        self.update_page_info_other()

    def tail_page_info_other(self,event):
        self.info_current_page =self.info_other_max_page
        self.load_info_title(self.info_other_list[self.info_other_widget_index], self.info_current_page)
        self.update_page_info_other()
    def update_page_info_other(self,):
        if self.info_current_page==1 and self.info_other_max_page==1:
            self.isdisable_info_page("sub",True)
            self.isdisable_info_page("add",True)
        elif self.info_current_page==1 and self.info_current_page<self.info_other_max_page:
            self.isdisable_info_page("sub", True)
            self.isdisable_info_page("add", False)
        elif 1<self.info_current_page<self.info_other_max_page:
            self.isdisable_info_page("sub", False)
            self.isdisable_info_page("add", False)
        elif self.info_current_page==self.info_other_max_page:
            self.isdisable_info_page("sub", False)
            self.isdisable_info_page("add", True)
        self.info_other_c_page.setText("第{}页".format(str( self.info_current_page)))

    def load_info_other_list_detail(self,item):
        #加载指定的数据
        try:
            self.info_other_title_list_1=[]
            for i in self.info_other_title_list:
                max_l = 27 if len(i) > 27 else len(i)
                if max_l < 27:
                    self.info_other_title_list_1.append(i[:max_l].strip())
                else:
                    self.info_other_title_list_1.append(i[:max_l].strip() + "...")
            self.info_other_item_index=self.info_other_title_list_1.index(item.text())
            article_title=self.info_other_title_list[ self.info_other_item_index]
            article_url=self.info_other_href_list[self.info_other_item_index]
            self.load_info_other_detail(article_title,article_url)
            self.info_other_collect_lb_1.mousePressEvent = partial(self.open_url, url=article_url)
            self.info_other_collect_lb_2.mousePressEvent = partial(self.copy_to_clipboard, url=article_url)
            self.update_detail_collect_list()
        except Exception as e:
            print(e)

    def load_info_other_detail(self,title,url):
        a=[]
        a.append(url)
        if "fund" in url:
            article_info = asyncio.run(url_infos_1(a).main())[0]
        else:
            article_info = asyncio.run(url_infos(a).main())[0]
        article_tip = "发布时间： " + article_info[0] + "  来源： " + article_info[1]
        article_content = article_info[2]
        self.info_detail_tip_lb_2.setText(article_tip)
        self.info_detail_title_lb_2.setText(title)
        self.info_other_webView.setHtml(article_content)
        self.check_collect(title=title, url=url)

    def update_info_other_list(self,):
        self.info_detail_listWidget_2.clear()
        start=0
        end=20
        self.info_other_title_list = self.info_other_title[start:end]
        self.info_other_href_list = self.info_other_href[start:end]
        for i in self.info_other_title_list:
            max_l = 27 if len(i) > 27 else len(i)
            if max_l < 27:
                self.info_detail_listWidget_2.addItem(i[:max_l].strip())
            else:
                self.info_detail_listWidget_2.addItem(i[:max_l].strip() + "...")
        for i in range(0, 20):
            getattr(self, f"info_other_time_{i + 1}").setText(self.info_other_time[i])

    def show_info_other(self,):
        try:
            self.info_other_widget.show()
            self.info_detail_listWidget_2.clear()
            self.info_other_list=["jjgd","jjxx","jjyw","tzcl","smzx"]
            self.load_info_title(self.info_other_list[self.info_other_widget_index], 1)
            self.info_current_page=1
            #向listwidget添加内容
        except Exception as e:
            print(e)


    def open_url(self,event,url):
        url = QUrl(url)  # 指定网址
        QDesktopServices.openUrl(url)  # 打开浏览器

    def copy_to_clipboard(self,event,url):
        """复制文本到剪贴板并弹出消息提示框"""
        clipboard = QApplication.clipboard()  # 获取剪贴板
        clipboard.setText(url)  # 设置剪贴板内容
        QMessageBox.information(self, "复制成功", "文本已复制到剪贴板！")

    def createShadowEffect(self):
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(15)  # 阴影模糊半径
        shadow.setColor(Qt.GlobalColor.lightGray)  # 阴影颜色
        shadow.setOffset(3, 4)  # 阴影偏移量
        return shadow

    def eventFilter(self, obj, event):
        if event.type() == QEvent.Type.MouseButtonPress:  # 使用 QEvent.Type.MouseButtonPress
            if not self.info_search_widget.geometry().contains(event.globalPosition().toPoint()):  # 使用 globalPosition()
                self.info_search_widget.hide()  # 隐藏弹出窗口
        return super().eventFilter(obj, event)

    def on_information_item_clicked(self,item):
        self.info_item_index=self.info_item_list_name.index(item.text())


    def toggle_collect_lb(self,current_url_collect_status):
        if current_url_collect_status:
            self.info_collect_lb.setPixmap(self.collect_img)
            self.info_other_collect_lb_3.setPixmap(self.collect_img)
            self.info_search_collect_lb_3.setPixmap(self.collect_img)
        else:
            self.info_collect_lb.setPixmap(self.no_collect_img)
            self.info_other_collect_lb_3.setPixmap(self.no_collect_img)
            self.info_search_collect_lb_3.setPixmap(self.no_collect_img)

    def update_collect(self,event,title,url):
        try:
            data = title + "|" + url + "\n"
            with open(r"api/widget/information\collect", "r", encoding="utf8") as f:
                collect_ = f.readlines()
            if self.current_url_collect_status:#已收藏
                collect_url_index=collect_.index(data)
                collect_.pop(collect_url_index)
                with open(r"api/widget/information\collect", "w", encoding="utf8") as f1:
                    f1.writelines(collect_)
                self.load_collect()
                self.toggle_collect_lb(False)
                self.current_url_collect_status = False
            else:
                with open(r"api/widget/information\collect", "a+", encoding="utf8") as f2:
                    if data not in set(collect_):
                        f2.write(data)
                        self.toggle_collect_lb(True)
                        self.current_url_collect_status=True
                self.load_collect()
            # self.update_collect_list()
            # self.update_detail_collect_list()
        except Exception as e:
            print(e)

        # if data not in set(collect_):
        #     self.current_url_collect_status = False
        #     self.info_collect_lb.setPixmap(self.no_collect_img)
        # else:
        #     self.current_url_collect_status = True
        #     self.info_collect_lb.setPixmap(self.collect_img)


    def check_collect(self,title,url):
        with open(r"api/widget/information\collect", "r", encoding="utf8") as f:
            collect_ = f.readlines()
        # with open(r"api/widget/information\collect", "a+", encoding="utf8") as f:
            data = title + "|" + url + "\n"
        if data not in set(collect_):
            self.current_url_collect_status = False
            self.info_collect_lb.setPixmap(self.no_collect_img)
            self.info_other_collect_lb_3.setPixmap(self.no_collect_img)
            self.info_search_collect_lb_3.setPixmap(self.no_collect_img)
        else:
            self.current_url_collect_status = True
            self.info_collect_lb.setPixmap(self.collect_img)
            self.info_other_collect_lb_3.setPixmap(self.collect_img)
            self.info_search_collect_lb_3.setPixmap(self.collect_img)
        self.info_collect_lb.mousePressEvent = partial(self.update_collect, title=title,
                                                       url=url)
        self.info_other_collect_lb_3.mousePressEvent = partial(self.update_collect, title=title,
                                                       url=url)
        self.info_search_collect_lb_3.mousePressEvent = partial(self.update_collect, title=title,
                                                               url=url)

    def load_collect(self):
        self.info_collect_widget.clear()
        with open(r"api/widget/information/collect", "r", encoding="utf8") as f:
            self.collect_list = f.readlines()
        for i in self.collect_list [::-1]:
            self.info_collect_widget.addItem(i.split("|")[0])
        self.update_collect_list()
    # def check_collect


    def smooth_scroll_to_top(self,event):
        current_value = self.scrollArea_3.verticalScrollBar().value()
        current_value1 = self.scrollArea_4.verticalScrollBar().value()
        self.animation.setStartValue(current_value)
        self.animation1.setStartValue(current_value1)
        self.animation.setEndValue(0)
        self.animation1.setEndValue(0)
        self.animation.start()
        self.animation1.start()

    def open_infomation_widget(self,event):
        self.infomation_widget.show()
        self.info_search_detail_widget.hide()
        self.info_load_lb.setText("加载中")
        # 监听滚动条事件
        self.scrollArea_3.verticalScrollBar().valueChanged.connect(self.on_scroll)
        self.info_up_img.setPixmap(QPixmap(self.img_path + r'\return_up.png').scaled(32, 32))
        self.info_collect_item_list_name=[]
        self.load_collect()
        self.load_info_data()#开启线程
        self.update_collect_list()
        self.update_history_list()#更新历史记录列表供使用
        self.info_collect_widget.itemClicked.connect(self.toggle_info_collect_list_detail)
        self.info_history_widget.itemClicked.connect(self.toggle_info_history_list_detail)


    def load_info_data(self):
        self.worker_thread_info = get_title()
        self.worker_thread_info.finished.connect(self.task_finished_info)
        self.worker_thread_info.start()

    def show_detail_widget(self,title,tip,content):
        self.info_detail_widget.show()
        self.info_detail_webBrowser.setHtml(content)
        self.info_detail_title_lb.setText(title)
        self.info_detail_tip_lb.setText(tip)

    def load_news(self, count):
        try:
            """加载指定数量的新闻"""
            # print(self.info_url[self.info_index_start:self.info_index_start+count])
            self.info= asyncio.run(
                url_infos(self.info_url[self.info_index_start:self.info_index_start + count]).main())
            print(self.info)
            tip_init=[]
            article_init=[]
            for i in range(self.info_index_start,self.info_index_start+count):
                getattr(self,f"info_title_{i-10*self.load_new_n+1}").setText(self.info_title_l[i])
                getattr(self, f"info_img_{i -10*self.load_new_n+ 1}").setHtml(f'<img src="{self.info_img_url[i]}" width="100%" height="100%">')
                getattr(self,f"info_date1_{i-10*self.load_new_n+1}").setText(self.info[i-10*self.load_new_n][0])
                getattr(self,f"info_source_{i-10*self.load_new_n+1}").setText(self.info[i-10*self.load_new_n][1])
                getattr(self, f"info_title_{i - 10 * self.load_new_n + 1}").mousePressEvent = partial(
                    self.toggle_info_detail, index=i - 10 * self.load_new_n)
                tip_init.append("发布时间： "+self.info[i-10*self.load_new_n][0]+"  来源： "+self.info[i-10*self.load_new_n][1])
                article_init.append(self.info[i-10*self.load_new_n][2])
            self.title_l=self.info_title_l[self.info_index_start:self.info_index_start+count]
            self.tip_l=tip_init
            self.article_l=article_init

            # print(self.title_l)
            # print(self.tip_l)
            # self.info_title_1.mousePressEvent=partial(self.toggle_info_detail,index=0)
            # getattr(self,f"info_tag_{i+1}").setText(f"<b>{random.choice(["精选","推荐","优质",])}</b>")
            # img_url=self.info_img_url[i]
            # date=self.info[i].split("|")[0]
            # source=self.info[i].split("|")[1]
            # article=self.info[i].split("|")[2]
            # article=article_data.return_article_data(url=self.info_url[i])

        except Exception as e:
            print(e)
    def task_finished_info(self,data_l):
        self.info_title_l,self.info_img_url,self.info_url=data_l
        self.load_news(10)
        self.load_new_n += 1
        self.info_load_lb.setText("加载完成")

        # self.info_index_start+=10

    def on_scroll(self):
        """滚动条事件处理"""
        scroll_bar = self.scrollArea_3.verticalScrollBar()
        if scroll_bar.value() == scroll_bar.maximum() and self.info_index_start <=49:  # 滚动到底部
            self.info_index_start+=10
            self.load_news(10)  # 加载更多新闻
            self.load_new_n += 1
            self.smooth_scroll_to_top(event=None)
            self.info_load_lb.setText("刷新成功")

        elif self.info_index_start >=49:
            self.info_load_lb.setText("已全部加载")

    def on_calendar_clicked(self, date: QDate):
        if self.start_date_c is None:
            self.start_date_c = date
            self.label_8.setText(f"起始日期: {self.start_date_c.toString('yyyy-MM-dd')}")
        # 如果起始日期已设置且结束日期未设置，则设置为当前点击的日期
        elif self.end_date_c is None:
            if date >= self.start_date_c:  # 验证结束日期必须大于或等于起始日期
                self.end_date_c = date
                self.label_8.setText(f"起始日期: {self.start_date_c.toString('yyyy-MM-dd')}, 结束日期: {self.end_date_c.toString('yyyy-MM-dd')}")
            else:
                QMessageBox.warning(self, "错误", "结束日期必须大于或等于起始日期！")
        # 如果起始日期和结束日期都已设置，则重置起始日期和结束日期
        else:
            self.start_date_c = date
            self.end_date_c = None
            self.label_8.setText(f"起始日期: {self.start_date_c.toString('yyyy-MM-dd')}")

    def kf_query_cal(self,event):
        if self.start_date_c is not None and self.end_date_c is not None:
            print(f"起始日期: {self.start_date_c.toString('yyyy-MM-dd')}, 结束日期: {self.end_date_c.toString('yyyy-MM-dd')}")
            self.start_date=self.start_date_c.toString('yyyy-MM-dd')
            self.end_date=self.end_date_c.toString('yyyy-MM-dd')
            self.kf_current_page=1
            self.load_kf_data_params(event=None, index=self.kf_jj_index, tab_index=self.kf_jj_sub_index,
                                     page=self.kf_current_page,
                                     sort_dep=self.sort_dep_index, sort=self.kf_sort, start_date=self.start_date,
                                     end_date=self.end_date)
        else:
            print("请选择起始日期和结束日期！")

    def on_checkbox_state_changed(self, state):
        if state == Qt.CheckState.Checked.value:
           self.kf_sort="asc"
        else:
            self.kf_sort="desc"
        self.load_kf_data_params(event=None, index=self.kf_jj_index, tab_index=self.kf_jj_sub_index,
                                 page=self.kf_current_page,
                                 sort_dep=self.sort_dep_index, sort=self.kf_sort, start_date=self.start_date,
                                 end_date=self.end_date)
    def sub_page_kf(self,event):
        if self.kf_current_page > 1:
            self.kf_current_page -= 1
            self.load_kf_data_params(event=None,index=self.kf_jj_index,tab_index=self.kf_jj_sub_index,page=self.kf_current_page,
                                                                  sort_dep=self.sort_dep_index,sort=self.kf_sort,start_date=self.start_date,end_date=self.end_date)
            self.update_kf_page()
            self.current_page_lb_dt.setText("第{}页".format(str(self.kf_current_page)))

    def add_page_kf(self,event):
        if self.kf_current_page < self.kf_total_page:
            self.kf_current_page += 1
            self.load_kf_data_params(event=None,index=self.kf_jj_index, tab_index=self.kf_jj_sub_index,
                                     page=self.kf_current_page,
                                     sort_dep=self.sort_dep_index, sort=self.kf_sort, start_date=self.start_date,
                                     end_date=self.end_date)
            self.update_kf_page()
            self.current_page_lb_kf.setText("第{}页".format(str(self.kf_current_page)))


    def tail_page_kf(self,event):
        self.kf_current_page = self.kf_total_page
        self.load_kf_data_params(event=None, index=self.kf_jj_index, tab_index=self.kf_jj_sub_index,
                                 page=self.kf_current_page,
                                 sort_dep=self.sort_dep_index, sort=self.kf_sort, start_date=self.start_date,
                                 end_date=self.end_date)
        self.update_kf_page()
        self.current_page_lb_kf.setText("第{}页".format(str(self.kf_current_page)))
    def first_page_kf(self,event):
        self.kf_current_page = 1
        self.load_kf_data_params(event=None, index=self.kf_jj_index, tab_index=self.kf_jj_sub_index,
                                 page=self.kf_current_page,
                                 sort_dep=self.sort_dep_index, sort=self.kf_sort, start_date=self.start_date,
                                 end_date=self.end_date)
        self.update_kf_page()
        self.current_page_lb_kf.setText("第{}页".format(str(self.kf_current_page)))

    def goto_page_kf(self,event):
        if int(self.goto_Edit_kf.text()) > self.kf_total_page or int(self.goto_Edit_kf.text()) < 0:
            self.goto_Edit_kf.setText("")
        else:
            self.kf_current_page = int(self.goto_Edit_kf.text())
            self.load_kf_data_params(event=None,index=self.kf_jj_index,tab_index=self.kf_jj_sub_index,page=self.kf_current_page,
                                                                  sort_dep=self.sort_dep_index,sort=self.kf_sort,start_date=self.start_date,end_date=self.end_date)
            self.update_kf_page()
            self.current_page_lb_kf.setText("第{}页".format(str(self.kf_current_page)))

    def update_kf_page(self):
        if self.kf_total_page == 1:
            self.goto_widget_3.hide()
        else:
            self.goto_widget_3.show()
        # 对按钮显示判断
        if self.kf_current_page == 1 and self.kf_total_page == 1:
            self.isdisable_kf_page("before", True)
            self.isdisable_kf_page("next", True)
        elif self.kf_current_page == 1 and self.kf_current_page < self.kf_total_page:
            self.isdisable_kf_page("before", True)
            self.isdisable_kf_page("next", False)
        elif 1 < self.kf_current_page < self.kf_total_page:
            self.isdisable_kf_page("before", False)
            self.isdisable_kf_page("next", False)
        elif self.kf_current_page == self.kf_total_page:
            self.isdisable_kf_page("before", False)
            self.isdisable_kf_page("next", True)
        self.current_page_lb_kf.setText("第{}页".format(str(self.kf_current_page)))

    def isdisable_kf_page(self, page_name, status):
        if status:
            getattr(self, f"{page_name}_page_lb_kf").setStyleSheet("""
                             QLabel {
                            border: 1px solid gray; /* 边框宽度和颜色 */
                            border-radius: 10px;   /* 边框圆角 */
                            padding: 10px;        /* 内边距 */
                          color:   gray
                        }
                    """)
        else:
            getattr(self, f"{page_name}_page_lb_kf").setStyleSheet("""
                                          QLabel {
                        border: 1px solid #295792; /* 边框宽度和颜色 */
                        border-radius: 10px;   /* 边框圆角 */
                        padding: 10px;        /* 内边距 */
        			    color:   #295792}
                      QLabel:hover {
                     color: white; 
                    background-color:#295792}
                                    }
                                """)

    @calculate_time
    def show_kfjj_widget(self):
        #default
        today = date.today()
        last_year_today = today - timedelta(days=365)
        last_year_today = last_year_today.strftime("%Y-%m-%d")
        self.kf_sort_check.stateChanged.connect(self.on_checkbox_state_changed)
        self.kf_current_page = 1
        self.sort_dep_index=11#近一年
        self.tab_index=0
        self.start_date=last_year_today
        self.end_date=today.strftime("%Y-%m-%d")

        #对第一个网页内容提取，渲染顶部按钮，以及页面
        #对所有顶部按钮多线程加载绑定数据
        self.bl_kf_pos, self.gr_kf_pos, self.top_lb, self.tb_header_lb = kfjj_data.get_kj_type_info()
        data, data_info = kfjj_data.return_kf_data_1(self.kf_jj_index)
        self.kf_total_page = data_info["allPages"]
        kf_top_info = [data_info["allRecords"], data_info["zs_count"], data_info["gp_count"], data_info["hh_count"],
                       data_info["zq_count"], data_info["qdii_count"], data_info["fof_count"]]
        for i in range(1, 8):
            getattr(self, f"kf_top_lb_{i}").setText(f"{self.top_lb[i - 1]}({str(kf_top_info[i - 1])})")
        self.kfjj_widget.show()
        self.kfjj_tableview_style(self.kf_jj_index, data,data_info)
        self.kf_loading_lb.setText("加载完成")
        self.tableView.horizontalHeader().sectionClicked.connect(self.kf_header_clicked)  # 监听表头点击事件
        self.label_8.setText(f"起始日期: {self.start_date}, 结束日期: {self.end_date}")


    def load_kfjj_widget(self,index1):
        s=time.time()
        data, data_info = kfjj_data.return_kf_data_1(index1)
        self.kfjj_tableview_style(index1,data,data_info)
        self.kf_jj_sub_index=0
        self.kf_loading_lb.setText("加载耗时:"+"{:.2f}s".format(time.time()-s))
        self.kf_current_page=1
        self.current_page_lb_kf.setText("第{}页".format(str(self.kf_current_page)))

    def kf_header_clicked(self,index):#排序依据
        self.sort_dep_index = index  # 近一年
        self.kf_current_page = 1
        self.load_kf_data_params(event=None,index=self.kf_jj_index,tab_index=self.kf_jj_sub_index,page=self.kf_current_page,
                                                                      sort_dep=index,sort=self.kf_sort,start_date=self.start_date,end_date=self.end_date)
        self.update_kf_page()


    def load_kf_data_params(self,event,index,tab_index,page,sort_dep,sort,start_date,end_date):
        self.st = time.time()
        self.kf_loading_lb.setText("加载中...")
        self.kf_jj_sub_index=tab_index
        self.kf_jj_index=index
        self.kf_current_page=page
        self.sort_dep_index=sort_dep
        self.kf_sort_check.stateChanged.connect(self.on_checkbox_state_changed)
        print(self.kf_sort)
        # self.start_date=start_date
        # self.end_date=end_date
        # 创建并启动子线程
        self.worker_thread_kf =kfjj_data_params(index=index,tab_index=tab_index, page=page,sort_dep=sort_dep,sort=sort,start_date=start_date,end_date=end_date)
        self.worker_thread_kf.finished.connect(self.task_finished_kf)
        self.worker_thread_kf.start()

    def load_kf_data_params_pre(self,event, index, tab_index, page, sort_dep, sort, start_date, end_date):
        self.kf_jj_sub_index=tab_index
        self.kf_current_page=1
        self.load_kf_data_params(event=None,index=index,tab_index=tab_index, page=page,sort_dep=sort_dep,sort=sort,start_date=start_date,end_date=end_date)

    def task_finished_kf(self, data, data_info):
        self.kf_total_page=data_info["allPages"]
        self.et = time.time()
        self.kfjj_tableview_style(self.kf_jj_index,data,data_info)
        self.kf_loading_lb.setText("本次加载共耗时:{:.2f}s".format(self.et-self.st))



    def select_kfjj_btn(self,index1):

        self.kf_jj_index=index1
        if index1==1:
            self.kf_qdii_tab_widget.hide()
            self.kf_zq_tab_widget.hide()
            self.kf_zs_tab_widget.show()

            for i in range(1, 9):
                getattr(self, f"kf_zs_tab_{i}").setStyleSheet("background-color:none")

            getattr(self, f"kf_zs_tab_{self.kf_jj_sub_index + 1}").setStyleSheet("""
                           QLabel {
                    color:white;  
                    border: 1; /* 移除边框 */  
                    background-color: qlineargradient(  
                        x1: 0, y1: 0,  
                        x2: 1, y2: 0,  
                        stop: 0 #6495ED, 
                        stop: 1 #00FA9A
                    );  
                    border-radius: 5px; /* 圆角 */  
                    padding:5px
                }  
                    """)
            self.update_kf_page()
        elif index1==4:
            self.kf_qdii_tab_widget.hide()
            self.kf_zs_tab_widget.hide()
            self.kf_zq_tab_widget.show()

            for i in range(1, 7):
                getattr(self, f"kf_zq_tab_{i}").setStyleSheet("background-color:none")
            getattr(self, f"kf_zq_tab_{self.kf_jj_sub_index + 1}").setStyleSheet("""
                           QLabel {
                    color:white;  
                    border: 1; /* 移除边框 */  
                    background-color: qlineargradient(  
                        x1: 0, y1: 0,  
                        x2: 1, y2: 0,  
                        stop: 0 #6495ED, 
                        stop: 1 #00FA9A
                    );  
                    border-radius: 5px; /* 圆角 */  
                    padding:5px
                }  
                    """)
            self.update_kf_page()
        elif index1==5:
            self.kf_zs_tab_widget.hide()
            self.kf_zq_tab_widget.hide()
            self.kf_qdii_tab_widget.show()

            for i in range(1, 9):
                getattr(self, f"kf_qdii_tab_{i}").setStyleSheet("background-color:none")
            getattr(self, f"kf_qdii_tab_{self.kf_jj_sub_index + 1}").setStyleSheet("""
                           QLabel {
                    color:white;  
                    border: 1; /* 移除边框 */  
                    background-color: qlineargradient(  
                        x1: 0, y1: 0,  
                        x2: 1, y2: 0,  
                        stop: 0 #6495ED, 
                        stop: 1 #00FA9A
                    );  
                    border-radius: 5px; /* 圆角 */  
                    padding:5px
                }  
                    """)
            self.update_kf_page()
        else:
            self.kf_zs_tab_widget.hide()
            self.kf_zq_tab_widget.hide()
            self.kf_qdii_tab_widget.hide()
        for i in range(1, 8):
            getattr(self, f"kf_top_lb_{i}").setStyleSheet("background-color:none")
        getattr(self,f"kf_top_lb_{index1+1}").setStyleSheet("""
                QPushButton {
                    color:white;  
                    border: 1; /* 移除边框 */  
                    background-color: qlineargradient(  
                        x1: 0, y1: 0,  
                        x2: 1, y2: 0,  
                        stop: 0 #FFB6C1, 
                        stop: 1 #8A2BE2 
                    );  
                    border-radius: 5px; /* 圆角 */  
                    padding:5px
                }  
        """)
        for i in range(1, 9):
            getattr(self, f"kf_zs_tab_{i}").mousePressEvent = partial(self.load_kf_data_params_pre, index=1,
                                                                      tab_index=i - 1, page=self.kf_current_page,
                                                                      sort_dep=self.sort_dep_index, sort=self.kf_sort,
                                                                      start_date=self.start_date,
                                                                      end_date=self.end_date)
        for i in range(1, 7):
            getattr(self, f"kf_zq_tab_{i}").mousePressEvent = partial(self.load_kf_data_params_pre, index=4,
                                                                      tab_index=i - 1, page=self.kf_current_page,
                                                                      sort_dep=self.sort_dep_index, sort=self.kf_sort,
                                                                      start_date=self.start_date,
                                                                      end_date=self.end_date)
        for i in range(1, 9):
            getattr(self, f"kf_qdii_tab_{i}").mousePressEvent = partial(self.load_kf_data_params_pre, index=5,
                                                                        tab_index=i - 1, page=self.kf_current_page,
                                                                        sort_dep=self.sort_dep_index, sort=self.kf_sort,
                                                                        start_date=self.start_date,
                                                                        end_date=self.end_date)
        self.update_kf_page()



    def kfjj_tableview_style(self,index,data,data_info):
        self.kf_total_page = data_info["allPages"]
        self.select_kfjj_btn(index)
        self.model = QStandardItemModel()
        self.model.setHorizontalHeaderLabels(self.tb_header_lb)
        for row in data:
            row_items = [QStandardItem(str(item)) for item in row]
            self.model.appendRow(row_items)
        self.tableView.setModel(self.model)
        # self.tableView.setStyleSheet("QTableView::item { text-align: center; }")
        # 设置自定义委托，使文本水平居中
        # delegate = CenteredItemDelegate(self.tableView)
        # self.tableView.setItemDelegate(delegate)
        self.tableView.setColumnWidth(0, 73)
        self.tableView.setColumnWidth(1, 260)
        for i in range(4, 16):
            self.tableView.setColumnWidth(i, 78)
        # 设置表头字段行的样式
        self.tableView.horizontalHeader().setStyleSheet(
            "QHeaderView::section {"
            "   background-color: #FF81ED;"  # 背景颜色
            "   color: white;"  # 字体颜色
            "   padding: 5px;"  # 内边距
            "   font-weight: bold;"  # 字体加粗
            "}"
        )
        self.tail_page_lb_kf.setText(str(self.kf_total_page))
        self.update_kf_page()
        delegate = CustomDelegate()
        self.tableView.setItemDelegate(delegate)

    def sub_page_dt(self,event):
        if self.dt_current_page>1:
            self.dt_current_page-=1
            self.load_dt_web(self.dt_jj_index,self.dt_current_page)
            self.update_dt_page()  # 更新dt页按钮
            self.current_page_lb_dt.setText("第{}页".format(str(self.dt_current_page)))

    def add_page_dt(self,event):
        if self.dt_current_page < self.dt_total_page:
            self.dt_current_page += 1
            self.load_dt_web(self.dt_jj_index, self.dt_current_page)
            self.update_dt_page()  # 更新dt页按钮
            self.current_page_lb_dt.setText("第{}页".format(str(self.dt_current_page)))
            self.kf

    def tail_page_dt(self,event):
        self.dt_current_page=self.dt_total_page
        self.load_dt_web(self.dt_jj_index, self.dt_total_page)
        self.update_dt_page()
        self.current_page_lb_dt.setText("第{}页".format(str(self.dt_current_page)))

    def first_page_dt(self,event):
        self.dt_current_page =1
        self.load_dt_web(self.dt_jj_index,1)
        self.update_dt_page()
        self.current_page_lb_dt.setText("第{}页".format(str(self.dt_current_page)))

    def goto_page_dt(self,event):
        if int(self.goto_Edit_dt.text())>self.dt_total_page or int(self.goto_Edit_dt)<0:
            self.goto_Edit_dt.setText("")
        else:
            self.dt_current_page=int(self.goto_Edit_dt.text())
            self.load_dt_web(self.dt_jj_index, self.dt_current_page)
            self.update_dt_page()
            self.current_page_lb_dt.setText("第{}页".format(str(self.dt_current_page)))

    def update_dt_page(self):
        if self.dt_total_page == 1:
            self.goto_widget_2.hide()
        else:
            self.goto_widget_2.show()
        # 对按钮显示判断
        if self.dt_current_page == 1 and self.dt_total_page == 1:
            self.isdisable_dt_page("before", True)
            self.isdisable_dt_page("next", True)
        elif self.dt_current_page == 1 and self.dt_current_page < self.dt_total_page:
            self.isdisable_dt_page("before", True)
            self.isdisable_dt_page("next", False)
        elif 1 < self.dt_current_page < self.dt_total_page:
            self.isdisable_dt_page("before", False)
            self.isdisable_dt_page("next", False)
        elif self.dt_current_page == self.dt_total_page:
            self.isdisable_dt_page("before", False)
            self.isdisable_dt_page("next", True)

    def isdisable_dt_page(self,page_name,status):
        if status:
            getattr(self, f"{page_name}_page_lb_dt").setStyleSheet("""
                             QLabel {
                            border: 1px solid gray; /* 边框宽度和颜色 */
                            border-radius: 10px;   /* 边框圆角 */
                            padding: 10px;        /* 内边距 */
                          color:   gray
                        }
                    """)
        else:
            getattr(self, f"{page_name}_page_lb_dt").setStyleSheet("""
                                          QLabel {
                        border: 1px solid #295792; /* 边框宽度和颜色 */
                        border-radius: 10px;   /* 边框圆角 */
                        padding: 10px;        /* 内边距 */
        			    color:   #295792}
                      QLabel:hover {
                     color: white; 
                    background-color:#295792}
                                    }
                                """)

    def show_dt_widget(self):
        self.dt_current_page=1
        self.dt_list_Widget.itemClicked.connect(self.on_dtitem_clicked)
        self.top_dtph_widget.show()
        self.load_dt_web(0,1)
        self.dt_list_name=[]
        for i in range(self.dt_list_Widget.count()):
            item = self.dt_list_Widget.item(i)
            self.dt_list_name.append(item.text())  # 打印列表项的文本

    def on_dtitem_clicked(self,item):
        self.dt_jj_index=self.dt_list_name.index(item.text())
        self.load_dt_web(self.dt_jj_index,1)
        self.dt_current_page=1

    def load_dt_web(self,index,page):
        self.s = time.time()
        self.dt_loading_lb.setText("加载中...")
        # 创建并启动子线程
        self.worker_thread = dt_web(index=index, page=page)
        self.worker_thread.finished.connect(self.task_finished)
        self.worker_thread.start()

    def task_finished(self, web_data, page1):
        self.dt_total_page=page1
        self.e = time.time()
        self.textBrowser.setHtml(web_data)
        self.tail_page_lb_dt.setText(str(page1))
        self.dt_loading_lb.setText("本次加载共耗时:{:.2f}s".format(self.e-self.s))

    """top_切换widget"""
    def change_left_widget(self,event):
        if self.top_widget_index==1:
            self.left_widget_change.setStyleSheet("""color: gray; }""")
            self.top_widget_index-=1
        elif 0<self.top_widget_index<=3:
            self.left_widget_change.setStyleSheet("""QLabel:hover { color: #FF4400; }""")
            self.right_widget_change.setStyleSheet("""QLabel:hover { color: #FF4400; }""")
            self.top_widget_index -= 1
        self.show_hide_top_widget(self.top_widget_index)

    def change_right_widget(self,event):
        if self.top_widget_index==2:
            self.top_widget_index+=1
            self.right_widget_change.setStyleSheet("""color: gray; }""")
        elif 0<=self.top_widget_index<3:
            self.left_widget_change.setStyleSheet("""QLabel:hover { color: #FF4400; }""")
            self.right_widget_change.setStyleSheet("""QLabel:hover { color: #FF4400; }""")
            self.top_widget_index += 1
        self.show_hide_top_widget(self.top_widget_index)



    def show_hide_top_widget(self,index):
        widget_list = ["tz_tools_widget", "tz_compute_widget", "zx_widget", "chatai_widget"]
        for i in range(4):
            if i == index:
                getattr(self, f"{widget_list[index]}").show()
            else:
                getattr(self, f"{widget_list[i]}").hide()
        match (index):
            case 0:
                print("主页")
                self.close_calculator_widget()
            case 1:
                print("计算器")
                self.open_calculator_widget()
            case 2:
                print("资讯")
                self.close_calculator_widget()
            case 3:
                print("ai聊天")
                self.close_calculator_widget()
        print(self.top_widget_index)




    def handle_mouse_pressself(self,event,name,):
        self.change_table(name, event)

    def change_table(self,table_name,event):
        self.current_page=1
        self.current_page_lb.setText("1")
        self.btn_index=0
        if event.button() == Qt.MouseButton.LeftButton:
            self.index_table_status = True
            self.total_tb_widget.show()
        headers, cols, self.btn_name,self.blue_pos,self.gr_pos = table_data.get_table_data(table_name)  # 调用获取数据api 获取page_url_l ，headers，，col
        print(headers, cols, self.btn_name,self.blue_pos,self.gr_pos)
        data_l,len_data_l,self.total_page=table_data.return_index_tb(self.btn_index,self.current_page)

        data_l=self.is_limit( data_l,len_data_l)
        print(len(data_l))
        self.table_r_c = [20, 20, 20, 20,cols]
        #样式渲染的函数
        self.btn_conn()
        self.table_btn_i(data_l, self.total_page,len_data_l, cols, self.btn_index,self.blue_pos,self.gr_pos)#让每个标题的表的不同按钮绑定正确数据
        #渲染数据
        self.first_page_lb.setStyleSheet("""
                         QLabel {
                                border: 1px solid #295792; /* 边框宽度和颜色 */
                                border-radius: 10px;   /* 边框圆角 */
                                padding: 10px;        /* 内边距 */
                			  color:  white;
                			  background-color:#295792

                            }
                        """)
        self.tail_page_lb.setStyleSheet("""
                             QLabel {
                                border: 1px solid #295792; /* 边框宽度和颜色 */
                                border-radius: 10px;   /* 边框圆角 */
                                padding: 10px;        /* 内边距 */
                			  color:   #295792

                            }
                              QLabel:hover {
                             color: white; 
                            background-color:#295792}
                        """)
        for col, header in enumerate(headers): self.top_tableWidget.setHorizontalHeaderItem(col,QTableWidgetItem(header))
        def process_button(i):
            # 调用 table_btn_i 函数
            data,len_data_rr,page=table_data.return_index_tb(i-1,1)
            getattr(self, f"tb_btn_i_{i}").clicked.connect(
                partial(self.table_btn_i, self.is_limit(data,len_data_rr),page, len_data_l, self.table_r_c[-1], i - 1,
                        self.blue_pos, self.gr_pos)
            )
            self.current_page=1

        # # 使用多线程处理按钮逻辑
        with concurrent.futures.ThreadPoolExecutor() as executor:
            futures = [executor.submit(process_button, i) for i in range(1, len(self.btn_name) + 1)]
            for future in concurrent.futures.as_completed(futures):
                try:
                    future.result()  # 获取线程执行结果
                except Exception as e:
                    print(f"Error processing button: {e}")
        print("page:" + str(self.total_page))
        self.update_page()
    def is_limit(self,data, len_data_l):
        if len_data_l > 20:
            data_l = data[:20]
        else:
            data_l = data[:len_data_l]
        return data_l

    def sub_page(self, event):
        if self.current_page >1:
            self.current_page -= 1
            self.update_page()
            data_l, len_data_l, self.total_page = table_data.return_index_tb(self.btn_index,self.current_page)
            self.table_btn_i(self.is_limit(data_l,len_data_l),self.total_page, len_data_l, self.table_r_c[-1], self.btn_index,
                            self.blue_pos, self.gr_pos)
            self.current_page_lb.setText(str(self.current_page))

    def add_page(self, event):
        if self.current_page <self.total_page:
            self.current_page += 1
            self.update_page()
            data_l, len_data_l, self.total_page = table_data.return_index_tb(self.btn_index,self.current_page)
            self.table_btn_i(self.is_limit(data_l, len_data_l), self.total_page, len_data_l, self.table_r_c[-1],
                             self.btn_index,
                             self.blue_pos, self.gr_pos)
            self.current_page_lb.setText(str(self.current_page))
    def tail_page(self,event):
        print(self.total_page)
        self.current_page=self.total_page
        self.update_page()
        data_l, len_data_l, total_page = table_data.return_index_tb(self.btn_index, self.total_page)
        self.table_btn_i(self.is_limit(data_l, len_data_l), total_page, len_data_l, self.table_r_c[-1],
                         self.btn_index,
                         self.blue_pos, self.gr_pos)
        self.current_page_lb.setText(str(self.current_page))
        self.tail_page_lb.setStyleSheet("""
         QLabel {
                border: 1px solid #295792; /* 边框宽度和颜色 */
                border-radius: 10px;   /* 边框圆角 */
                padding: 10px;        /* 内边距 */
			  color:  white;
			  background-color:#295792

            }
        """)
        self.first_page_lb.setStyleSheet("""
             QLabel {
                border: 1px solid #295792; /* 边框宽度和颜色 */
                border-radius: 10px;   /* 边框圆角 */
                padding: 10px;        /* 内边距 */
			  color:   #295792

            }
              QLabel:hover {
             color: white; 
            background-color:#295792}
        """)

    def first_page(self,event):
        self.current_page =1
        self.update_page()
        data_l, len_data_l, total_page = table_data.return_index_tb(self.btn_index, 1)
        self.table_btn_i(self.is_limit(data_l, len_data_l), total_page, len_data_l, self.table_r_c[-1],
                         self.btn_index,
                         self.blue_pos, self.gr_pos)
        self.current_page_lb.setText(str(self.current_page))
        self.first_page_lb.setStyleSheet("""
                 QLabel {
                        border: 1px solid #295792; /* 边框宽度和颜色 */
                        border-radius: 10px;   /* 边框圆角 */
                        padding: 10px;        /* 内边距 */
                        color:  white;
                        background-color:#295792
                    }
                """)
        self.tail_page_lb.setStyleSheet("""
                     QLabel {
                        border: 1px solid #295792; /* 边框宽度和颜色 */
                        border-radius: 10px;   /* 边框圆角 */
                        padding: 10px;        /* 内边距 */
        			  color:   #295792

                    }
                      QLabel:hover {
                     color: white; 
                    background-color:#295792}
                """)

    def goto_page(self,event):
        if int(self.goto_Edit.text()) >self.total_page or int(self.goto_Edit.text()) < 0:
            self.goto_Edit.setText("")
        else:
            self.current_page = int(self.goto_Edit.text())
            self.update_page()
            data_l, len_data_l, total_page = table_data.return_index_tb(self.btn_index, self.current_page)
            self.table_btn_i(self.is_limit(data_l, len_data_l), total_page, len_data_l, self.table_r_c[-1],
                             self.btn_index,
                             self.blue_pos, self.gr_pos)
            self.current_page_lb.setText(str(self.current_page))
            self.goto_Edit.setText("")

    def isdisable_page(self,page_name,status):
        if status:
            getattr(self,f"{page_name}_page_lb").setStyleSheet("""
                     QLabel {
                    border: 1px solid gray; /* 边框宽度和颜色 */
                    border-radius: 10px;   /* 边框圆角 */
                    padding: 10px;        /* 内边距 */
                  color:   gray
                }
            """)
        else:
            getattr(self, f"{page_name}_page_lb").setStyleSheet("""
                                  QLabel {
                border: 1px solid #295792; /* 边框宽度和颜色 */
                border-radius: 10px;   /* 边框圆角 */
                padding: 10px;        /* 内边距 */
			    color:   #295792}
              QLabel:hover {
             color: white; 
            background-color:#295792}
                            }
                        """)

    def update_page(self):
        print("pd")
        if self.total_page==1:
            self.goto_widget.hide()
        else:
            self.goto_widget.show()
        # 对按钮显示判断
        if self.current_page==1 and self.total_page==1:
            self.isdisable_page("before",True)
            self.isdisable_page("next",True)
        elif self.current_page==1 and self.current_page<self.total_page:
            self.isdisable_page("before", True)
            self.isdisable_page("next", False)
        elif 1<self .current_page<self.total_page:
            self.isdisable_page("before", False)
            self.isdisable_page("next", False)
        elif self.current_page==self.total_page:
            self.isdisable_page("before", False)
            self.isdisable_page("next", True)

    def table_btn_i(self,data,page,rows,cols,index,blue_cols,gr_cols):
        try:
            # self.current_page=1
            print("c_page"+str(self.current_page))
            self.total_page=page
            if index!=self.btn_index:
                self.current_page=1
                self.current_page_lb.setText("1")
            self.btn_index=index
            color=["#294EBC","#009900","#FF0018","black"]#蓝，绿，红
            #对表格按钮样式进行渲染
            print(rows,cols)
            self.top_tableWidget.setColumnCount(cols)
            self.top_tableWidget.setRowCount(rows)
            for row in range(rows):
                for col in range(cols):
                    item = QTableWidgetItem(f"{data[row][col]}")
                    item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    item.setBackground(QColor('#FFFFFF')) if row%2 else item.setBackground(QColor('#F2F2F2'))
                    for i in blue_cols:
                        if col+1==i:
                            item.setForeground(QColor(color[0]))
                    for j in gr_cols:  #传入 -12 13.45  -12.0  |  -12% -
                        if col + 1 == j and isinstance(data[row][col], float):
                             if data[row][col]<0 :
                                 item.setForeground(QColor(color[1]))
                             elif data[row][col]==0:
                                 item.setForeground(QColor(color[-1]))
                             else :item.setForeground(QColor(color[2]))
                        elif col + 1 == j and isinstance(data[row][col], int):
                            item.setForeground(QColor(color[1])) if data[row][col]<0  else item.setForeground(QColor(color[2]))
                        elif col + 1 == j and isinstance(data[row][col], str) and data[row][col]=="-":
                            item.setForeground(QColor(color[-1]))
                        elif col + 1 == j and isinstance(data[row][col], str):
                            # print(data[row][col],type(data[row][col]))
                            da = float(data[row][col].replace("%",""))
                            if da < 0 :
                                item.setForeground(QColor(color[1]))
                            elif da==0:
                                item.setForeground(QColor(color[-1]))
                            else :item.setForeground(QColor(color[2]))

                    self.top_tableWidget.setItem(row, col, item)
            for i in range(1,5):
                getattr(self, f"tb_btn_i_{i}").setStyleSheet("background-color:none")
            print("kkk")
            getattr(self, f"tb_btn_i_{index+1}").setStyleSheet("""  
                        QPushButton {
                            color:white;  
                            border: 1; /* 移除边框 */  
                            background-color: qlineargradient(  
                                x1: 0, y1: 0,  
                                x2: 1, y2: 0,  
                                stop: 0 #FFB6C1, 
                                stop: 1 #8A2BE2 
                            );  
                            border-radius: 5px; /* 圆角 */  
                            padding:5px
                        }  
                    """)
            """调整列宽，使所有列平均占满表格宽度"""
            table_width = self.top_tableWidget.width()  # 获取表格宽度
            column_count = self.top_tableWidget.columnCount()  # 获取列数
            if column_count > 0:
                column_width = table_width // column_count  # 计算每列的宽度
                for col in range(column_count):
                    self.top_tableWidget.setColumnWidth(col, column_width)
            self.top_tableWidget.horizontalHeader().setStyleSheet("""
                                    QHeaderView::section {
                                        background-color: #EBF6FE;  /* 背景颜色 */
                                        color: black;             /* 字体颜色 */
                                        padding-left: 5px;
                                        padding-right: 5px;
                                    }
                                """)
            self.top_tableWidget.setStyleSheet("""
                                    QTableWidget {
                                        gridline-color:#B9D4E7;
                                        border: 1px solid #BBD4E8;  /* 边框颜色 */
                                    }
                                """)
            self.tail_page_lb.setText(str(page))
            self.update_page()
            start=(self.current_page-1)*20+1
            end=start+rows
            self.top_tableWidget.setVerticalHeaderLabels([str(i) for i in range(start,end)])
        except Exception as e:
            print(e)

    def btn_conn(self,):
        for i in range(1,5):
            btn = getattr(self, f"tb_btn_i_{i}")
            btn.show() if i-1<len(self.btn_name) else btn.hide()
        for i in range(0,len(self.btn_name)):
            getattr(self, f"tb_btn_i_{i + 1}").setText(self.btn_name[i])

    """横屏移动消息"""
    def scroll_text(self):
        self.message = self.message[1:] + self.message[0]
        self.textEdit_2.setPlainText(self.message)
        if self.message.strip() == self.initial_message.strip():
            self.message = self.initial_message + " " * 50

    def update_message(self, new_message):
        self.initial_message = new_message
        self.message = self.initial_message + " " * 50
        self.textEdit_2.setPlainText(self.message)



    def open_zjph_window(self,event):
        ZJPHWindow().show()

    """网络图片显示"""
    def load_image(self,url):
        self.network_manager.get(QNetworkRequest(QUrl(url)))

    def on_image_downloaded(self, reply):
        if reply.error() == QNetworkReply.NetworkError.NoError:

            pixmap = QPixmap()
            pixmap.loadFromData(reply.readAll())
            self.label_88.setPixmap(pixmap)
            # self.index_hs_png_2.setPixmap()
        else:
            self.label.setText("图片访问失败")
        reply.deleteLater()



    def info_mouse_press_event(self, event):
        super(QLineEdit, self.info_search_textEdit).mousePressEvent(event)  # 调用父类方法，确保正常行为
        self.show_search_widget()


    def toggle_left_hy_arrow(self,event):
        if self.left_hy_status==1:#当前跌幅榜
            self.left_bang_index += 1
            self.label_107.setPixmap(QPixmap(self.img_path+'/red_up.png').scaled(15, 25,))
            self.left_hy_status = 0
        elif self.left_hy_status==0:#当前涨幅榜
            self.left_bang_index -= 1
            self.label_107.setPixmap(QPixmap(self.img_path+'/green_down.png').scaled(15, 25, ))
            self.left_hy_status = 1
        self.l_center_lb.setText(self.left_table[self.left_bang_index])
    def toggle_kf_index_widget(self,event):
        if self.index_kf_status:
            self.kf_current_page = 1
            self.index_kf_status = False
            self.index_widget.show()
            self.index_top_widget.show()
            self.index_top2_widget.show()
            self.kfjj_widget.hide()
        else:
            self.show_kfjj_widget()
            # self.kfjj_widget.show()
            self.index_top_widget.hide()
            self.index_widget.hide()
            self.index_top2_widget.hide()
            self.index_kf_status = True
    def toggle_cal_widget(self,event):
        if self.kf_cal_status:
            self.kf_cal_status=False
            self.calendarWidget.hide()
        else:
            self.kf_cal_status = True
            self.calendarWidget.show()

    def load_info_detail(self,index):
        # self.info_index=10 * (self.load_new_n - 1) + index
        article_title = self.title_l[index]
        article_url = self.info_url[10 * (self.load_new_n - 1) + index]
        self.show_detail_widget(self.title_l[index], self.tip_l[index],
                                self.article_l[index])
        self.update_view_history(self.title_l[index],
                                 self.info_url[10 * (self.load_new_n - 1) + index])
        self.info_collect_lb_3.mousePressEvent = partial(self.open_url, url=article_url)
        self.info_collect_lb_2.mousePressEvent = partial(self.copy_to_clipboard, url=article_url)
        self.check_collect(title=article_title, url=article_url)
        self.info_detail_status = True

        self.update_info_list()
        self.info_detail_listWidget.itemClicked.connect(self.load_info_list_detail)


    def toggle_info_detail(self,event,index):#[0-9]
        try:
            if self.info_detail_status:
                self.info_detail_widget.hide()
                self.info_detail_status=False
                self.load_view_history()
            else:
                self.info_detail_return_lb_2.hide()
                self.info_detail_return_lb_3.hide()
                self.info_detail_return_lb.show()
                self.info_detail_widget.show()
                self.load_info_detail(index)
                self.info_list_lb.setText("最新新闻")
        except Exception as e:
            print(e)


    def update_collect_list(self):
        self.info_collect_item_list_name = []
        for i in range(self.info_collect_widget.count()):
            item = self.info_collect_widget.item(i)
            self.info_collect_item_list_name.append(item.text())

    def update_history_list(self):
        self.info_history_item_list_name = []
        for i in range(self.info_history_widget.count()):
            item = self.info_history_widget.item(i)
            self.info_history_item_list_name.append(item.text())

    def toggle_info_collect_list_detail(self,item):
        try:
                self.info_collect_item_index = self.info_collect_item_list_name.index(item.text())
                with open(r"api/widget/information/collect", "r", encoding="utf8") as f:
                    collect_list = f.readlines()[::-1]
                article_title = collect_list[self.info_collect_item_index].split("|")[0].strip()
                article_url = collect_list[self.info_collect_item_index].split("|")[1].replace("\n", "")
                a = []
                a.append(article_url)
                article_info = asyncio.run(url_infos(a).main())[0]
                article_tip = "发布时间： " + article_info[0] + "  来源： " + article_info[1]
                article_content = article_info[2]
                self.info_detail_widget.show()
                self.info_detail_return_lb_2.show()
                self.info_detail_return_lb.hide()
                self.info_detail_return_lb_3.hide()
                self.show_detail_widget(article_title,article_tip,article_content)
                # self.update_view_history(article_title,self.info_url[10*self.load_new_n+self.info_index])
                self.check_collect(title=article_title,url=article_url)
                self.info_detail_status_2 = True
                self.info_list_lb.setText("全部收藏")
                self.update_detail_collect_list()
                # self.update_detail_viewed_list()
                self.info_detail_listWidget.itemClicked.connect(self.load_collect_list_detail)
                self.info_collect_lb_3.mousePressEvent = partial(self.open_url, url=article_url)
                self.info_collect_lb_2.mousePressEvent = partial(self.copy_to_clipboard, url=article_url)
        except Exception as e:
            print(e)

    def toggle_info_history_list_detail(self,item):
        try:
            self.info_history_item_index = self.info_history_item_list_name.index(item.text())
            with open(r"api/widget/information/view_history", "r", encoding="utf8") as f:
                collect_list = f.readlines()[::-1]
            article_title = collect_list[self.info_history_item_index].split("|")[0].strip()
            article_url = collect_list[self.info_history_item_index].split("|")[1].replace("\n", "")
            a = []
            a.append(article_url)
            article_info = asyncio.run(url_infos(a).main())[0]
            article_tip = "发布时间： " + article_info[0] + "  来源： " + article_info[1]
            article_content = article_info[2]
            self.info_detail_widget.show()
            self.info_detail_return_lb_3.show()
            self.info_detail_return_lb_2.hide()
            self.info_detail_return_lb.hide()
            self.show_detail_widget(article_title,article_tip,article_content)
            # self.update_view_history(article_title,self.info_url[10*self.load_new_n+self.info_index])
            self.check_collect(title=article_title,url=article_url)
            self.info_collect_lb_3.mousePressEvent = partial(self.open_url, url=article_url)
            self.info_collect_lb_2.mousePressEvent = partial(self.copy_to_clipboard, url=article_url)
            self.update_collect_list()
            self.info_detail_status_3 = True
            self.info_list_lb.setText("全部浏览历史")
            self.update_detail_viewed_list()
            self.info_detail_listWidget.itemClicked.connect(self.load_viewed_list_detail)
        except Exception as e:
            print(e)

    def load_info_list_detail(self,item):#从首页进入文章详情页面的
        try:
            self.info_item_index = self.info_title_l.index(item.text())
            article_title = self.info_title_l[self.info_item_index].strip()
            article_url = self.info_url[self.info_item_index].strip()
            a = []
            a.append(article_url)
            article_info = asyncio.run(url_infos(a).main())[0]
            article_tip = "发布时间： " + article_info[0] + "  来源： " + article_info[1]
            article_content = article_info[2]
            self.show_detail_widget(article_title,article_tip,article_content)
            self.check_collect(title=article_title,url=article_url)
        except Exception as e:
            print(e)

    def load_viewed_list_detail(self,item):#从首页进入文章详情页面的
        try:
            self.info_item_index = self.view_history_title.index(item.text())
            article_title = self.view_history_title[self.info_item_index].strip()
            article_url = self.view_history_url[self.info_item_index].strip()
            a = []
            a.append(article_url)
            article_info = asyncio.run(url_infos(a).main())[0]
            article_tip = "发布时间： " + article_info[0] + "  来源： " + article_info[1]
            article_content = article_info[2]
            self.show_detail_widget(article_title,article_tip,article_content)
            self.check_collect(title=article_title,url=article_url)
        except Exception as e:
            print(e)

    def load_collect_list_detail(self,item):#从首页进入文章详情页面的
        try:
            self.info_item_index = self.collect_title.index(item.text())
            article_title = self.collect_title[self.info_item_index].strip()
            article_url = self.collect_url[self.info_item_index].strip()
            a = []
            a.append(article_url)
            article_info = asyncio.run(url_infos(a).main())[0]
            article_tip = "发布时间： " + article_info[0] + "  来源： " + article_info[1]
            article_content = article_info[2]
            self.show_detail_widget(article_title,article_tip,article_content)
            self.check_collect(title=article_title,url=article_url)
        except Exception as e:
            print(e)

    def update_info_list(self):#刷新最新新闻列表项
        self.info_detail_listWidget.clear()
        for i in range(len(self.info_title_l)):
            self.info_detail_listWidget.addItem(self.info_title_l[i])

    def update_detail_viewed_list(self):#刷新最新新闻列表项
        self.info_detail_listWidget.clear()
        with open(r"api/widget/information\view_history", "r", encoding="utf8") as f:
            view_history = f.readlines()
        self.view_history_title=[i.split("|")[0] for i in view_history]
        self.view_history_url=[i.split("|")[1] for i in view_history]
        for i in view_history [::-1]:
            self.info_detail_listWidget.addItem(i.split("|")[0])

    def update_detail_collect_list(self):  # 刷新最新新闻列表项
        self.info_detail_listWidget.clear()
        with open(r"api/widget/information\collect", "r", encoding="utf8") as f:
            collect_ = f.readlines()
        self.collect_title = [i.split("|")[0] for i in collect_]
        self.collect_url = [i.split("|")[1] for i in collect_]
        for i in collect_[::-1]:
            self.info_detail_listWidget.addItem(i.split("|")[0])

    def hide_info_detail(self,event):
        self.info_detail_widget.hide()
        self.info_detail_status_2 = False
        self.load_view_history()

    def hide_info_detail_his(self,event):
        self.info_detail_widget.hide()
        self.info_detail_status_3 = False
        self.load_view_history()

    def info_move_to(self,status):
        if status:
            self.scrollArea_3.move(30, 210)
            self.info_load_lb.move(1370,890)
            self.info_up_lb.move(1365,830)
            self.info_up_img.move(1370,790)
            self.info_search_textEdit.move(380, 80)
            self.info_search_btn.move(900, 81)
            self.info_search_widget.move(380,121)

        else:
            self.scrollArea_3.move(200, 210)
            self.info_load_lb.move(1540, 890)
            self.info_up_lb.move(1535, 830)
            self.info_up_img.move(1540, 790)
            self.info_search_textEdit.move(550,80)
            self.info_search_btn.move(1070,81)
            self.info_search_widget.move(550,121)

    def update_view_history(self,title,url):
        with open(r"api/widget/information\view_history", "r", encoding="utf8")as f:
            view_history=f.readlines()
        with open(r"api/widget/information\view_history", "a+",
                  encoding="utf8") as f:
            data=title+"|"+url+"\n"
            if data not in set(view_history):
                f.write(title+"|"+url+"\n")
            f.close()

        # self.load_view_history()

    def load_view_history(self):
        self.info_history_widget.clear()
        with open(r"api/widget/information\view_history", "r", encoding="utf8")as f:
            self.view_history=f.readlines()
            f.close()
        for i in self.view_history[::-1]:
            self.info_history_widget.addItem(i.split("|")[0])
        self.update_history_list()

    def toggle_info_history_list(self,event):
        if self.info_history_status:
            self.info_history_status = False
            self.info_history_widget.hide()
            # self.info_collect_widget.hide()
            self.info_move_to(False)
        else:
            self.info_collect_status = False
            self.info_history_status = True
            self.info_history_widget.show()
            self.info_collect_widget.hide()
            self.info_move_to(True)
            self.load_view_history()

    def toggle_info_collect_list(self, event):
        if self.info_collect_status:
            self.info_collect_status = False
            # self.info_history_widget.hide()
            self.info_collect_widget.hide()
            self.info_move_to(False)
        else:
            self.info_collect_status = True
            self.info_history_status = False
            self.info_collect_widget.show()
            self.info_history_widget.hide()
            self.info_move_to(True)
            self.load_collect()

    toggle_lz_hot_widget = toggle_lz_hot_widget
    toggle_list_news_widget = toggle_list_news_widget
    toggle_table_index_widget = toggle_table_index_widget
    toggle_dt_index_widget = toggle_dt_index_widget
    change_l_l_bang = change_l_l_bang
    change_l_r_bang = change_l_r_bang
    change_r_l_bang = change_r_l_bang
    change_r_r_bang = change_r_r_bang
    r_table_index = r_table_index
    toggle_send_stop = toggle_send_stop
    thread_finished = thread_finished

    change_chat_model = change_chat_model
    clear_context = clear_context
    send_question = send_question
    stop_response = stop_response
    add_user_message = add_user_message
    add_ai_message_stream = add_ai_message_stream
    toggle_send_stop_img = toggle_send_stop_img

