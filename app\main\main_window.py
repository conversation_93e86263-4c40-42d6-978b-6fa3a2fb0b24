# 数据api
from app.modules.FundCalculator.FundCalculator import FundCalculator
from app.modules.FundInformation.FundInformation import FundInformation
from app.api.widget.index.get_table_data import table_data
from app.api.widget.dtjj.get_dt_data import dt_web
from app.api.widget.information.get_m_infos_1 import url_infos_1
from app.api.widget.information.get_other_article import get_other_title
from app.api.widget.information.get_search_article import get_search_title
from app.api.widget.information.get_search_code import get_search_c
from app.api.widget.kf_hb_jj.get_kfjj_data_2 import kfjj_data_params
from app.api.widget.kf_hb_jj.get_kfjj_data import kfjj_data
from app.api.widget.kf_hb_jj.kfjj_ import CustomDelegate
from app.api.widget.information.get_title_data import get_title
from app.api.widget.information.get_m_infos import url_infos
from app.api.widget.information.get_search_data import search_data
from app.common.api.code_company import FundCompany
from app.modules.FundCompany_.FundCompany_ import FundCompany_
from app.modules.FundCompare.FundCompare import FundCompare
from app.modules.FundFilter.FundFilter import FundFilter
from app.modules.FundHome1.FundHome1 import FundHome1
from app.modules.FundHome2.FundHome2 import FundHome2
from app.modules.FundHome3.FundHome3 import FundHome3
from app.modules.FundNewFound.FundNewFound import FundNewFound
from app.modules.FundSelection.FundSelection import FundSelection
from app.modules.FundSubject.FundSubject import FundSubject
from app.modules.Fundhb.FundHb import FundHb
# from showZJLX import ZJPHWindow
from main_w import Ui_MainWindow
from app.api.other.wrapp import calculate_time
from app.api.widget.chat.function import toggle_send_stop,toggle_send_stop_img,thread_finished,change_chat_model,clear_context,send_question,stop_response,add_user_message,add_ai_message_stream
from PyQt6.QtCore import Qt
#基础模块
import asyncio, time
from datetime import datetime, timedelta, date
from functools import partial
import concurrent.futures
#函数
from PyQt6.QtNetwork import *
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *
from PyQt6.QtWidgets import QGraphicsDropShadowEffect
# from PyQt6.QtWebEngineCore import QWebEnginePage
import urllib3
urllib3.disable_warnings()

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.ui: Ui_MainWindow = Ui_MainWindow()  # 添加类型注释
        self.ui.setupUi(self)

        # init_hot_widget_status(self)
        # init_news_widget_status(self)
        # init_top_table_widget_status(self)
        # init_dtjj_widget_status(self)
        # init_chatai_widget_status(self)

        #定投基金
        # self.dt_jj_index=0
        # self.top_dtph_widget.hide()

        #开放基金
        # self.index_kf_status=False
        # self.kf_cal_status=False
        # self.kf_sort = "desc"
        # self.kf_jj_index=0#开放基金top索引
        # self.kf_jj_sub_index=0#开放基金对应子索引
        # self.calendarWidget.clicked.connect(self.on_calendar_clicked)
        # self.kfjj_widget.hide()
        # self.kf_qdii_tab_widget.hide()
        # self.kf_zs_tab_widget.hide()
        # self.kf_zq_tab_widget.hide()

        #资讯相关
        # self.info_detail_status=False
        # self.info_detail_status_2=False
        # self.info_detail_status_3=False

        # self.info_history_status=False
        # self.info_collect_status=False
        # self.info_search_status=False
        # self.hot_start = 0
        # self.search_widget_status=False
        # self.infomation_widget.hide()
        # # 滚动区域的内容 Widget
        # self.load_new_n = 0
        # self.news_list = []
        # self.info_index_start=0
        # # 初始化动画
        # self.info_up_img.mousePressEvent=self.smooth_scroll_to_top
        # self.info_up_lb.mousePressEvent=self.smooth_scroll_to_top
        # self.animation = QPropertyAnimation(self.scrollArea_3.verticalScrollBar(), b"value")
        # self.animation.setEasingCurve(QEasingCurve.Type.OutCubic)  # 设置缓动曲线
        # self.animation.setDuration(500)  # 设置动画时长（毫秒）
        # self.animation1 = QPropertyAnimation(self.scrollArea_4.verticalScrollBar(), b"value")
        # self.animation1.setEasingCurve(QEasingCurve.Type.OutCubic)  # 设置缓动曲线
        # self.animation1.setDuration(500)  # 设置动画时长（毫秒）
        #
        # #文章详情
        # self.info_detail_widget.hide()
        # # self.info_detail_return_lb.mousePressEvent=
        # self.info_detail_return_lb.mousePressEvent=partial(self.toggle_info_detail,index=-1)
        # # self.info_collect_lb.setPixmap(QPixmap(self.img_path + r'\sc_no.png').scaled(30, 30))
        #
        # self.info_collect_lb_2.setPixmap(QPixmap(self.img_path + r'\share_n.png').scaled(30, 30))
        # self.info_collect_lb_3.setPixmap(QPixmap(self.img_path + r'\web1.png').scaled(30, 30))
        # self.info_other_collect_lb_1.setPixmap(QPixmap(self.img_path + r'\web1.png').scaled(30, 30))
        # self.info_other_collect_lb_2.setPixmap(QPixmap(self.img_path + r'\share_n.png').scaled(30, 30))
        # self.info_search_collect_lb_1.setPixmap(QPixmap(self.img_path + r'\web1.png').scaled(30, 30))
        # self.info_search_collect_lb_2.setPixmap(QPixmap(self.img_path + r'\share_n.png').scaled(30, 30))
        # self.info_history_widget.hide()
        # self.info_collect_widget.hide()
        # self.info_search_widget.hide()
        # self.info_viewed_list.mousePressEvent=self.toggle_info_history_list
        # self.info_collect_list.mousePressEvent=self.toggle_info_collect_list
        # self.update_hot.mousePressEvent=self.toggle_hot_start
        # self.info_search_textEdit.mousePressEvent = self.info_mouse_press_event
        # self.info_search_btn.mousePressEvent=self.search_info
        # self.info_search_btn_2.mousePressEvent=self.search_info_2
        # # self.infomation_widget.mousePressEvent=self.hide_search_widget
        # # QApplication.instance().installEventFilter(self)
        # self.label_99.setPixmap(QPixmap(self.img_path + r'\fire.png').scaled(25, 25))
        # self.label_100.setPixmap(QPixmap(self.img_path + r'\zs.png').scaled(25, 25))
        # self.info_search_img_1.setPixmap(QPixmap(self.img_path + r'\link.png').scaled(25, 25))
        # self.info_search_img_2.setPixmap(QPixmap(self.img_path + r'\sq.png').scaled(30, 30))
        # self.label_136.setPixmap(QPixmap(self.img_path + r'\history-info.png').scaled(25, 25))
        # self.label_136.setPixmap(QPixmap(self.img_path + r'\history-info.png').scaled(25, 25))
        # self.label_143.setPixmap(QPixmap(self.img_path + r'\delete_info.png').scaled(25, 25))
        # self.label_143.mousePressEvent =self.clear_history
        # self.info_search_widget.setGraphicsEffect(self.createShadowEffect())
        # self.collect_img = QPixmap(self.img_path + r'\sc.png').scaled(30, 30)
        # self.no_collect_img = QPixmap(self.img_path + r'\sc_no.png').scaled(30, 30)
        # self.info_detail_return_lb_2.mousePressEvent =self.hide_info_detail
        # self.info_detail_return_lb_3.mousePressEvent =self.hide_info_detail_his#从历史记录返回
        # # self.top_transaction.mousePressEvent =self.show_info_other
        # self.info_other_widget.hide()
        # self.info_other_add_page.mousePressEvent=self.add_page_info_other
        # self.info_other_sub_page.mousePressEvent=self.sub_page_info_other
        # self.info_other_first_page.mousePressEvent=self.first_page_info_other
        # self.info_other_tail_page.mousePressEvent=self.tail_page_info_other
        # self.info_current_page = 1
        # # self.info_other_widget_index=0
        # self.jj_info_lb.mousePressEvent=self.open_infomation_widget
        # self.jjgd_lb.mousePressEvent=partial(self.change_info_other_index,index=0)
        # self.jjxx_lb.mousePressEvent=partial(self.change_info_other_index,index=1)
        # self.jjyw_lb.mousePressEvent=partial(self.change_info_other_index,index=2)
        # self.tzcl_lb.mousePressEvent=partial(self.change_info_other_index,index=3)
        #
        # self.info_search_textEdit.textChanged.connect(self.get_search_c_result)
        # self.info_search_img_2.mousePressEvent=self.close_search_widget
        #
        # self.info_search_return_lb.mousePressEvent=self.return_info_

        #顶部widget切换
        self.top_widget_index=0#顶部widget切换索引
        # self.show_hide_top_widget(self.top_widget_index)
        self.ui.left_widget_change.mousePressEvent=self.change_left_widget
        self.ui.right_widget_change.mousePressEvent=self.change_right_widget

        #顶部样式
        # for i in range(1,5):getattr(self.ui,f"top_img_{i}").setPixmap(QPixmap(self.img_path+fr'\top{i}.png'))# top_bar 左边图片+文字
        # self.top_m_png.setPixmap(QPixmap(self.img_path + r'\line_plot_transparent.png').scaled(300, 100))#top_moneny 收益图片




        # self.zx_widget.hide()  # 资讯页面隐藏
        # self.chatai_widget.hide()  #顶部chatai隐藏

        # self.total_tb_widget.hide()#top表格隐藏
        # # self.chat_widget.hide()#chat界面隐藏

        #TODO top_table

        # for name in ["zs", "qz", "qq","bk","ph","xg","cyb","kcb","jj","gg","hs","mg","qh","hj","wh"]:  # 添加你需要的所有名称
        #     label = getattr(self, f"index_top_{name}")
        #     label.mousePressEvent = partial(self.handle_mouse_pressself, name=name)
        # self.top_table_return.mousePressEvent=self.toggle_table_index_widget
        # self.before_page_lb.mousePressEvent=self.sub_page
        # self.next_page_lb.mousePressEvent=self.add_page
        # self.tail_page_lb.mousePressEvent=self.tail_page
        # self.first_page_lb.mousePressEvent=self.first_page
        # self.goto_page_lb.mousePressEvent=self.goto_page

        #TODO top_dtph_widget

        # self.dt_web_return.mousePressEvent = self.toggle_dt_index_widget
        # self.before_page_lb_dt.mousePressEvent = self.sub_page_dt
        # self.next_page_lb_dt.mousePressEvent = self.add_page_dt
        # self.tail_page_lb_dt.mousePressEvent = self.tail_page_dt
        # self.first_page_lb_dt.mousePressEvent = self.first_page_dt
        # self.goto_page_lb_dt.mousePressEvent = self.goto_page_dt
        # self.top_img_2.mousePressEvent = self.toggle_dt_index_widget

        #TODO kfjj_widget

        # self.ui.calendarWidget.hide()
        # self.start_date_c=None
        # self.end_date_c=None
        # self.top_img_1.mousePressEvent = self.toggle_kf_index_widget
        # self.kfjj_retuen_lb.mousePressEvent = self.toggle_kf_index_widget
        # for i in range(1,8):
        #     getattr(self, f"kf_top_lb_{i}").clicked.connect(partial(self.load_kfjj_widget, i - 1))
        # self.before_page_lb_kf.mousePressEvent = self.sub_page_kf
        # self.next_page_lb_kf.mousePressEvent = self.add_page_kf
        # self.tail_page_lb_kf.mousePressEvent = self.tail_page_kf
        # self.first_page_lb_kf.mousePressEvent = self.first_page_kf
        # self.goto_page_lb_kf.mousePressEvent = self.goto_page_kf
        # self.pushButton.clicked.connect(self.kf_query_cal)
        # self.kf_cal_img.setPixmap(QPixmap(self.img_path + r'\cal1.png').scaled(38, 42))
        # self.kf_cal_img.mousePressEvent=self.toggle_cal_widget

        # 首页
        # self.scrollArea.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)#index_scroll，
        # self.scrollArea_2.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)#chat_scroll，

        #基金比较
        # self.fund_compare=FundCompare(self.ui)
        #基金自选
        # self.fund_selection=FundSelection(self.ui)
        #基金超市
        # self.fund_filter=FundFilter(self.ui)
        #新发基金
        # self.fund_new_found=FundNewFound(self.ui)
        #主题基金
        # self.fund_subject=FundSubject(self.ui)
        #基金公司
        # self.fund_company=FundCompany_(self.ui)

        #首页-历史记录-搜索-7*24消息-top_1
        self.fund_home_1=FundHome1(self.ui)
        #首页-图表-板块top_2
        self.fund_home_2=FundHome2(self.ui)
        #首页-推荐基金-主题
        self.fund_home_3=FundHome3(self.ui)

        # 改写完成

        #货币基金
        self.fund_hb=FundHb(self.ui)
        #计算器
        self.fund_calculator=FundCalculator(self.ui)
        #资讯

        self.fund_information=FundInformation(self.ui)

    # """资讯搜索"""
    # def return_info_(self,event):
    #     if self.info_search_status==True:
    #         for i in range(1, 11):
    #             getattr(self, f"info_search_content_{i}").setHtml("")
    #             getattr(self, f"info_search_title_{i}").setHtml("")
    #             getattr(self, f"info_search_url_{i}").setText("")
    #         self.info_search_detail_tip_lb.setText("")
    #         self.info_search_detail_title_lb.setText("")
    #         self.info_detail_webBrowser_2.setHtml("")
    #         self.info_search_detail_widget.hide()
    #         self.info_search_status =False
    #         self.load_info_search_listWidget()
    #
    # def close_search_widget(self,event):
    #     self.info_search_widget.hide()
    #
    # def get_search_c_result(self):
    #     try:
    #         if self.info_search_textEdit.text()=="":
    #             self.info_search_c_widget.hide()
    #         else:
    #             self.info_search_c_widget.show()
    #             self.info_search_listWidget.clear()
    #             keyword = self.info_search_textEdit.text()
    #             # print(keyword)
    #             self.stock_list,self.stock_name=get_search_c.return_code_l(keyword=keyword)
    #             for i in self.stock_list:
    #                 self.info_search_listWidget.addItem(i)
    #             self.show_all_search.setText(f"查看全部 {keyword} 的搜索结果")
    #     except Exception as e:
    #         print(e)
    #
    # def clear_history(self, event):
    #     self.info_se_listWidget.clear()
    #     with open(r"api/widget/information\search_history", "w",
    #               encoding="utf8") as f:
    #         f.write("")
    #
    # def search_info(self, event):
    #     if self.info_search_textEdit != "":
    #         self.search_scroll_count = 0
    #         self.info_search_keyword = self.info_search_textEdit.text()
    #         self.info_search_textEdit_2.setText(self.info_search_keyword)
    #         self.info_search_page = 1
    #         self.scrollArea_4.verticalScrollBar().valueChanged.connect(self.on_search_scroll)
    #         self.info_search_status = True
    #         try:
    #             self.update_info_search_data(self.info_search_keyword)  # 记录搜索记录
    #             self.load_info_search_title(self.info_search_keyword, self.info_search_page)
    #         except Exception as e:
    #             print(e)
    #     else:
    #         QMessageBox.information(self, "搜索失败", "搜索内容不能为空！")
    #
    # def search_info_keyword(self, event, keyword):
    #     self.search_scroll_count = 0
    #     self.info_search_keyword = keyword
    #     self.info_search_textEdit_2.setText(keyword)
    #     self.info_search_page = 1
    #     self.scrollArea_4.verticalScrollBar().valueChanged.connect(self.on_search_scroll)
    #     self.info_search_status = True
    #     try:
    #         self.update_info_search_data(self.info_search_keyword)  # 记录搜索记录
    #         self.load_info_search_title(self.info_search_keyword, self.info_search_page)
    #     except Exception as e:
    #         print(e)
    #
    # def search_info_2(self, event):
    #     if self.info_search_textEdit != "":
    #         self.search_scroll_count = 0
    #         self.info_search_keyword = self.info_search_textEdit_2.text()
    #         self.info_search_page = 1
    #         self.scrollArea_4.verticalScrollBar().valueChanged.connect(self.on_search_scroll)
    #         self.info_search_status = True
    #         try:
    #             self.update_info_search_data(self.info_search_keyword)  # 记录搜索记录
    #             self.load_info_search_title(self.info_search_keyword, self.info_search_page)
    #         except Exception as e:
    #             print(e)
    #     else:
    #         QMessageBox.information(self, "搜索失败", "搜索内容不能为空！")
    # def load_info_search_title(self, keyword, page):
    #     self.info_s_time = time.time()
    #     self.info_search_detail_widget.show()
    #     self.info_search_load_lb.setText("搜索中...")
    #     self.worker_thread_info_search = get_search_title(keyword=keyword, page=page)
    #     self.worker_thread_info_search.finished.connect(self.task_finished_info_search)
    #     self.worker_thread_info_search.start()
    #
    # def task_finished_info_search(self, search_data_list):
    #     self.info_search_date = search_data_list[0]
    #     self.info_search_url = search_data_list[1]
    #     self.info_search_title = search_data_list[2]
    #     self.info_search_title_or = search_data_list[-1]
    #     self.info_search_content = search_data_list[3]
    #     self.info_search_source = search_data_list[4]
    #
    #     self.info_e_time = time.time()
    #     # 遍历显示
    #     self.load_search_()
    #     self.load_info_search_detail(self.info_search_title_or[0], self.info_search_url[0])
    #     for i in range(1, 11):
    #         getattr(self, f"info_search_url_{i}").mousePressEvent = partial(self.on_click_search_index, index=i - 1)
    #     # 更新列表对应点击，收藏列表，浏览历史
    #     # self.update_info_other_list()
    #     self.info_search_load_lb.setText("本次搜索共耗时:{:.2f}s".format(self.info_e_time - self.info_s_time))
    #
    # def on_click_search_index(self, event, index):
    #     url = self.info_search_url
    #     title = self.info_search_title_or
    #     self.load_info_search_detail(title[index], url[index])
    #     self.info_search_collect_lb_1.mousePressEvent = partial(self.open_url, url=url[index])
    #     self.info_search_collect_lb_2.mousePressEvent = partial(self.copy_to_clipboard, url=url[index])
    #
    # def load_info_search_detail(self, title, url):
    #     a = []
    #     a.append(url)
    #     if "fund" in url:
    #         article_info = asyncio.run(url_infos_1(a).main())[0]
    #     else:
    #         article_info = asyncio.run(url_infos(a).main())[0]
    #     article_tip = "发布时间： " + article_info[0] + "  来源： " + article_info[1]
    #     article_content = article_info[2]
    #     self.info_search_detail_tip_lb.setText(article_tip)
    #     self.info_search_detail_title_lb.setText(title)
    #     self.info_detail_webBrowser_2.setHtml(article_content)
    #     self.check_collect(title=title, url=url)
    #     self.update_view_history(title=title, url=url)
    #
    # def load_search_(self):
    #     for i in range(1, 11):
    #         getattr(self, f"info_search_content_{i}").setHtml(self.info_search_content[i - 1])
    #         getattr(self, f"info_search_title_{i}").setHtml(self.info_search_title[i - 1])
    #         getattr(self, f"info_search_url_{i}").setText(self.info_search_url[i - 1])
    #
    # def on_search_scroll(self):
    #     scroll_bar = self.scrollArea_4.verticalScrollBar()
    #     if self.search_scroll_count == 50:
    #         self.info_search_load_lb.setText(f"已加载{self.info_search_keyword}全部搜索结果")
    #     if scroll_bar.value() == scroll_bar.maximum() and self.search_scroll_count < 49:  # 滚动到底部
    #         self.info_search_page += 1
    #         self.search_scroll_count += 1
    #         self.load_info_search_title(self.info_search_keyword, self.info_search_page)
    #         self.smooth_scroll_to_top(event=None)
    #
    # def toggle_hot_start(self, event):
    #     if self.hot_start == 0:
    #         self.hot_start += 10
    #         self.load_search_data()
    #     else:
    #         self.hot_start = 0
    #         self.load_search_data()
    #
    # def load_search_data(self):
    #     try:
    #         self.hot_listWidget_1.clear()
    #         self.hot_listWidget_2.clear()
    #         self.stock_listWidget_1.clear()
    #         self.stock_listWidget_2.clear()
    #         self.hot_l,self.hot_or, self.stock_l ,self.stock_or= search_data.return_search_init()
    #         for i in range(self.hot_start, self.hot_start + 10):
    #             if i < 5 or 9 < i < 15:
    #                 self.hot_listWidget_1.addItem(self.hot_l[i])
    #             else:
    #                 self.hot_listWidget_2.addItem(self.hot_l[i])
    #         for j in range(self.hot_start, self.hot_start + 10):
    #             if j < 5 or 9 < j < 15:
    #                 self.stock_listWidget_1.addItem(self.stock_l[j])
    #             else:
    #                 self.stock_listWidget_2.addItem(self.stock_l[j])
    #         self.hot_listWidget_1.itemClicked.connect(self.bind_info_hot_search)
    #         self.hot_listWidget_2.itemClicked.connect(self.bind_info_hot_search)
    #         self.stock_listWidget_1.itemClicked.connect(self.bind_info_hot_stock_search)
    #         self.stock_listWidget_2.itemClicked.connect(self.bind_info_hot_stock_search)
    #     except Exception as e:
    #         print(e)
    #
    # def show_search_widget(self):
    #     try:
    #         self.info_search_widget.show()
    #         if len(self.info_search_textEdit.text()) == 0:
    #             self.info_search_c_widget.hide()
    #         self.load_search_data()
    #         self.load_info_search_listWidget()
    #         self.search_widget_status = True
    #         self.info_search_listWidget.itemClicked.connect(self.bind_info_search_stock)
    #     except Exception as e:
    #         print(e)
    #
    # def load_info_search_listWidget(self):
    #     self.info_se_listWidget.clear()
    #     with open(r"api/widget/information/search_history", "r", encoding="utf8") as f:
    #         self.info_search_history = f.readlines()
    #     for i in self.info_search_history[::-1]:
    #         max_l = 10 if len(i) > 10 else len(i)
    #         if max_l < 10:
    #             self.info_se_listWidget.addItem(i[:max_l].strip())
    #         else:
    #             self.info_se_listWidget.addItem(i[:max_l].strip() + "...")
    #     self.info_se_listWidget.itemClicked.connect(self.bind_info_search_data)
    #
    # def bind_info_search_data(self, item):
    #     try:
    #
    #         self.info_search_history_list = []
    #         with open(r"api/widget/information/search_history", "r", encoding="utf8") as f:
    #             self.info_search_history = f.readlines()
    #         for i in self.info_search_history:
    #             max_l = 10 if len(i) > 10 else len(i)
    #             if max_l < 10:
    #                 self.info_search_history_list.append(i[:max_l].strip())
    #             else:
    #                 self.info_search_history_list.append(i[:max_l].strip() + "...")
    #         self.info_search_item_index = self.info_search_history_list.index(item.text())
    #         print(self.info_search_item_index)
    #         # self.info_search_textEdit.setText(self.info_search_history[ self.info_search_item_index].strip())
    #         self.info_search_textEdit_2.setText(self.info_search_history[self.info_search_item_index].strip())
    #         self.search_info_keyword(event=None, keyword=self.info_search_history[self.info_search_item_index].strip())
    #     except Exception as e:
    #         print(e)
    #
    # def bind_info_search_stock(self, item):
    #     try:
    #         self.info_search_stock_list = []
    #         print(self.stock_name)
    #         for i in self.stock_list:
    #             self.info_search_stock_list.append(i)
    #         print(self.info_search_stock_list)
    #         self.info_stock_item_index = self.info_search_stock_list.index(item.text())
    #         # self.info_search_textEdit.setText(self.stock_name[ self.info_stock_item_index])
    #         self.info_search_textEdit_2.setText(self.stock_name[self.info_stock_item_index])
    #         self.search_info_keyword(event=None, keyword=self.stock_name[self.info_stock_item_index])
    #     except Exception as e:
    #         print(e)
    #
    # def bind_info_hot_search(self, item):
    #     try:
    #         self.info_hot_item_index = self.hot_l.index(item.text())
    #         self.info_search_textEdit_2.setText(self.hot_l[self.info_hot_item_index])
    #         self.search_info_keyword(event=None, keyword=self.hot_or[self.info_hot_item_index])
    #     except Exception as e:
    #         print(e)
    #
    # def bind_info_hot_stock_search(self, item):
    #     try:
    #         self.info_hot_stock_item_index = self.stock_l.index(item.text())
    #         self.info_search_textEdit_2.setText(self.stock_l[self.info_hot_stock_item_index])
    #         self.search_info_keyword(event=None, keyword=self.stock_or[self.info_hot_stock_item_index])
    #     except Exception as e:
    #         print(e)
    #
    # def update_info_search_data(self, query):
    #     with open(r"api/widget/information/search_history", "r", encoding="utf8") as f:
    #         view_history = f.readlines()
    #         f.close()
    #     data = query + "\n"
    #     if len(view_history) > 10:
    #         view_history.pop(0)
    #     if data not in set(view_history):
    #         view_history.append(data)
    #     else:
    #         view_history.remove(data)
    #         view_history.append(data)
    #     with open(r"api/widget/information/search_history", "w", encoding="utf8") as f1:
    #         f1.writelines(view_history)
    #         f1.close()
    #
    # """资讯其他文章+页面"""
    #
    # def change_info_other_index(self,event,index):
    #     self.info_other_widget_index =index
    #     self.show_info_other()
    #
    # def isdisable_info_page(self, page_name, status):
    #     if status:
    #         getattr(self, f"info_other_{page_name}_page").setStyleSheet("""
    #                          QLabel {
    #                         border: 1px solid gray; /* 边框宽度和颜色 */
    #                         border-radius: 10px;   /* 边框圆角 */
    #                         padding: 10px;        /* 内边距 */
    #                       color:   gray
    #                     }
    #                 """)
    #     else:
    #         getattr(self, f"info_other_{page_name}_page").setStyleSheet("""
    #                                       QLabel {
    #                     border: 1px solid #5E548E; /* 边框宽度和颜色 */
    #                     border-radius: 10px;   /* 边框圆角 */
    #                     padding: 10px;        /* 内边距 */
    #     			    color:   #5E548E}
    #                   QLabel:hover {
    #                  color: white;
    #                 background-color:#5E548E}
    #                                 }
    #                             """)
    #
    # def load_info_title(self,type,page):
    #     self.s = time.time()
    #     self.info_other_load_lb.setText("加载中...")
    #     # 创建并启动子线程
    #     self.worker_thread_info_other = get_other_title(type=type, url_page=page)
    #     if type=="tzcl":
    #         self.worker_thread_info_other.finished.connect(self.task_finished_info_other_tzcl)
    #     else:
    #         self.worker_thread_info_other.finished.connect(self.task_finished_info_other)
    #     self.worker_thread_info_other.start()
    #
    # def task_finished_info_other_tzcl(self,title_data_l):
    #     try:
    #         #传入1个页面的40条数据
    #         self.info_other_time = title_data_l[0]
    #         self.info_other_href = title_data_l[1]
    #         self.info_other_title = title_data_l[2]
    #         self.info_other_max_page = title_data_l[3]
    #         self.e = time.time()
    #
    #         self.info_other_tail_page.setText(str(self.info_other_max_page))
    #         self.load_info_other_detail(self.info_other_title[0],self.info_other_href[0])
    #         self.info_detail_listWidget_2.itemClicked.connect(self.load_info_other_list_detail)
    #         self.update_info_other_list()
    #         self.update_page_info_other()
    #         self.info_other_load_lb.setText("本次加载共耗时:{:.2f}s".format(self.e - self.s))
    #         #加载第一篇文章
    #     except Exception as e:
    #         print(e)
    #
    # def task_finished_info_other(self, title_data_l):
    #     try:
    #         #传入1个页面的40条数据
    #         self.info_other_time = title_data_l[0]
    #         self.info_other_href = title_data_l[1]
    #         self.info_other_title = title_data_l[2]
    #         self.info_other_max_page = title_data_l[3]
    #         self.e = time.time()
    #         self.info_other_tail_page.setText(str(self.info_other_max_page))
    #         self.load_info_other_detail(self.info_other_title[0],self.info_other_href[0])
    #         self.info_detail_listWidget_2.itemClicked.connect(self.load_info_other_list_detail)
    #         self.update_info_other_list()
    #         self.update_page_info_other()
    #         self.info_other_load_lb.setText("本次加载共耗时:{:.2f}s".format(self.e - self.s))
    #         #加载第一篇文章
    #     except Exception as e:
    #         print(e)
    #
    # def sub_page_info_other(self,event):
    #     if self.info_current_page >1:
    #         self.info_current_page -= 1
    #         self.load_info_title(self.info_other_list[self.info_other_widget_index], self.info_current_page)
    #         self.update_page_info_other()
    #
    # def add_page_info_other(self,event):
    #     if self.info_current_page<self.info_other_max_page:
    #         self.info_current_page+=1
    #         self.load_info_title(self.info_other_list[self.info_other_widget_index],self.info_current_page)
    #         self.update_page_info_other()
    #
    # def first_page_info_other(self,event):
    #     self.info_current_page=1
    #     self.load_info_title(self.info_other_list[self.info_other_widget_index], self.info_current_page)
    #     self.update_page_info_other()
    #
    # def tail_page_info_other(self,event):
    #     self.info_current_page =self.info_other_max_page
    #     self.load_info_title(self.info_other_list[self.info_other_widget_index], self.info_current_page)
    #     self.update_page_info_other()
    # def update_page_info_other(self,):
    #     if self.info_current_page==1 and self.info_other_max_page==1:
    #         self.isdisable_info_page("sub",True)
    #         self.isdisable_info_page("add",True)
    #     elif self.info_current_page==1 and self.info_current_page<self.info_other_max_page:
    #         self.isdisable_info_page("sub", True)
    #         self.isdisable_info_page("add", False)
    #     elif 1<self.info_current_page<self.info_other_max_page:
    #         self.isdisable_info_page("sub", False)
    #         self.isdisable_info_page("add", False)
    #     elif self.info_current_page==self.info_other_max_page:
    #         self.isdisable_info_page("sub", False)
    #         self.isdisable_info_page("add", True)
    #     self.info_other_c_page.setText("第{}页".format(str( self.info_current_page)))
    #
    # def load_info_other_list_detail(self,item):
    #     #加载指定的数据
    #     try:
    #         self.info_other_title_list_1=[]
    #         for i in self.info_other_title_list:
    #             max_l = 27 if len(i) > 27 else len(i)
    #             if max_l < 27:
    #                 self.info_other_title_list_1.append(i[:max_l].strip())
    #             else:
    #                 self.info_other_title_list_1.append(i[:max_l].strip() + "...")
    #         self.info_other_item_index=self.info_other_title_list_1.index(item.text())
    #         article_title=self.info_other_title_list[ self.info_other_item_index]
    #         article_url=self.info_other_href_list[self.info_other_item_index]
    #         self.load_info_other_detail(article_title,article_url)
    #         self.info_other_collect_lb_1.mousePressEvent = partial(self.open_url, url=article_url)
    #         self.info_other_collect_lb_2.mousePressEvent = partial(self.copy_to_clipboard, url=article_url)
    #         self.update_detail_collect_list()
    #     except Exception as e:
    #         print(e)
    #
    # def load_info_other_detail(self,title,url):
    #     a=[]
    #     a.append(url)
    #     if "fund" in url:
    #         article_info = asyncio.run(url_infos_1(a).main())[0]
    #     else:
    #         article_info = asyncio.run(url_infos(a).main())[0]
    #     article_tip = "发布时间： " + article_info[0] + "  来源： " + article_info[1]
    #     article_content = article_info[2]
    #     self.info_detail_tip_lb_2.setText(article_tip)
    #     self.info_detail_title_lb_2.setText(title)
    #     self.info_other_webView.setHtml(article_content)
    #     self.check_collect(title=title, url=url)
    #
    # def update_info_other_list(self,):
    #     self.info_detail_listWidget_2.clear()
    #     start=0
    #     end=20
    #     self.info_other_title_list = self.info_other_title[start:end]
    #     self.info_other_href_list = self.info_other_href[start:end]
    #     for i in self.info_other_title_list:
    #         max_l = 27 if len(i) > 27 else len(i)
    #         if max_l < 27:
    #             self.info_detail_listWidget_2.addItem(i[:max_l].strip())
    #         else:
    #             self.info_detail_listWidget_2.addItem(i[:max_l].strip() + "...")
    #     for i in range(0, 20):
    #         getattr(self, f"info_other_time_{i + 1}").setText(self.info_other_time[i])
    #
    # def show_info_other(self,):
    #     try:
    #         self.info_other_widget.show()
    #         self.info_detail_listWidget_2.clear()
    #         self.info_other_list=["jjgd","jjxx","jjyw","tzcl","smzx"]
    #         self.load_info_title(self.info_other_list[self.info_other_widget_index], 1)
    #         self.info_current_page=1
    #         #向listwidget添加内容
    #     except Exception as e:
    #         print(e)
    #
    #
    # def open_url(self,event,url):
    #     url = QUrl(url)  # 指定网址
    #     QDesktopServices.openUrl(url)  # 打开浏览器
    #
    # def copy_to_clipboard(self,event,url):
    #     """复制文本到剪贴板并弹出消息提示框"""
    #     clipboard = QApplication.clipboard()  # 获取剪贴板
    #     clipboard.setText(url)  # 设置剪贴板内容
    #     QMessageBox.information(self, "复制成功", "文本已复制到剪贴板！")
    #
    # def createShadowEffect(self):
    #     shadow = QGraphicsDropShadowEffect(self)
    #     shadow.setBlurRadius(15)  # 阴影模糊半径
    #     shadow.setColor(Qt.GlobalColor.lightGray)  # 阴影颜色
    #     shadow.setOffset(3, 4)  # 阴影偏移量
    #     return shadow
    #
    # def eventFilter(self, obj, event):
    #     if event.type() == QEvent.Type.MouseButtonPress:  # 使用 QEvent.Type.MouseButtonPress
    #         if not self.info_search_widget.geometry().contains(event.globalPosition().toPoint()):  # 使用 globalPosition()
    #             self.info_search_widget.hide()  # 隐藏弹出窗口
    #     return super().eventFilter(obj, event)
    #
    # def on_information_item_clicked(self,item):
    #     self.info_item_index=self.info_item_list_name.index(item.text())
    #
    #
    # def toggle_collect_lb(self,current_url_collect_status):
    #     if current_url_collect_status:
    #         self.info_collect_lb.setPixmap(self.collect_img)
    #         self.info_other_collect_lb_3.setPixmap(self.collect_img)
    #         self.info_search_collect_lb_3.setPixmap(self.collect_img)
    #     else:
    #         self.info_collect_lb.setPixmap(self.no_collect_img)
    #         self.info_other_collect_lb_3.setPixmap(self.no_collect_img)
    #         self.info_search_collect_lb_3.setPixmap(self.no_collect_img)
    #
    # def update_collect(self,event,title,url):
    #     try:
    #         data = title + "|" + url + "\n"
    #         with open(r"api/widget/information\collect", "r", encoding="utf8") as f:
    #             collect_ = f.readlines()
    #         if self.current_url_collect_status:#已收藏
    #             collect_url_index=collect_.index(data)
    #             collect_.pop(collect_url_index)
    #             with open(r"api/widget/information\collect", "w", encoding="utf8") as f1:
    #                 f1.writelines(collect_)
    #             self.load_collect()
    #             self.toggle_collect_lb(False)
    #             self.current_url_collect_status = False
    #         else:
    #             with open(r"api/widget/information\collect", "a+", encoding="utf8") as f2:
    #                 if data not in set(collect_):
    #                     f2.write(data)
    #                     self.toggle_collect_lb(True)
    #                     self.current_url_collect_status=True
    #             self.load_collect()
    #         # self.update_collect_list()
    #         # self.update_detail_collect_list()
    #     except Exception as e:
    #         print(e)
    #
    #     # if data not in set(collect_):
    #     #     self.current_url_collect_status = False
    #     #     self.info_collect_lb.setPixmap(self.no_collect_img)
    #     # else:
    #     #     self.current_url_collect_status = True
    #     #     self.info_collect_lb.setPixmap(self.collect_img)
    #
    #
    # def check_collect(self,title,url):
    #     with open(r"api/widget/information\collect", "r", encoding="utf8") as f:
    #         collect_ = f.readlines()
    #     # with open(r"api/widget/information\collect", "a+", encoding="utf8") as f:
    #         data = title + "|" + url + "\n"
    #     if data not in set(collect_):
    #         self.current_url_collect_status = False
    #         self.info_collect_lb.setPixmap(self.no_collect_img)
    #         self.info_other_collect_lb_3.setPixmap(self.no_collect_img)
    #         self.info_search_collect_lb_3.setPixmap(self.no_collect_img)
    #     else:
    #         self.current_url_collect_status = True
    #         self.info_collect_lb.setPixmap(self.collect_img)
    #         self.info_other_collect_lb_3.setPixmap(self.collect_img)
    #         self.info_search_collect_lb_3.setPixmap(self.collect_img)
    #     self.info_collect_lb.mousePressEvent = partial(self.update_collect, title=title,
    #                                                    url=url)
    #     self.info_other_collect_lb_3.mousePressEvent = partial(self.update_collect, title=title,
    #                                                    url=url)
    #     self.info_search_collect_lb_3.mousePressEvent = partial(self.update_collect, title=title,
    #                                                            url=url)
    #
    # def load_collect(self):
    #     self.info_collect_widget.clear()
    #     with open(r"api/widget/information/collect", "r", encoding="utf8") as f:
    #         self.collect_list = f.readlines()
    #     for i in self.collect_list [::-1]:
    #         self.info_collect_widget.addItem(i.split("|")[0])
    #     self.update_collect_list()
    # # def check_collect
    #
    #
    # def smooth_scroll_to_top(self,event):
    #     current_value = self.scrollArea_3.verticalScrollBar().value()
    #     current_value1 = self.scrollArea_4.verticalScrollBar().value()
    #     self.animation.setStartValue(current_value)
    #     self.animation1.setStartValue(current_value1)
    #     self.animation.setEndValue(0)
    #     self.animation1.setEndValue(0)
    #     self.animation.start()
    #     self.animation1.start()
    #
    # def open_infomation_widget(self,event):
    #     self.infomation_widget.show()
    #     self.info_search_detail_widget.hide()
    #     self.info_load_lb.setText("加载中")
    #     # 监听滚动条事件
    #     self.scrollArea_3.verticalScrollBar().valueChanged.connect(self.on_scroll)
    #     self.info_up_img.setPixmap(QPixmap(self.img_path + r'\return_up.png').scaled(32, 32))
    #     self.info_collect_item_list_name=[]
    #     self.load_collect()
    #     self.load_info_data()#开启线程
    #     self.update_collect_list()
    #     self.update_history_list()#更新历史记录列表供使用
    #     self.info_collect_widget.itemClicked.connect(self.toggle_info_collect_list_detail)
    #     self.info_history_widget.itemClicked.connect(self.toggle_info_history_list_detail)
    #
    #
    # def load_info_data(self):
    #     self.worker_thread_info = get_title()
    #     self.worker_thread_info.finished.connect(self.task_finished_info)
    #     self.worker_thread_info.start()
    #
    # def show_detail_widget(self,title,tip,content):
    #     self.info_detail_widget.show()
    #     self.info_detail_webBrowser.setHtml(content)
    #     self.info_detail_title_lb.setText(title)
    #     self.info_detail_tip_lb.setText(tip)
    #
    # def load_news(self, count):
    #     try:
    #         """加载指定数量的新闻"""
    #         # print(self.info_url[self.info_index_start:self.info_index_start+count])
    #         self.info= asyncio.run(
    #             url_infos(self.info_url[self.info_index_start:self.info_index_start + count]).main())
    #         print(self.info)
    #         tip_init=[]
    #         article_init=[]
    #         for i in range(self.info_index_start,self.info_index_start+count):
    #             getattr(self,f"info_title_{i-10*self.load_new_n+1}").setText(self.info_title_l[i])
    #             getattr(self, f"info_img_{i -10*self.load_new_n+ 1}").setHtml(f'<img src="{self.info_img_url[i]}" width="100%" height="100%">')
    #             getattr(self,f"info_date1_{i-10*self.load_new_n+1}").setText(self.info[i-10*self.load_new_n][0])
    #             getattr(self,f"info_source_{i-10*self.load_new_n+1}").setText(self.info[i-10*self.load_new_n][1])
    #             getattr(self, f"info_title_{i - 10 * self.load_new_n + 1}").mousePressEvent = partial(
    #                 self.toggle_info_detail, index=i - 10 * self.load_new_n)
    #             tip_init.append("发布时间： "+self.info[i-10*self.load_new_n][0]+"  来源： "+self.info[i-10*self.load_new_n][1])
    #             article_init.append(self.info[i-10*self.load_new_n][2])
    #         self.title_l=self.info_title_l[self.info_index_start:self.info_index_start+count]
    #         self.tip_l=tip_init
    #         self.article_l=article_init
    #
    #         # print(self.title_l)
    #         # print(self.tip_l)
    #         # self.info_title_1.mousePressEvent=partial(self.toggle_info_detail,index=0)
    #         # getattr(self,f"info_tag_{i+1}").setText(f"<b>{random.choice(["精选","推荐","优质",])}</b>")
    #         # img_url=self.info_img_url[i]
    #         # date=self.info[i].split("|")[0]
    #         # source=self.info[i].split("|")[1]
    #         # article=self.info[i].split("|")[2]
    #         # article=article_data.return_article_data(url=self.info_url[i])
    #
    #     except Exception as e:
    #         print(e)
    # def task_finished_info(self,data_l):
    #     self.info_title_l,self.info_img_url,self.info_url=data_l
    #     self.load_news(10)
    #     self.load_new_n += 1
    #     self.info_load_lb.setText("加载完成")
    #
    #     # self.info_index_start+=10
    #
    # def on_scroll(self):
    #     """滚动条事件处理"""
    #     scroll_bar = self.scrollArea_3.verticalScrollBar()
    #     if scroll_bar.value() == scroll_bar.maximum() and self.info_index_start <=49:  # 滚动到底部
    #         self.info_index_start+=10
    #         self.load_news(10)  # 加载更多新闻
    #         self.load_new_n += 1
    #         self.smooth_scroll_to_top(event=None)
    #         self.info_load_lb.setText("刷新成功")
    #
    #     elif self.info_index_start >=49:
    #         self.info_load_lb.setText("已全部加载")

    def on_calendar_clicked(self, date: QDate):
        if self.start_date_c is None:
            self.start_date_c = date
            self.label_8.setText(f"起始日期: {self.start_date_c.toString('yyyy-MM-dd')}")
        # 如果起始日期已设置且结束日期未设置，则设置为当前点击的日期
        elif self.end_date_c is None:
            if date >= self.start_date_c:  # 验证结束日期必须大于或等于起始日期
                self.end_date_c = date
                self.label_8.setText(f"起始日期: {self.start_date_c.toString('yyyy-MM-dd')}, 结束日期: {self.end_date_c.toString('yyyy-MM-dd')}")
            else:
                QMessageBox.warning(self, "错误", "结束日期必须大于或等于起始日期！")
        # 如果起始日期和结束日期都已设置，则重置起始日期和结束日期
        else:
            self.start_date_c = date
            self.end_date_c = None
            self.label_8.setText(f"起始日期: {self.start_date_c.toString('yyyy-MM-dd')}")

    def kf_query_cal(self,event):
        if self.start_date_c is not None and self.end_date_c is not None:
            print(f"起始日期: {self.start_date_c.toString('yyyy-MM-dd')}, 结束日期: {self.end_date_c.toString('yyyy-MM-dd')}")
            self.start_date=self.start_date_c.toString('yyyy-MM-dd')
            self.end_date=self.end_date_c.toString('yyyy-MM-dd')
            self.kf_current_page=1
            self.load_kf_data_params(event=None, index=self.kf_jj_index, tab_index=self.kf_jj_sub_index,
                                     page=self.kf_current_page,
                                     sort_dep=self.sort_dep_index, sort=self.kf_sort, start_date=self.start_date,
                                     end_date=self.end_date)
        else:
            print("请选择起始日期和结束日期！")

    def on_checkbox_state_changed(self, state):
        if state == Qt.CheckState.Checked.value:
           self.kf_sort="asc"
        else:
            self.kf_sort="desc"
        self.load_kf_data_params(event=None, index=self.kf_jj_index, tab_index=self.kf_jj_sub_index,
                                 page=self.kf_current_page,
                                 sort_dep=self.sort_dep_index, sort=self.kf_sort, start_date=self.start_date,
                                 end_date=self.end_date)
    def sub_page_kf(self,event):
        if self.kf_current_page > 1:
            self.kf_current_page -= 1
            self.load_kf_data_params(event=None,index=self.kf_jj_index,tab_index=self.kf_jj_sub_index,page=self.kf_current_page,
                                                                  sort_dep=self.sort_dep_index,sort=self.kf_sort,start_date=self.start_date,end_date=self.end_date)
            self.update_kf_page()
            self.current_page_lb_dt.setText("第{}页".format(str(self.kf_current_page)))

    def add_page_kf(self,event):
        if self.kf_current_page < self.kf_total_page:
            self.kf_current_page += 1
            self.load_kf_data_params(event=None,index=self.kf_jj_index, tab_index=self.kf_jj_sub_index,
                                     page=self.kf_current_page,
                                     sort_dep=self.sort_dep_index, sort=self.kf_sort, start_date=self.start_date,
                                     end_date=self.end_date)
            self.update_kf_page()
            self.current_page_lb_kf.setText("第{}页".format(str(self.kf_current_page)))


    def tail_page_kf(self,event):
        self.kf_current_page = self.kf_total_page
        self.load_kf_data_params(event=None, index=self.kf_jj_index, tab_index=self.kf_jj_sub_index,
                                 page=self.kf_current_page,
                                 sort_dep=self.sort_dep_index, sort=self.kf_sort, start_date=self.start_date,
                                 end_date=self.end_date)
        self.update_kf_page()
        self.current_page_lb_kf.setText("第{}页".format(str(self.kf_current_page)))
    def first_page_kf(self,event):
        self.kf_current_page = 1
        self.load_kf_data_params(event=None, index=self.kf_jj_index, tab_index=self.kf_jj_sub_index,
                                 page=self.kf_current_page,
                                 sort_dep=self.sort_dep_index, sort=self.kf_sort, start_date=self.start_date,
                                 end_date=self.end_date)
        self.update_kf_page()
        self.current_page_lb_kf.setText("第{}页".format(str(self.kf_current_page)))

    def goto_page_kf(self,event):
        if int(self.goto_Edit_kf.text()) > self.kf_total_page or int(self.goto_Edit_kf.text()) < 0:
            self.goto_Edit_kf.setText("")
        else:
            self.kf_current_page = int(self.goto_Edit_kf.text())
            self.load_kf_data_params(event=None,index=self.kf_jj_index,tab_index=self.kf_jj_sub_index,page=self.kf_current_page,
                                                                  sort_dep=self.sort_dep_index,sort=self.kf_sort,start_date=self.start_date,end_date=self.end_date)
            self.update_kf_page()
            self.current_page_lb_kf.setText("第{}页".format(str(self.kf_current_page)))

    def update_kf_page(self):
        if self.kf_total_page == 1:
            self.goto_widget_3.hide()
        else:
            self.goto_widget_3.show()
        # 对按钮显示判断
        if self.kf_current_page == 1 and self.kf_total_page == 1:
            self.isdisable_kf_page("before", True)
            self.isdisable_kf_page("next", True)
        elif self.kf_current_page == 1 and self.kf_current_page < self.kf_total_page:
            self.isdisable_kf_page("before", True)
            self.isdisable_kf_page("next", False)
        elif 1 < self.kf_current_page < self.kf_total_page:
            self.isdisable_kf_page("before", False)
            self.isdisable_kf_page("next", False)
        elif self.kf_current_page == self.kf_total_page:
            self.isdisable_kf_page("before", False)
            self.isdisable_kf_page("next", True)
        self.current_page_lb_kf.setText("第{}页".format(str(self.kf_current_page)))

    def isdisable_kf_page(self, page_name, status):
        if status:
            getattr(self, f"{page_name}_page_lb_kf").setStyleSheet("""
                             QLabel {
                            border: 1px solid gray; /* 边框宽度和颜色 */
                            border-radius: 10px;   /* 边框圆角 */
                            padding: 10px;        /* 内边距 */
                          color:   gray
                        }
                    """)
        else:
            getattr(self, f"{page_name}_page_lb_kf").setStyleSheet("""
                                          QLabel {
                        border: 1px solid #295792; /* 边框宽度和颜色 */
                        border-radius: 10px;   /* 边框圆角 */
                        padding: 10px;        /* 内边距 */
        			    color:   #295792}
                      QLabel:hover {
                     color: white; 
                    background-color:#295792}
                                    }
                                """)

    @calculate_time
    def show_kfjj_widget(self):
        #default
        today = date.today()
        last_year_today = today - timedelta(days=365)
        last_year_today = last_year_today.strftime("%Y-%m-%d")
        self.kf_sort_check.stateChanged.connect(self.on_checkbox_state_changed)
        self.kf_current_page = 1
        self.sort_dep_index=11#近一年
        self.tab_index=0
        self.start_date=last_year_today
        self.end_date=today.strftime("%Y-%m-%d")

        #对第一个网页内容提取，渲染顶部按钮，以及页面
        #对所有顶部按钮多线程加载绑定数据
        self.bl_kf_pos, self.gr_kf_pos, self.top_lb, self.tb_header_lb = kfjj_data.get_kj_type_info()
        data, data_info = kfjj_data.return_kf_data_1(self.kf_jj_index)
        self.kf_total_page = data_info["allPages"]
        kf_top_info = [data_info["allRecords"], data_info["zs_count"], data_info["gp_count"], data_info["hh_count"],
                       data_info["zq_count"], data_info["qdii_count"], data_info["fof_count"]]
        for i in range(1, 8):
            getattr(self, f"kf_top_lb_{i}").setText(f"{self.top_lb[i - 1]}({str(kf_top_info[i - 1])})")
        self.kfjj_widget.show()
        self.kfjj_tableview_style(self.kf_jj_index, data,data_info)
        self.kf_loading_lb.setText("加载完成")
        self.tableView.horizontalHeader().sectionClicked.connect(self.kf_header_clicked)  # 监听表头点击事件
        self.label_8.setText(f"起始日期: {self.start_date}, 结束日期: {self.end_date}")


    def load_kfjj_widget(self,index1):
        s=time.time()
        data, data_info = kfjj_data.return_kf_data_1(index1)
        self.kfjj_tableview_style(index1,data,data_info)
        self.kf_jj_sub_index=0
        self.kf_loading_lb.setText("加载耗时:"+"{:.2f}s".format(time.time()-s))
        self.kf_current_page=1
        self.current_page_lb_kf.setText("第{}页".format(str(self.kf_current_page)))

    def kf_header_clicked(self,index):#排序依据
        self.sort_dep_index = index  # 近一年
        self.kf_current_page = 1
        self.load_kf_data_params(event=None,index=self.kf_jj_index,tab_index=self.kf_jj_sub_index,page=self.kf_current_page,
                                                                      sort_dep=index,sort=self.kf_sort,start_date=self.start_date,end_date=self.end_date)
        self.update_kf_page()


    def load_kf_data_params(self,event,index,tab_index,page,sort_dep,sort,start_date,end_date):
        self.st = time.time()
        self.kf_loading_lb.setText("加载中...")
        self.kf_jj_sub_index=tab_index
        self.kf_jj_index=index
        self.kf_current_page=page
        self.sort_dep_index=sort_dep
        self.kf_sort_check.stateChanged.connect(self.on_checkbox_state_changed)
        print(self.kf_sort)
        # self.start_date=start_date
        # self.end_date=end_date
        # 创建并启动子线程
        self.worker_thread_kf =kfjj_data_params(index=index,tab_index=tab_index, page=page,sort_dep=sort_dep,sort=sort,start_date=start_date,end_date=end_date)
        self.worker_thread_kf.finished.connect(self.task_finished_kf)
        self.worker_thread_kf.start()

    def load_kf_data_params_pre(self,event, index, tab_index, page, sort_dep, sort, start_date, end_date):
        self.kf_jj_sub_index=tab_index
        self.kf_current_page=1
        self.load_kf_data_params(event=None,index=index,tab_index=tab_index, page=page,sort_dep=sort_dep,sort=sort,start_date=start_date,end_date=end_date)

    def task_finished_kf(self, data, data_info):
        self.kf_total_page=data_info["allPages"]
        self.et = time.time()
        self.kfjj_tableview_style(self.kf_jj_index,data,data_info)
        self.kf_loading_lb.setText("本次加载共耗时:{:.2f}s".format(self.et-self.st))



    def select_kfjj_btn(self,index1):

        self.kf_jj_index=index1
        if index1==1:
            self.kf_qdii_tab_widget.hide()
            self.kf_zq_tab_widget.hide()
            self.kf_zs_tab_widget.show()

            for i in range(1, 9):
                getattr(self, f"kf_zs_tab_{i}").setStyleSheet("background-color:none")

            getattr(self, f"kf_zs_tab_{self.kf_jj_sub_index + 1}").setStyleSheet("""
                           QLabel {
                    color:white;  
                    border: 1; /* 移除边框 */  
                    background-color: qlineargradient(  
                        x1: 0, y1: 0,  
                        x2: 1, y2: 0,  
                        stop: 0 #6495ED, 
                        stop: 1 #00FA9A
                    );  
                    border-radius: 5px; /* 圆角 */  
                    padding:5px
                }  
                    """)
            self.update_kf_page()
        elif index1==4:
            self.kf_qdii_tab_widget.hide()
            self.kf_zs_tab_widget.hide()
            self.kf_zq_tab_widget.show()

            for i in range(1, 7):
                getattr(self, f"kf_zq_tab_{i}").setStyleSheet("background-color:none")
            getattr(self, f"kf_zq_tab_{self.kf_jj_sub_index + 1}").setStyleSheet("""
                           QLabel {
                    color:white;  
                    border: 1; /* 移除边框 */  
                    background-color: qlineargradient(  
                        x1: 0, y1: 0,  
                        x2: 1, y2: 0,  
                        stop: 0 #6495ED, 
                        stop: 1 #00FA9A
                    );  
                    border-radius: 5px; /* 圆角 */  
                    padding:5px
                }  
                    """)
            self.update_kf_page()
        elif index1==5:
            self.kf_zs_tab_widget.hide()
            self.kf_zq_tab_widget.hide()
            self.kf_qdii_tab_widget.show()

            for i in range(1, 9):
                getattr(self, f"kf_qdii_tab_{i}").setStyleSheet("background-color:none")
            getattr(self, f"kf_qdii_tab_{self.kf_jj_sub_index + 1}").setStyleSheet("""
                           QLabel {
                    color:white;  
                    border: 1; /* 移除边框 */  
                    background-color: qlineargradient(  
                        x1: 0, y1: 0,  
                        x2: 1, y2: 0,  
                        stop: 0 #6495ED, 
                        stop: 1 #00FA9A
                    );  
                    border-radius: 5px; /* 圆角 */  
                    padding:5px
                }  
                    """)
            self.update_kf_page()
        else:
            self.kf_zs_tab_widget.hide()
            self.kf_zq_tab_widget.hide()
            self.kf_qdii_tab_widget.hide()
        for i in range(1, 8):
            getattr(self, f"kf_top_lb_{i}").setStyleSheet("background-color:none")
        getattr(self,f"kf_top_lb_{index1+1}").setStyleSheet("""
                QPushButton {
                    color:white;  
                    border: 1; /* 移除边框 */  
                    background-color: qlineargradient(  
                        x1: 0, y1: 0,  
                        x2: 1, y2: 0,  
                        stop: 0 #FFB6C1, 
                        stop: 1 #8A2BE2 
                    );  
                    border-radius: 5px; /* 圆角 */  
                    padding:5px
                }  
        """)
        for i in range(1, 9):
            getattr(self, f"kf_zs_tab_{i}").mousePressEvent = partial(self.load_kf_data_params_pre, index=1,
                                                                      tab_index=i - 1, page=self.kf_current_page,
                                                                      sort_dep=self.sort_dep_index, sort=self.kf_sort,
                                                                      start_date=self.start_date,
                                                                      end_date=self.end_date)
        for i in range(1, 7):
            getattr(self, f"kf_zq_tab_{i}").mousePressEvent = partial(self.load_kf_data_params_pre, index=4,
                                                                      tab_index=i - 1, page=self.kf_current_page,
                                                                      sort_dep=self.sort_dep_index, sort=self.kf_sort,
                                                                      start_date=self.start_date,
                                                                      end_date=self.end_date)
        for i in range(1, 9):
            getattr(self, f"kf_qdii_tab_{i}").mousePressEvent = partial(self.load_kf_data_params_pre, index=5,
                                                                        tab_index=i - 1, page=self.kf_current_page,
                                                                        sort_dep=self.sort_dep_index, sort=self.kf_sort,
                                                                        start_date=self.start_date,
                                                                        end_date=self.end_date)
        self.update_kf_page()



    def kfjj_tableview_style(self,index,data,data_info):
        self.kf_total_page = data_info["allPages"]
        self.select_kfjj_btn(index)
        self.model = QStandardItemModel()
        self.model.setHorizontalHeaderLabels(self.tb_header_lb)
        for row in data:
            row_items = [QStandardItem(str(item)) for item in row]
            self.model.appendRow(row_items)
        self.tableView.setModel(self.model)
        # self.tableView.setStyleSheet("QTableView::item { text-align: center; }")
        # 设置自定义委托，使文本水平居中
        # delegate = CenteredItemDelegate(self.tableView)
        # self.tableView.setItemDelegate(delegate)
        self.tableView.setColumnWidth(0, 73)
        self.tableView.setColumnWidth(1, 260)
        for i in range(4, 16):
            self.tableView.setColumnWidth(i, 78)
        # 设置表头字段行的样式
        self.tableView.horizontalHeader().setStyleSheet(
            "QHeaderView::section {"
            "   background-color: #FF81ED;"  # 背景颜色
            "   color: white;"  # 字体颜色
            "   padding: 5px;"  # 内边距
            "   font-weight: bold;"  # 字体加粗
            "}"
        )
        self.tail_page_lb_kf.setText(str(self.kf_total_page))
        self.update_kf_page()
        delegate = CustomDelegate()
        self.tableView.setItemDelegate(delegate)

    def sub_page_dt(self,event):
        if self.dt_current_page>1:
            self.dt_current_page-=1
            self.load_dt_web(self.dt_jj_index,self.dt_current_page)
            self.update_dt_page()  # 更新dt页按钮
            self.current_page_lb_dt.setText("第{}页".format(str(self.dt_current_page)))

    def add_page_dt(self,event):
        if self.dt_current_page < self.dt_total_page:
            self.dt_current_page += 1
            self.load_dt_web(self.dt_jj_index, self.dt_current_page)
            self.update_dt_page()  # 更新dt页按钮
            self.current_page_lb_dt.setText("第{}页".format(str(self.dt_current_page)))
            self.kf

    def tail_page_dt(self,event):
        self.dt_current_page=self.dt_total_page
        self.load_dt_web(self.dt_jj_index, self.dt_total_page)
        self.update_dt_page()
        self.current_page_lb_dt.setText("第{}页".format(str(self.dt_current_page)))

    def first_page_dt(self,event):
        self.dt_current_page =1
        self.load_dt_web(self.dt_jj_index,1)
        self.update_dt_page()
        self.current_page_lb_dt.setText("第{}页".format(str(self.dt_current_page)))

    def goto_page_dt(self,event):
        if int(self.goto_Edit_dt.text())>self.dt_total_page or int(self.goto_Edit_dt)<0:
            self.goto_Edit_dt.setText("")
        else:
            self.dt_current_page=int(self.goto_Edit_dt.text())
            self.load_dt_web(self.dt_jj_index, self.dt_current_page)
            self.update_dt_page()
            self.current_page_lb_dt.setText("第{}页".format(str(self.dt_current_page)))

    def update_dt_page(self):
        if self.dt_total_page == 1:
            self.goto_widget_2.hide()
        else:
            self.goto_widget_2.show()
        # 对按钮显示判断
        if self.dt_current_page == 1 and self.dt_total_page == 1:
            self.isdisable_dt_page("before", True)
            self.isdisable_dt_page("next", True)
        elif self.dt_current_page == 1 and self.dt_current_page < self.dt_total_page:
            self.isdisable_dt_page("before", True)
            self.isdisable_dt_page("next", False)
        elif 1 < self.dt_current_page < self.dt_total_page:
            self.isdisable_dt_page("before", False)
            self.isdisable_dt_page("next", False)
        elif self.dt_current_page == self.dt_total_page:
            self.isdisable_dt_page("before", False)
            self.isdisable_dt_page("next", True)

    def isdisable_dt_page(self,page_name,status):
        if status:
            getattr(self, f"{page_name}_page_lb_dt").setStyleSheet("""
                             QLabel {
                            border: 1px solid gray; /* 边框宽度和颜色 */
                            border-radius: 10px;   /* 边框圆角 */
                            padding: 10px;        /* 内边距 */
                          color:   gray
                        }
                    """)
        else:
            getattr(self, f"{page_name}_page_lb_dt").setStyleSheet("""
                                          QLabel {
                        border: 1px solid #295792; /* 边框宽度和颜色 */
                        border-radius: 10px;   /* 边框圆角 */
                        padding: 10px;        /* 内边距 */
        			    color:   #295792}
                      QLabel:hover {
                     color: white; 
                    background-color:#295792}
                                    }
                                """)

    def show_dt_widget(self):
        self.dt_current_page=1
        self.dt_list_Widget.itemClicked.connect(self.on_dtitem_clicked)
        self.top_dtph_widget.show()
        self.load_dt_web(0,1)
        self.dt_list_name=[]
        for i in range(self.dt_list_Widget.count()):
            item = self.dt_list_Widget.item(i)
            self.dt_list_name.append(item.text())  # 打印列表项的文本

    def on_dtitem_clicked(self,item):
        self.dt_jj_index=self.dt_list_name.index(item.text())
        self.load_dt_web(self.dt_jj_index,1)
        self.dt_current_page=1

    def load_dt_web(self,index,page):
        self.s = time.time()
        self.dt_loading_lb.setText("加载中...")
        # 创建并启动子线程
        self.worker_thread = dt_web(index=index, page=page)
        self.worker_thread.finished.connect(self.task_finished)
        self.worker_thread.start()

    def task_finished(self, web_data, page1):
        self.dt_total_page=page1
        self.e = time.time()
        self.textBrowser.setHtml(web_data)
        self.tail_page_lb_dt.setText(str(page1))
        self.dt_loading_lb.setText("本次加载共耗时:{:.2f}s".format(self.e-self.s))

    """top_切换widget"""
    def change_left_widget(self,event):
        if self.top_widget_index==1:
            # self.left_widget_change.setStyleSheet("""color: gray; }""")
            self.top_widget_index-=1
        # elif 0<self.top_widget_index<=3:
        #     self.left_widget_change.setStyleSheet("""QLabel:hover { color: #FF4400; }""")
        #     self.right_widget_change.setStyleSheet("""QLabel:hover { color: #FF4400; }""")
            self.top_widget_index -= 1
        self.show_hide_top_widget(self.top_widget_index)

    def change_right_widget(self,event):
        print("向右切换")
        if self.top_widget_index==2:
            self.top_widget_index+=1
            # self.right_widget_change.setStyleSheet("""color: gray; }""")
        elif 0<=self.top_widget_index<3:
            # self.left_widget_change.setStyleSheet("""QLabel:hover { color: #FF4400; }""")
            # self.right_widget_change.setStyleSheet("""QLabel:hover { color: #FF4400; }""")
            self.top_widget_index += 1
        self.show_hide_top_widget(self.top_widget_index)


    #top_widget
    def show_hide_top_widget(self,index):
        widget_list = ["tz_tools_widget", "tz_compute_widget", "zx_widget", "chatai_widget"]
        for i in range(4):
            if i == index:
                getattr(self.ui, f"{widget_list[index]}").show()
            else:
                getattr(self.ui, f"{widget_list[i]}").hide()
        match (index):
            case 0:
                print("主页")
                # self.close_calculator_widget()
            case 1:
                print("计算器")
                self.fund_calculator.open_calculator_widget()
            case 2:
                print("资讯")
                # self.close_calculator_widget()
                self.fund_information.open_infomation_widget(event=None)
            case 3:
                print("ai聊天")
                # self.close_calculator_widget()
        # print(self.top_widget_index)




    def handle_mouse_pressself(self,event,name,):
        self.change_table(name, event)

    def change_table(self,table_name,event):
        self.current_page=1
        self.current_page_lb.setText("1")
        self.btn_index=0
        if event.button() == Qt.MouseButton.LeftButton:
            self.index_table_status = True
            self.total_tb_widget.show()
        headers, cols, self.btn_name,self.blue_pos,self.gr_pos = table_data.get_table_data(table_name)  # 调用获取数据api 获取page_url_l ，headers，，col
        print(headers, cols, self.btn_name,self.blue_pos,self.gr_pos)
        data_l,len_data_l,self.total_page=table_data.return_index_tb(self.btn_index,self.current_page)

        data_l=self.is_limit( data_l,len_data_l)
        print(len(data_l))
        self.table_r_c = [20, 20, 20, 20,cols]
        #样式渲染的函数
        self.btn_conn()
        self.table_btn_i(data_l, self.total_page,len_data_l, cols, self.btn_index,self.blue_pos,self.gr_pos)#让每个标题的表的不同按钮绑定正确数据
        #渲染数据
        self.first_page_lb.setStyleSheet("""
                         QLabel {
                                border: 1px solid #295792; /* 边框宽度和颜色 */
                                border-radius: 10px;   /* 边框圆角 */
                                padding: 10px;        /* 内边距 */
                			  color:  white;
                			  background-color:#295792

                            }
                        """)
        self.tail_page_lb.setStyleSheet("""
                             QLabel {
                                border: 1px solid #295792; /* 边框宽度和颜色 */
                                border-radius: 10px;   /* 边框圆角 */
                                padding: 10px;        /* 内边距 */
                			  color:   #295792

                            }
                              QLabel:hover {
                             color: white; 
                            background-color:#295792}
                        """)
        for col, header in enumerate(headers): self.top_tableWidget.setHorizontalHeaderItem(col,QTableWidgetItem(header))
        def process_button(i):
            # 调用 table_btn_i 函数
            data,len_data_rr,page=table_data.return_index_tb(i-1,1)
            getattr(self, f"tb_btn_i_{i}").clicked.connect(
                partial(self.table_btn_i, self.is_limit(data,len_data_rr),page, len_data_l, self.table_r_c[-1], i - 1,
                        self.blue_pos, self.gr_pos)
            )
            self.current_page=1

        # # 使用多线程处理按钮逻辑
        with concurrent.futures.ThreadPoolExecutor() as executor:
            futures = [executor.submit(process_button, i) for i in range(1, len(self.btn_name) + 1)]
            for future in concurrent.futures.as_completed(futures):
                try:
                    future.result()  # 获取线程执行结果
                except Exception as e:
                    print(f"Error processing button: {e}")
        print("page:" + str(self.total_page))
        self.update_page()
    def is_limit(self,data, len_data_l):
        if len_data_l > 20:
            data_l = data[:20]
        else:
            data_l = data[:len_data_l]
        return data_l

    def sub_page(self, event):
        if self.current_page >1:
            self.current_page -= 1
            self.update_page()
            data_l, len_data_l, self.total_page = table_data.return_index_tb(self.btn_index,self.current_page)
            self.table_btn_i(self.is_limit(data_l,len_data_l),self.total_page, len_data_l, self.table_r_c[-1], self.btn_index,
                            self.blue_pos, self.gr_pos)
            self.current_page_lb.setText(str(self.current_page))

    def add_page(self, event):
        if self.current_page <self.total_page:
            self.current_page += 1
            self.update_page()
            data_l, len_data_l, self.total_page = table_data.return_index_tb(self.btn_index,self.current_page)
            self.table_btn_i(self.is_limit(data_l, len_data_l), self.total_page, len_data_l, self.table_r_c[-1],
                             self.btn_index,
                             self.blue_pos, self.gr_pos)
            self.current_page_lb.setText(str(self.current_page))
    def tail_page(self,event):
        print(self.total_page)
        self.current_page=self.total_page
        self.update_page()
        data_l, len_data_l, total_page = table_data.return_index_tb(self.btn_index, self.total_page)
        self.table_btn_i(self.is_limit(data_l, len_data_l), total_page, len_data_l, self.table_r_c[-1],
                         self.btn_index,
                         self.blue_pos, self.gr_pos)
        self.current_page_lb.setText(str(self.current_page))
        self.tail_page_lb.setStyleSheet("""
         QLabel {
                border: 1px solid #295792; /* 边框宽度和颜色 */
                border-radius: 10px;   /* 边框圆角 */
                padding: 10px;        /* 内边距 */
			  color:  white;
			  background-color:#295792

            }
        """)
        self.first_page_lb.setStyleSheet("""
             QLabel {
                border: 1px solid #295792; /* 边框宽度和颜色 */
                border-radius: 10px;   /* 边框圆角 */
                padding: 10px;        /* 内边距 */
			  color:   #295792

            }
              QLabel:hover {
             color: white; 
            background-color:#295792}
        """)

    def first_page(self,event):
        self.current_page =1
        self.update_page()
        data_l, len_data_l, total_page = table_data.return_index_tb(self.btn_index, 1)
        self.table_btn_i(self.is_limit(data_l, len_data_l), total_page, len_data_l, self.table_r_c[-1],
                         self.btn_index,
                         self.blue_pos, self.gr_pos)
        self.current_page_lb.setText(str(self.current_page))
        self.first_page_lb.setStyleSheet("""
                 QLabel {
                        border: 1px solid #295792; /* 边框宽度和颜色 */
                        border-radius: 10px;   /* 边框圆角 */
                        padding: 10px;        /* 内边距 */
                        color:  white;
                        background-color:#295792
                    }
                """)
        self.tail_page_lb.setStyleSheet("""
                     QLabel {
                        border: 1px solid #295792; /* 边框宽度和颜色 */
                        border-radius: 10px;   /* 边框圆角 */
                        padding: 10px;        /* 内边距 */
        			  color:   #295792

                    }
                      QLabel:hover {
                     color: white; 
                    background-color:#295792}
                """)

    def goto_page(self,event):
        if int(self.goto_Edit.text()) >self.total_page or int(self.goto_Edit.text()) < 0:
            self.goto_Edit.setText("")
        else:
            self.current_page = int(self.goto_Edit.text())
            self.update_page()
            data_l, len_data_l, total_page = table_data.return_index_tb(self.btn_index, self.current_page)
            self.table_btn_i(self.is_limit(data_l, len_data_l), total_page, len_data_l, self.table_r_c[-1],
                             self.btn_index,
                             self.blue_pos, self.gr_pos)
            self.current_page_lb.setText(str(self.current_page))
            self.goto_Edit.setText("")

    def isdisable_page(self,page_name,status):
        if status:
            getattr(self,f"{page_name}_page_lb").setStyleSheet("""
                     QLabel {
                    border: 1px solid gray; /* 边框宽度和颜色 */
                    border-radius: 10px;   /* 边框圆角 */
                    padding: 10px;        /* 内边距 */
                  color:   gray
                }
            """)
        else:
            getattr(self, f"{page_name}_page_lb").setStyleSheet("""
                                  QLabel {
                border: 1px solid #295792; /* 边框宽度和颜色 */
                border-radius: 10px;   /* 边框圆角 */
                padding: 10px;        /* 内边距 */
			    color:   #295792}
              QLabel:hover {
             color: white; 
            background-color:#295792}
                            }
                        """)

    def update_page(self):
        print("pd")
        if self.total_page==1:
            self.goto_widget.hide()
        else:
            self.goto_widget.show()
        # 对按钮显示判断
        if self.current_page==1 and self.total_page==1:
            self.isdisable_page("before",True)
            self.isdisable_page("next",True)
        elif self.current_page==1 and self.current_page<self.total_page:
            self.isdisable_page("before", True)
            self.isdisable_page("next", False)
        elif 1<self .current_page<self.total_page:
            self.isdisable_page("before", False)
            self.isdisable_page("next", False)
        elif self.current_page==self.total_page:
            self.isdisable_page("before", False)
            self.isdisable_page("next", True)

    def table_btn_i(self,data,page,rows,cols,index,blue_cols,gr_cols):
        try:
            # self.current_page=1
            print("c_page"+str(self.current_page))
            self.total_page=page
            if index!=self.btn_index:
                self.current_page=1
                self.current_page_lb.setText("1")
            self.btn_index=index
            color=["#294EBC","#009900","#FF0018","black"]#蓝，绿，红
            #对表格按钮样式进行渲染
            print(rows,cols)
            self.top_tableWidget.setColumnCount(cols)
            self.top_tableWidget.setRowCount(rows)
            for row in range(rows):
                for col in range(cols):
                    item = QTableWidgetItem(f"{data[row][col]}")
                    item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    item.setBackground(QColor('#FFFFFF')) if row%2 else item.setBackground(QColor('#F2F2F2'))
                    for i in blue_cols:
                        if col+1==i:
                            item.setForeground(QColor(color[0]))
                    for j in gr_cols:  #传入 -12 13.45  -12.0  |  -12% -
                        if col + 1 == j and isinstance(data[row][col], float):
                             if data[row][col]<0 :
                                 item.setForeground(QColor(color[1]))
                             elif data[row][col]==0:
                                 item.setForeground(QColor(color[-1]))
                             else :item.setForeground(QColor(color[2]))
                        elif col + 1 == j and isinstance(data[row][col], int):
                            item.setForeground(QColor(color[1])) if data[row][col]<0  else item.setForeground(QColor(color[2]))
                        elif col + 1 == j and isinstance(data[row][col], str) and data[row][col]=="-":
                            item.setForeground(QColor(color[-1]))
                        elif col + 1 == j and isinstance(data[row][col], str):
                            # print(data[row][col],type(data[row][col]))
                            da = float(data[row][col].replace("%",""))
                            if da < 0 :
                                item.setForeground(QColor(color[1]))
                            elif da==0:
                                item.setForeground(QColor(color[-1]))
                            else :item.setForeground(QColor(color[2]))

                    self.top_tableWidget.setItem(row, col, item)
            for i in range(1,5):
                getattr(self, f"tb_btn_i_{i}").setStyleSheet("background-color:none")
            print("kkk")
            getattr(self, f"tb_btn_i_{index+1}").setStyleSheet("""  
                        QPushButton {
                            color:white;  
                            border: 1; /* 移除边框 */  
                            background-color: qlineargradient(  
                                x1: 0, y1: 0,  
                                x2: 1, y2: 0,  
                                stop: 0 #FFB6C1, 
                                stop: 1 #8A2BE2 
                            );  
                            border-radius: 5px; /* 圆角 */  
                            padding:5px
                        }  
                    """)
            """调整列宽，使所有列平均占满表格宽度"""
            table_width = self.top_tableWidget.width()  # 获取表格宽度
            column_count = self.top_tableWidget.columnCount()  # 获取列数
            if column_count > 0:
                column_width = table_width // column_count  # 计算每列的宽度
                for col in range(column_count):
                    self.top_tableWidget.setColumnWidth(col, column_width)
            self.top_tableWidget.horizontalHeader().setStyleSheet("""
                                    QHeaderView::section {
                                        background-color: #EBF6FE;  /* 背景颜色 */
                                        color: black;             /* 字体颜色 */
                                        padding-left: 5px;
                                        padding-right: 5px;
                                    }
                                """)
            self.top_tableWidget.setStyleSheet("""
                                    QTableWidget {
                                        gridline-color:#B9D4E7;
                                        border: 1px solid #BBD4E8;  /* 边框颜色 */
                                    }
                                """)
            self.tail_page_lb.setText(str(page))
            self.update_page()
            start=(self.current_page-1)*20+1
            end=start+rows
            self.top_tableWidget.setVerticalHeaderLabels([str(i) for i in range(start,end)])
        except Exception as e:
            print(e)

    def btn_conn(self,):
        for i in range(1,5):
            btn = getattr(self, f"tb_btn_i_{i}")
            btn.show() if i-1<len(self.btn_name) else btn.hide()
        for i in range(0,len(self.btn_name)):
            getattr(self, f"tb_btn_i_{i + 1}").setText(self.btn_name[i])



    # def info_mouse_press_event(self, event):
    #     super(QLineEdit, self.info_search_textEdit).mousePressEvent(event)  # 调用父类方法，确保正常行为
    #     self.show_search_widget()
    #
    #
    # def toggle_left_hy_arrow(self,event):
    #     if self.left_hy_status==1:#当前跌幅榜
    #         self.left_bang_index += 1
    #         self.label_107.setPixmap(QPixmap(self.img_path+'/red_up.png').scaled(15, 25,))
    #         self.left_hy_status = 0
    #     elif self.left_hy_status==0:#当前涨幅榜
    #         self.left_bang_index -= 1
    #         self.label_107.setPixmap(QPixmap(self.img_path+'/green_down.png').scaled(15, 25, ))
    #         self.left_hy_status = 1
    #     self.l_center_lb.setText(self.left_table[self.left_bang_index])
    def toggle_kf_index_widget(self,event):
        if self.index_kf_status:
            self.kf_current_page = 1
            self.index_kf_status = False
            self.index_widget.show()
            self.index_top_widget.show()
            self.index_top2_widget.show()
            self.kfjj_widget.hide()
        else:
            self.show_kfjj_widget()
            # self.kfjj_widget.show()
            self.index_top_widget.hide()
            self.index_widget.hide()
            self.index_top2_widget.hide()
            self.index_kf_status = True
    def toggle_cal_widget(self,event):
        if self.kf_cal_status:
            self.kf_cal_status=False
            self.calendarWidget.hide()
        else:
            self.kf_cal_status = True
            self.calendarWidget.show()
    #
    # def load_info_detail(self,index):
    #     # self.info_index=10 * (self.load_new_n - 1) + index
    #     article_title = self.title_l[index]
    #     article_url = self.info_url[10 * (self.load_new_n - 1) + index]
    #     self.show_detail_widget(self.title_l[index], self.tip_l[index],
    #                             self.article_l[index])
    #     self.update_view_history(self.title_l[index],
    #                              self.info_url[10 * (self.load_new_n - 1) + index])
    #     self.info_collect_lb_3.mousePressEvent = partial(self.open_url, url=article_url)
    #     self.info_collect_lb_2.mousePressEvent = partial(self.copy_to_clipboard, url=article_url)
    #     self.check_collect(title=article_title, url=article_url)
    #     self.info_detail_status = True
    #
    #     self.update_info_list()
    #     self.info_detail_listWidget.itemClicked.connect(self.load_info_list_detail)
    #
    #
    # def toggle_info_detail(self,event,index):#[0-9]
    #     try:
    #         if self.info_detail_status:
    #             self.info_detail_widget.hide()
    #             self.info_detail_status=False
    #             self.load_view_history()
    #         else:
    #             self.info_detail_return_lb_2.hide()
    #             self.info_detail_return_lb_3.hide()
    #             self.info_detail_return_lb.show()
    #             self.info_detail_widget.show()
    #             self.load_info_detail(index)
    #             self.info_list_lb.setText("最新新闻")
    #     except Exception as e:
    #         print(e)
    #
    #
    # def update_collect_list(self):
    #     self.info_collect_item_list_name = []
    #     for i in range(self.info_collect_widget.count()):
    #         item = self.info_collect_widget.item(i)
    #         self.info_collect_item_list_name.append(item.text())
    #
    # def update_history_list(self):
    #     self.info_history_item_list_name = []
    #     for i in range(self.info_history_widget.count()):
    #         item = self.info_history_widget.item(i)
    #         self.info_history_item_list_name.append(item.text())
    #
    # def toggle_info_collect_list_detail(self,item):
    #     try:
    #             self.info_collect_item_index = self.info_collect_item_list_name.index(item.text())
    #             with open(r"api/widget/information/collect", "r", encoding="utf8") as f:
    #                 collect_list = f.readlines()[::-1]
    #             article_title = collect_list[self.info_collect_item_index].split("|")[0].strip()
    #             article_url = collect_list[self.info_collect_item_index].split("|")[1].replace("\n", "")
    #             a = []
    #             a.append(article_url)
    #             article_info = asyncio.run(url_infos(a).main())[0]
    #             article_tip = "发布时间： " + article_info[0] + "  来源： " + article_info[1]
    #             article_content = article_info[2]
    #             self.info_detail_widget.show()
    #             self.info_detail_return_lb_2.show()
    #             self.info_detail_return_lb.hide()
    #             self.info_detail_return_lb_3.hide()
    #             self.show_detail_widget(article_title,article_tip,article_content)
    #             # self.update_view_history(article_title,self.info_url[10*self.load_new_n+self.info_index])
    #             self.check_collect(title=article_title,url=article_url)
    #             self.info_detail_status_2 = True
    #             self.info_list_lb.setText("全部收藏")
    #             self.update_detail_collect_list()
    #             # self.update_detail_viewed_list()
    #             self.info_detail_listWidget.itemClicked.connect(self.load_collect_list_detail)
    #             self.info_collect_lb_3.mousePressEvent = partial(self.open_url, url=article_url)
    #             self.info_collect_lb_2.mousePressEvent = partial(self.copy_to_clipboard, url=article_url)
    #     except Exception as e:
    #         print(e)
    #
    # def toggle_info_history_list_detail(self,item):
    #     try:
    #         self.info_history_item_index = self.info_history_item_list_name.index(item.text())
    #         with open(r"api/widget/information/view_history", "r", encoding="utf8") as f:
    #             collect_list = f.readlines()[::-1]
    #         article_title = collect_list[self.info_history_item_index].split("|")[0].strip()
    #         article_url = collect_list[self.info_history_item_index].split("|")[1].replace("\n", "")
    #         a = []
    #         a.append(article_url)
    #         article_info = asyncio.run(url_infos(a).main())[0]
    #         article_tip = "发布时间： " + article_info[0] + "  来源： " + article_info[1]
    #         article_content = article_info[2]
    #         self.info_detail_widget.show()
    #         self.info_detail_return_lb_3.show()
    #         self.info_detail_return_lb_2.hide()
    #         self.info_detail_return_lb.hide()
    #         self.show_detail_widget(article_title,article_tip,article_content)
    #         # self.update_view_history(article_title,self.info_url[10*self.load_new_n+self.info_index])
    #         self.check_collect(title=article_title,url=article_url)
    #         self.info_collect_lb_3.mousePressEvent = partial(self.open_url, url=article_url)
    #         self.info_collect_lb_2.mousePressEvent = partial(self.copy_to_clipboard, url=article_url)
    #         self.update_collect_list()
    #         self.info_detail_status_3 = True
    #         self.info_list_lb.setText("全部浏览历史")
    #         self.update_detail_viewed_list()
    #         self.info_detail_listWidget.itemClicked.connect(self.load_viewed_list_detail)
    #     except Exception as e:
    #         print(e)
    #
    # def load_info_list_detail(self,item):#从首页进入文章详情页面的
    #     try:
    #         self.info_item_index = self.info_title_l.index(item.text())
    #         article_title = self.info_title_l[self.info_item_index].strip()
    #         article_url = self.info_url[self.info_item_index].strip()
    #         a = []
    #         a.append(article_url)
    #         article_info = asyncio.run(url_infos(a).main())[0]
    #         article_tip = "发布时间： " + article_info[0] + "  来源： " + article_info[1]
    #         article_content = article_info[2]
    #         self.show_detail_widget(article_title,article_tip,article_content)
    #         self.check_collect(title=article_title,url=article_url)
    #     except Exception as e:
    #         print(e)
    #
    # def load_viewed_list_detail(self,item):#从首页进入文章详情页面的
    #     try:
    #         self.info_item_index = self.view_history_title.index(item.text())
    #         article_title = self.view_history_title[self.info_item_index].strip()
    #         article_url = self.view_history_url[self.info_item_index].strip()
    #         a = []
    #         a.append(article_url)
    #         article_info = asyncio.run(url_infos(a).main())[0]
    #         article_tip = "发布时间： " + article_info[0] + "  来源： " + article_info[1]
    #         article_content = article_info[2]
    #         self.show_detail_widget(article_title,article_tip,article_content)
    #         self.check_collect(title=article_title,url=article_url)
    #     except Exception as e:
    #         print(e)
    #
    # def load_collect_list_detail(self,item):#从首页进入文章详情页面的
    #     try:
    #         self.info_item_index = self.collect_title.index(item.text())
    #         article_title = self.collect_title[self.info_item_index].strip()
    #         article_url = self.collect_url[self.info_item_index].strip()
    #         a = []
    #         a.append(article_url)
    #         article_info = asyncio.run(url_infos(a).main())[0]
    #         article_tip = "发布时间： " + article_info[0] + "  来源： " + article_info[1]
    #         article_content = article_info[2]
    #         self.show_detail_widget(article_title,article_tip,article_content)
    #         self.check_collect(title=article_title,url=article_url)
    #     except Exception as e:
    #         print(e)
    #
    # def update_info_list(self):#刷新最新新闻列表项
    #     self.info_detail_listWidget.clear()
    #     for i in range(len(self.info_title_l)):
    #         self.info_detail_listWidget.addItem(self.info_title_l[i])
    #
    # def update_detail_viewed_list(self):#刷新最新新闻列表项
    #     self.info_detail_listWidget.clear()
    #     with open(r"api/widget/information\view_history", "r", encoding="utf8") as f:
    #         view_history = f.readlines()
    #     self.view_history_title=[i.split("|")[0] for i in view_history]
    #     self.view_history_url=[i.split("|")[1] for i in view_history]
    #     for i in view_history [::-1]:
    #         self.info_detail_listWidget.addItem(i.split("|")[0])
    #
    # def update_detail_collect_list(self):  # 刷新最新新闻列表项
    #     self.info_detail_listWidget.clear()
    #     with open(r"api/widget/information\collect", "r", encoding="utf8") as f:
    #         collect_ = f.readlines()
    #     self.collect_title = [i.split("|")[0] for i in collect_]
    #     self.collect_url = [i.split("|")[1] for i in collect_]
    #     for i in collect_[::-1]:
    #         self.info_detail_listWidget.addItem(i.split("|")[0])
    #
    # def hide_info_detail(self,event):
    #     self.info_detail_widget.hide()
    #     self.info_detail_status_2 = False
    #     self.load_view_history()
    #
    # def hide_info_detail_his(self,event):
    #     self.info_detail_widget.hide()
    #     self.info_detail_status_3 = False
    #     self.load_view_history()
    #
    # def info_move_to(self,status):
    #     if status:
    #         self.scrollArea_3.move(30, 210)
    #         self.info_load_lb.move(1370,890)
    #         self.info_up_lb.move(1365,830)
    #         self.info_up_img.move(1370,790)
    #         self.info_search_textEdit.move(380, 80)
    #         self.info_search_btn.move(900, 81)
    #         self.info_search_widget.move(380,121)
    #
    #     else:
    #         self.scrollArea_3.move(200, 210)
    #         self.info_load_lb.move(1540, 890)
    #         self.info_up_lb.move(1535, 830)
    #         self.info_up_img.move(1540, 790)
    #         self.info_search_textEdit.move(550,80)
    #         self.info_search_btn.move(1070,81)
    #         self.info_search_widget.move(550,121)
    #
    # def update_view_history(self,title,url):
    #     with open(r"api/widget/information\view_history", "r", encoding="utf8")as f:
    #         view_history=f.readlines()
    #     with open(r"api/widget/information\view_history", "a+",
    #               encoding="utf8") as f:
    #         data=title+"|"+url+"\n"
    #         if data not in set(view_history):
    #             f.write(title+"|"+url+"\n")
    #         f.close()
    #
    #     # self.load_view_history()
    #
    # def load_view_history(self):
    #     self.info_history_widget.clear()
    #     with open(r"api/widget/information\view_history", "r", encoding="utf8")as f:
    #         self.view_history=f.readlines()
    #         f.close()
    #     for i in self.view_history[::-1]:
    #         self.info_history_widget.addItem(i.split("|")[0])
    #     self.update_history_list()
    #
    # def toggle_info_history_list(self,event):
    #     if self.info_history_status:
    #         self.info_history_status = False
    #         self.info_history_widget.hide()
    #         # self.info_collect_widget.hide()
    #         self.info_move_to(False)
    #     else:
    #         self.info_collect_status = False
    #         self.info_history_status = True
    #         self.info_history_widget.show()
    #         self.info_collect_widget.hide()
    #         self.info_move_to(True)
    #         self.load_view_history()
    #
    # def toggle_info_collect_list(self, event):
    #     if self.info_collect_status:
    #         self.info_collect_status = False
    #         # self.info_history_widget.hide()
    #         self.info_collect_widget.hide()
    #         self.info_move_to(False)
    #     else:
    #         self.info_collect_status = True
    #         self.info_history_status = False
    #         self.info_collect_widget.show()
    #         self.info_history_widget.hide()
    #         self.info_move_to(True)
    #         self.load_collect()


    # toggle_list_news_widget = toggle_list_news_widget
    # toggle_table_index_widget = toggle_table_index_widget
    # toggle_dt_index_widget = toggle_dt_index_widget
    # toggle_send_stop = toggle_send_stop
    # thread_finished = thread_finished
    #
    # change_chat_model = change_chat_model
    # clear_context = clear_context
    # send_question = send_question
    # stop_response = stop_response
    # add_user_message = add_user_message
    # add_ai_message_stream = add_ai_message_stream
    # toggle_send_stop_img = toggle_send_stop_img
    #
