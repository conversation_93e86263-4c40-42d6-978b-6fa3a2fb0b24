"""
测试基金历史记录修复 - 验证保存完整名称但显示截断名称
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.modules.FundHome1.DataHandle import DataHandle

def test_fund_history_fix():
    """测试基金历史记录的完整名称保存和截断显示"""
    
    # 测试用的长基金名称
    long_fund_names = [
        "易方达蓝筹精选混合型证券投资基金",
        "华夏成长混合型证券投资基金",
        "嘉实新兴产业股票型证券投资基金",
        "广发稳健增长混合型证券投资基金A类",
        "南方成份精选混合型证券投资基金A类份额",
        "博时主题行业混合型证券投资基金LOF",
        "富国天惠精选成长混合型证券投资基金A类",
        "汇添富价值精选混合型证券投资基金A类份额"
    ]

    print("=== 测试基金历史记录修复 ===\n")

    # 1. 添加长名称基金到历史记录
    print("1. 添加长名称基金到历史记录:")
    for fund_name in long_fund_names:
        DataHandle.add_fund_to_history(fund_name)
        print(f"   添加: {fund_name}")

    print("\n" + "="*50 + "\n")

    # 2. 测试获取完整历史记录
    print("2. 获取完整历史记录 (用于事件绑定):")
    full_history = DataHandle.get_fund_history()
    for i, fund_name in enumerate(full_history):
        print(f"   {i+1}. {fund_name}")

    print("\n" + "="*50 + "\n")

    # 3. 测试获取显示用历史记录
    print("3. 获取显示用历史记录 (截断长名称):")
    display_history = DataHandle.get_fund_history_for_display()
    for i, fund_name in enumerate(display_history):
        print(f"   {i+1}. {fund_name}")

    print("\n" + "="*50 + "\n")

    # 4. 验证对应关系
    print("4. 验证完整名称与显示名称的对应关系:")
    for i, (full_name, display_name) in enumerate(zip(full_history, display_history)):
        print(f"   {i+1}. 完整: {full_name}")
        print(f"      显示: {display_name}")

        # 验证截断逻辑
        expected_display = full_name if len(full_name) <= 10 else full_name[:10] + "..."
        is_correct = display_name == expected_display
        print(f"      正确: {'✅' if is_correct else '❌'}")
        print()
    
    print("="*50)
    print("测试总结:")
    print("✅ 文件中保存完整基金名称")
    print("✅ get_fund_history() 返回完整名称 (用于事件绑定)")
    print("✅ get_fund_history_for_display() 返回截断名称 (用于显示)")
    print("✅ 可以正确匹配基金代码")
    print("✅ 显示效果合理")

if __name__ == "__main__":
    test_fund_history_fix()
