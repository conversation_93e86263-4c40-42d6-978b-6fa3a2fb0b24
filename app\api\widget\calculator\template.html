<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Line Chart</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <canvas id="myChart" width="600" height="251"></canvas>
    <p style="float: left;margin-top: 1px">time_start</p>
    <p style="float: right;margin-top: 1px">time_end</p>
    <script>
        function renderChart(labels, data,stepSize,min,max) {
            const ctx = document.getElementById('myChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: "kv",
                        data: data,
                        borderColor: '#2E481A',
                        borderWidth: 2,
                        pointRadius: 0
                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true,
                            stepSize: stepSize,
                            min:min,
                            max:max
                        },
                        x:{
                            display:false
                        }

                    }
                }
            });
        }
    </script>
</body>
</html>