import sys
import os
from PyQt6.QtWidgets import QApplication, QMainWindow
from PyQt6.QtWebEngineWidgets import QWebEngineView
from PyQt6.QtCore import QUrl
import random

class EChartDemoWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('ECharts 数据注入示例')
        self.setGeometry(100, 100, 1000, 600)

        # 生成模拟数据
        self.mock_data = self.generate_mock_data()

        # 创建 Web 视图
        self.browser = QWebEngineView()

        # 获取 HTML 文件路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # html_path = os.path.join(current_dir, '../../html/echart_demo.html')
        html_path = r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\api\widget\echart\demo_chart.html"
        print(html_path)

        # 加载 HTML 文件
        self.browser.setUrl(QUrl.fromLocalFile(html_path))

        # 页面加载完成后注入数据
        self.browser.loadFinished.connect(self.on_load_finished)

        # 设置为中央部件
        self.setCentralWidget(self.browser)

    def generate_mock_data(self):
        # 生成模拟的柱状图数据
        categories = ['一月', '二月', '三月', '四月', '五月', '六月']
        data1 = [random.randint(10, 100) for _ in range(6)]
        data2 = [random.randint(10, 100) for _ in range(6)]

        return {
            'title': '模拟数据图表展示',
            'legend': ['系列1', '系列2'],
            'xAxis': categories,
            'series': [
                {
                    'name': '系列1',
                    'type': 'bar',
                    'data': data1
                },
                {
                    'name': '系列2',
                    'type': 'bar',
                    'data': data2
                }
            ]
        }

    def on_load_finished(self):
        # 将数据转换为 JS 字符串
        data_str = str(self.mock_data).replace("'", '"')
        # 注入数据并调用 updateChart 函数
        js_code = f'updateChart({data_str});'
        self.browser.page().runJavaScript(js_code, self.js_callback)

    def js_callback(self, result):
        print(f'JS 执行结果: {result}')

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = EChartDemoWindow()
    window.show()
    sys.exit(app.exec())