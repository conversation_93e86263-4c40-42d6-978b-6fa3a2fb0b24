# PyCharm 性能优化指南

## 1. 解决代码提示问题

### 方法1：使用更智能的类型注释
```python
# 在文件顶部添加
from __future__ import annotations
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from main_w import Ui_MainWindow

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.ui: Ui_MainWindow = Ui_MainWindow()  # 这样就有提示了
        self.ui.setupUi(self)
```

### 方法2：使用类型存根文件
创建 `.pyi` 文件来提供类型信息而不影响运行时性能。

### 方法3：在方法内部使用类型断言
```python
def some_method(self):
    ui = self.ui  # type: Ui_MainWindow
    ui.  # 这里就会有代码提示
```

## 2. PyCharm 性能优化设置

### 内存设置
1. Help → Change Memory Settings
2. 设置为 2048-4096MB

### 禁用不必要的功能
1. File → Settings → Editor → General → Code Completion
   - 取消 "Show suggestions as you type"
   - 设置 "Case sensitive completion" 为 "First letter"

2. File → Settings → Editor → Inspections
   - 禁用 "Spelling"
   - 禁用 "Proofreading"
   - 禁用不需要的语言检查

3. File → Settings → Plugins
   - 禁用不使用的插件

### 索引优化
1. File → Settings → System Settings
   - 取消 "Synchronize files on frame or editor tab activation"
   - 取消 "Save files on frame deactivation"

2. 排除大型目录：
   - 右键 __pycache__ → Mark Directory as → Excluded
   - 右键 .git → Mark Directory as → Excluded

### 代码检查优化
1. File → Settings → Editor → Inspections → Python
   - 只保留必要的检查
   - 禁用复杂的类型检查

## 3. 项目结构优化

### 清理缓存文件
定期清理 __pycache__ 目录：
```cmd
for /d /r . %d in (__pycache__) do @if exist "%d" rd /s /q "%d"
```

### 使用 .gitignore
```
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
```

## 4. 代码组织建议

### 使用工厂模式减少循环导入
```python
# ui_factory.py
def create_main_window():
    from main_w import Ui_MainWindow
    return Ui_MainWindow()

# main_window.py
from ui_factory import create_main_window

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.ui = create_main_window()
        self.ui.setupUi(self)
```

### 延迟导入
```python
def get_ui_class():
    if not hasattr(get_ui_class, '_ui_class'):
        from main_w import Ui_MainWindow
        get_ui_class._ui_class = Ui_MainWindow
    return get_ui_class._ui_class
```
