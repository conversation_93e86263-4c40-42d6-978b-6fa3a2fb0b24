<!DOCTYPE html>
<html style="height: 100%">
<head>
    <meta charset="utf-8">
    <title>ECharts 数据注入示例</title>
</head>
<body style="height: 100%; margin: 0">
    <div id="chart_container" style="width: 100%; height: 100%"></div>
    <script src="../../../html/echarts.min.js"></script>
    <script>
        // 初始化图表
        var chart = echarts.init(document.getElementById('chart_container'));
        var option = {};

        // 接收数据并更新图表
        function updateChart(data) {
            option = {
                title: {
                    text: data.title
                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: data.legend
                },
                xAxis: {
                    type: 'category',
                    data: data.xAxis
                },
                yAxis: {
                    type: 'value'
                },
                series: data.series
            };
            chart.setOption(option);
        }
    </script>
</body>
</html>