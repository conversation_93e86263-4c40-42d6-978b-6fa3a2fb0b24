def get_bar_chart_option(self):
    """获取柱状图配置"""
    # 生成更多模拟数据
    categories = [
        '人工智能', '芯片', '减肥药', '破净股', '机器人', 'CPO', '电力', '汽车整车',
        '新能源', '光伏', '风电', '锂电池', '储能', '氢能源', '核电', '5G',
        '物联网', '云计算', '大数据', '区块链', '虚拟现实', '生物医药',
        '医疗器械', '疫苗', '创新药', '仿制药', '中药', '白酒',
        '食品饮料', '农业', '种业', '化肥', '农药', '房地产',
        '建筑', '水泥', '钢铁', '有色金属', '煤炭', '银行'
    ]
    
    # 将每个分类名称的字符间添加换行符
    vertical_categories = ['\n'.join(list(category)) for category in categories]
    
    import random
    data = []
    for _ in range(len(categories)):
        if random.random() < 0.6:
            data.append(round(random.uniform(0.1, 30), 2))
        else:
            data.append(round(random.uniform(-25, -0.1), 2))
    
    # 按数据从高到低排序
    paired_data = list(zip(vertical_categories, data))
    paired_data.sort(key=lambda x: x[1], reverse=True)
    
    sorted_categories = [item[0] for item in paired_data]
    sorted_data = [item[1] for item in paired_data]
    
    return {
        'title': {'text': '主力资金流向', 'left': 'center'},
        'tooltip': {
            'trigger': 'axis',
            'formatter': '''function(params) {
                var param = params.find(p => p.value !== null);
                if (param) {
                    var value = param.value;
                    var name = param.name.replace(/\\n/g, '');
                    var type = value >= 0 ? '净流入' : '净流出';
                    return name + '<br/>' + type + ': ' + Math.abs(value) + '亿';
                }
                return '';
            }'''
        },
        'legend': {
            'data': ['主力净流入', '主力净流出'],
            'top': 10,
            'right': 20,
            'show': True
        },
        'toolbox': {
            'show': True,
            'feature': {
                'dataZoom': {'yAxisIndex': 'none'},
                'restore': {},
                'saveAsImage': {}
            },
            'right': '5%',
            'top': '5%'
        },
        'grid': {
            'left': '3%',
            'right': '4%',
            'bottom': '25%',
            'top': '15%',
            'containLabel': True
        },
        'xAxis': {
            'type': 'category',
            'data': sorted_categories,
            'axisLabel': {
                'rotate': 0,
                'interval': 0,
                'textStyle': {'align': 'center'},
                'lineHeight': 16
            },
            'axisTick': {'show': False}
        },
        'yAxis': {
            'type': 'value',
            'axisLabel': {'formatter': '{value}亿'},
            'splitLine': {'show': True, 'lineStyle': {'type': 'dashed'}}
        },
        'dataZoom': [
            {
                'type': 'slider',
                'show': True,
                'xAxisIndex': [0],
                'start': 0,
                'end': 25,
                'bottom': '5%',
                'height': 20
            },
            {
                'type': 'inside',
                'xAxisIndex': [0],
                'start': 0,
                'end': 25
            }
        ],
        'series': [
            {
                'name': '主力净流入',
                'type': 'bar',
                'barWidth': '60%',
                'barCategoryGap': '20%',
                'data': [value if value >= 0 else None for value in sorted_data],
                'itemStyle': {'color': '#ff4d4f'},
                'label': {
                    'show': True,
                    'position': 'top',
                    'formatter': '{c}亿',
                    'color': '#ff4d4f',
                    'fontSize': 10
                }
            },
            {
                'name': '主力净流出',
                'type': 'bar',
                'barWidth': '60%', 
                'barCategoryGap': '20%',
                'data': [value if value < 0 else None for value in sorted_data],
                'itemStyle': {'color': '#52c41a'},
                'label': {
                    'show': True,
                    'position': 'bottom',
                    'formatter': '{c}亿',
                    'color': '#52c41a',
                    'fontSize': 10
                }
            }
        ]
    }

