import json

import requests

url="https://api.fund.eastmoney.com/FundRank/GetHbRankList?intCompany=0&MinsgType=&IsSale=1&strSortCol=SYL_1N&orderType=desc&pageIndex=1&pageSize=10000&callback=jQuery18309600284469421518_1744893312771&_=1744893312997"
header={
    "User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0",
    "Referer":"https://fund.eastmoney.com/",
    "Cookie":"qgqp_b_id=50d74afee65419b05e9120f0df53c69f; emshistory=%5B%22%E5%8D%8A%E5%AF%BC%E4%BD%93%22%2C%22%E9%BB%84%E9%B8%A1%22%2C%22%E4%BA%BA%E6%80%A7%E6%9C%BA%E5%99%A8%E4%BA%BA%22%2C%22%E4%BA%BA%E5%BD%A2%E6%9C%BA%E5%99%A8%E4%BA%BA%22%2C%22%E9%B8%A1%E8%82%89%22%2C%22%E9%BB%84%E9%87%91%22%2C%22CPO%22%2C%22%E6%AD%8C%E5%B0%94%E8%82%A1%E4%BB%BD%22%2C%22%E5%B1%B1%E4%B8%9C%E9%BB%84%E9%87%91%22%2C%22%E7%89%9B%E5%A5%B6%22%5D; mtp=1; ct=WbAM5gZmXDQrlDkq16RoK_RznGcQmui6r7McHKezekoOVfWS4eRm23tSyLCfgpJ91gTgs9aWy3mf5cp8N0AWgTPUAcJbIH5Zz50CRCPnxxkxSfppe40XfGBeWv4aT91DvrWhzpDfcgctRSDiRgMR31yGZCKtxRpjbqpj91xZv64; ut=FobyicMgeV60R-wNFHdtrEkjyi1hTBkF_XKkzaxQ0FFYPohwhuROtceUOY7pLm7jbXajA9ZTTsuKd58BgJJ6cCwAtPrirJ8aEW60Gq79Fo5ldEgytoqlN0PHsXmjNx5rk4qcImtt25inSZOftasUXjZk0yFEvD8jB2N24NIcLDRwv86c9ba6_kM4_Iiwq36fEfYO0oCrQdghgUyNYiDuMNLgCZxWS-8z_nqmXnWUIg6S60j-df8KOaUcsMPjxcU07sJHIjGeESQ; pi=1541316586261916%3Bo1541316586261916%3B%E5%9F%BA%E6%B0%91223A8K7027%3BNsdCm%2BJdoCn8FEqpD2aXGcTSevswUHi20bQV5DwOvgt%2F84sWHGGiHlb3c9G3nMGFCPicGe6e%2B6jwbdTjvlDHY1Kth9WCVJ3%2F4MMM5%2Bbebtlwev0tVal9gXBFEMA1nSkGQ5vjdPVGnQM3XR5sE%2BBRUJbIM09%2FjQQTDX3VVQWj6EoHPXgn2JtcNizDPolZw7wqRb2Hn55G%3BuZgq8J7XOPD0tBYPwhepl1QISKJegWSKEP3UPB4BXR1MX3WY7e3roK6kACNr5LkxC9APFTtcHa0NbcOItnQDU5q1XtlerSzHnicHr7jhIrAU%2BwIoysgG1JTm0lxUqr7fwuKboBRZI6gk6BZhYM18kI2hdwmiJg%3D%3D; uidal=1541316586261916%e5%9f%ba%e6%b0%91223A8K7027; sid=*********; vtpst=|; st_si=38199516453511; st_asi=delete; ap_0_13a7d068=1; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=11; st_psi=20250417203451270-112200312936-1839219557"

}
response=requests.get(url,headers=header,verify=False,)
response_text=json.loads(response.text.strip("jQuery18309600284469421518_1744893312771(").replace(")",""))
# print(response_text["Data"][0])
data_list=[]
def data_format(s):
    if s=="":
        return "--"
    else:
        return "{:.2f}%".format(float(s))
for i in response_text["Data"]:
    data_list.append([i["FCODE"],#基金代码
                      i["SHORTNAME"],#基金简称
                      i["FSRQ"],#日期
                      i["DWJZ"],#万份收益
                      data_format(i["LJJZ"]),#7日年化收益
                      data_format(i["FTYI"]),#14日年化收益
                      data_format(i["TEYI"]),#28日年化收益
                      data_format(i["SYL_Y"]),#近1月
                      data_format(i["SYL_3Y"]),#近3月
                      data_format(i["SYL_6Y"]),#近6月
                      data_format(i["SYL_1N"]),#近1年
                      data_format(i["SYL_2N"]),#近2年
                      data_format(i["SYL_3N"]),#近3年
                      data_format(i["SYL_5N"]),#近5年
                      data_format(i["SYL_JN"]),#今年来
                      data_format(i["SYL_LN"]),#成立来
                      ])

print(data_list[0])
print(data_list[1])
