import matplotlib.pyplot as plt

# 数据
x = [1, 2, 3, 4, 5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,]
y = [2, 3, 5, 7, 11,12,6,9,3,9,11,12,13,14,15,16,17,18,19]

# 创建图表和坐标轴
fig, ax = plt.subplots(figsize=(9, 3))

# 绘制线状图
ax.plot(x, y, marker='o', linestyle='-', color='red', label='数据线')

# 隐藏标签
ax.set_xticks([])  # 隐藏 X 轴刻度
ax.set_yticks([])  # 隐藏 Y 轴刻度
ax.set_xticklabels([])  # 隐藏 X 轴标签
ax.set_yticklabels([])  # 隐藏 Y 轴标签

# 在下方添加颜色覆盖
ax.fill_between(x, y, color='red', alpha=0.3)  # 设置颜色和透明度

# 设置背景透明
fig.patch.set_alpha(0)  # 设置图表背景透明
ax.set_facecolor('none')  # 设置坐标轴背景透明
for spine in ax.spines.values():
    spine.set_visible(False)  # 隐藏所有框线

# 保存为透明背景的图片
plt.savefig('line_plot_transparent.png', dpi=300, transparent=True)

# 显示图表
plt.show()