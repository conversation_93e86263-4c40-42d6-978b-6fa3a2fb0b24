"""
测试基金历史记录功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication, QMainWindow
from main_w import Ui_MainWindow
from app.modules.FundHome1.FundHome1 import FundHome1
from app.modules.FundHome1.DataHandle import DataHandle

def test_fund_history():
    """测试基金历史记录功能"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    ui = Ui_MainWindow()
    ui.setupUi(main_window)
    
    # 添加一些测试数据
    data_handler = DataHandle()
    test_funds = [
        "易方达蓝筹精选混合",
        "华夏成长混合",
        "嘉实新兴产业股票",
        "广发稳健增长混合",
        "南方成份精选混合A",
        "博时主题行业混合",
        "富国天惠精选成长混合A",
        "汇添富价值精选混合A",
        "招商中证白酒指数分级",
        "中欧时代先锋股票A"
    ]
    
    # 添加测试数据到历史记录
    for fund in test_funds:
        data_handler.add_fund_to_history(fund)
    
    # 创建 FundHome1 实例
    fund_home = FundHome1(ui)
    
    # 显示主窗口
    main_window.show()
    
    print("测试基金历史记录功能:")
    print("1. 应该显示最多8个历史记录标签")
    print("2. 鼠标悬停时应该变色为 #FF4400")
    print("3. 点击标签应该触发 goto_detail_2 方法")
    print("4. 点击后应该更新浏览记录顺序")
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    test_fund_history()
