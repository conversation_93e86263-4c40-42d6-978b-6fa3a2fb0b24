import ast
import requests
from urllib.parse import urlencode
import random
import json
header={
    "Cookie":'qgqp_b_id=50d74afee65419b05e9120f0df53c69f; fund_registerAd_1=1; st_si=18639092801927; st_asi=delete; ASP.NET_SessionId=iurcwwhnaijp1l0zlgb2mjnn; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=2; st_psi=20250319095514921-112200312936-9443065310',
    "Referer":"https://fund.eastmoney.com/data/fundranking.html"
}
base_url="https://fund.eastmoney.com/data/rankhandler.aspx?"
# 参数字典
params = {
    "op": "ph",#排行
    "dt": "kf",#开放基金
    "ft":"all",#fund_type类型：全部，指数，股票，混合，债券，qdii,fof ["all","zs","gp","hh","zq","qdii","fof"]
    "rs":"",
    "gs":0,
    "sc":"1nzf",#排序依据 基金代码 基金简称， 日期，单位净值，累计净值，且增长率，近1周，近1月，近3月，近6月，近1年，近2年，近3年，今年来，成立来，自定义
    #["dm","jc","jzrq","dwjz","ljjz","rzdf","zzf","1yzf","3yzf","6yzf","1nzf","2nzf","3nzf","jnzf","lnzf","qjzf"]
    "st":"desc",#升序，降序["desc","asc"]
    "sd":"2024-03-19",#sc="qjzf" sd起始日期
    "ed":"2025-03-19",#sc="qjzf" ed结束日期
    "qdii":"",#所有默认为空
    #指数，债券，qdii，
    #指数{"沪深指数":"053|","行业主题":"054|","大盘指数":"01|","中小盘指数":"02,03|","股票指数":"001|","债券指数":"003|","海外指数":"000003|"}
    #债券{"长期纯债":"041|","短期纯债":"042|","混合债基":"043|","定期开放债券":"008|","可转债":"045|",}
    #qdii{"全球股票":"311","亚太股票":"312","大中华区股票":"313","美国股票":"317","股债混合":"320","债券":"330","商品":"340",}
    "tabSubtype":",,,,,",
    "pi":1,#页数
    "pn":50,#单页条数
    "dx":1,
    "v":0.7143502235731323
}
base_url1="https://fund.eastmoney.com/data/rankhandler.aspx?"+f"op={params["op"]}&"+f"dt={params["dt"]}&"+f"ft={params["ft"]}&"+f"re={params["rs"]}&"+f"gs={params["gs"]}&"+f"sc={params["sc"]}&"+f"st={params["st"]}&"+f"sd={params["sd"]}&"+f"ed={params["ed"]}&"+f"qdii={params["qdii"]}&"+f"tabSubtype={params["tabSubtype"]}&"+f"pi={params["pi"]}&"+f"pn={params["pn"]}&"+f"dx={params["dx"]}&"+f"v={str(params["v"])}"
# base_url="https://fund.eastmoney.com/data/rankhandler.aspx?op=ph&dt=kf&ft=zs&rs=&gs=0&sc=1nzf&st=desc&sd=2024-03-19&ed=2025-03-19&qdii=%7C&tabSubtype=,,,,,&pi=1&pn=50&dx=1&v=0.2188906602910081"


table=["基金代码","基金名称","基金简称","日期","单位净值","累计净值","日增长率","近1周","近1月","近3月","近1年","近2年","近3年","今年来","成立日期","成立以来","自定义"]
l="015739,国泰中证港股通科技ETF发起联接A,GTZZGGTKJETFFQLJA,2025-03-18,1.3025,1.3025,3.38,4.1,8.67,37.61,67.76,74.67,50.27,,36.22,30.25,2022-06-07"
# data=requests.get(base_url1,headers=header)
# # print(data.text)
# with open("temp","w",encoding="utf8")as f:
#     f.write(data.text)
with open("temp","r",encoding="utf8")as f:
    data=f.read()
json1=data.replace("var rankData = {datas:","").replace("};","")
json2=data.replace("var rankData = {datas:","")

split_s=json2.index("]")+1#分割符索引

data_l=ast.literal_eval(json2[0:split_s])#行数据列表
data_list=[]
for i in data_l:
    tt=list(i.split(",")[0:17])
    tt = [
        "-" if item == "" and 4 <= i < 15  # 对第 5-15 个元素，空字符串替换为 "-"
        else item + "%" if item != "" and 4 <= i < 15  # 对第 5-15 个元素，非空字符串加上 "%"
        else item  # 其他元素保持不变
        for i, item in enumerate(tt)
    ]

    # tt.append("{:.2f}%".format(float(i.split(",")[18])))
    if i.split(",")[18]=="":
        tt.append("-")
    else:
        tt.append("{:.2f}%".format(float(i.split(",")[18])))
    data_list.append(tt)
    # print(tt)

data_all_info=json1[split_s+1:].split(",")
print(data_all_info)
# info_key,info_value=[],[]
# for i in range(0,len(data_all_info)):
#     info_key.append(data_all_info[i].split(":")[0])
#     info_value.append(data_all_info[i].split(":")[1])
# info_dict=dict(zip(info_key,info_value))
# print(info_dict)