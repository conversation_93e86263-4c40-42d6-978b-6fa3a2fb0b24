import asyncio

import aiohttp
import requests
from concurrent.futures import ThreadPoolExecutor
import threading
import time
# 需要访问的网址列表
urls = [
    "http://finance.eastmoney.com/a/202503233353273079.html",
    "http://finance.eastmoney.com/a/202503223353218054.html",
    "http://finance.eastmoney.com/a/202503233353295597.html",
    "http://finance.eastmoney.com/a/202503233353302967.html",
    "http://finance.eastmoney.com/a/202503233353248747.html",
"http://finance.eastmoney.com/a/202503233353273079.html",
    "http://finance.eastmoney.com/a/202503223353218054.html",
    "http://finance.eastmoney.com/a/202503233353295597.html",
    "http://finance.eastmoney.com/a/202503233353302967.html",
    "http://finance.eastmoney.com/a/202503233353248747.html",
"http://finance.eastmoney.com/a/202503233353273079.html",
    "http://finance.eastmoney.com/a/202503223353218054.html",
    "http://finance.eastmoney.com/a/202503233353295597.html",
    "http://finance.eastmoney.com/a/202503233353302967.html",
    "http://finance.eastmoney.com/a/202503233353248747.html",
"http://finance.eastmoney.com/a/202503233353273079.html",
    "http://finance.eastmoney.com/a/202503223353218054.html",
    "http://finance.eastmoney.com/a/202503233353295597.html",
    "http://finance.eastmoney.com/a/202503233353302967.html",
    "http://finance.eastmoney.com/a/202503233353248747.html",
"http://finance.eastmoney.com/a/202503233353273079.html",
    "http://finance.eastmoney.com/a/202503223353218054.html",
    "http://finance.eastmoney.com/a/202503233353295597.html",
    "http://finance.eastmoney.com/a/202503233353302967.html",
    "http://finance.eastmoney.com/a/202503233353248747.html",
]
s=time.time()
# 共享列表，用于存储结果
results = [None] * len(urls)

# 锁对象，用于确保按顺序存储
result_lock = threading.Lock()


async def fetch_url_content(session, url):
    try:
        async with session.get(url) as response:
            content = await response.text()
            return url, content[:500]  # 只取前100个字符作为示例
    except Exception as e:
        return url, f"请求失败: {e}"

async def main():
    async with aiohttp.ClientSession() as session:
        tasks = [fetch_url_content(session, url) for url in urls]
        results = await asyncio.gather(*tasks)
        for i, (url, content) in enumerate(results):
            print(f"结果 {i + 1} (URL: {url}): {content}")

# 运行异步任务
asyncio.run(main())
e=time.time()
print(e-s)