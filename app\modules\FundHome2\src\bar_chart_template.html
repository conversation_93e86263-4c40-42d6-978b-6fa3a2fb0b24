<!DOCTYPE html>
<html style="height: 100%">
<head>
    <meta charset="utf-8">
    <title>资金流向图表</title>
</head>
<body style="height: 100%; margin: 0">
    <div id="chart_container" style="width: 100%; height: 100%"></div>
    <script src="../../../html/echarts.min.js"></script>
    <script>
        var chart = echarts.init(document.getElementById('chart_container'));
        
        function updateChart(data) {
            console.log('接收到的数据:', data); // 调试用
            
            var option = {
                title: {
                    text: '主力资金流向',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function(params) {
                        var param = params.find(p => p.value !== null && p.value !== undefined);
                        if (param) {
                            var value = parseFloat(param.value);
                            var name = param.name.replace(/\n/g, '');
                            var type = value >= 0 ? '净流入' : '净流出';
                            return name + '<br/>' + type + ': ' + Math.abs(value) + '亿';
                        }
                        return '';
                    }
                },
                legend: {
                    data: ['主力净流入', '主力净流出'],
                    top: 10,
                    right: 20,
                    show: true
                },
                grid: {
                    left: '8%',
                    right: '8%',
                    bottom: '35%',
                    top: '15%',
                    containLabel: false
                },
                toolbox: {
                    show: true,
                    feature: {
                        dataZoom: {
                            yAxisIndex: 'none'
                        },
                        // restore: {},
                        // saveAsImage: {}
                    },
                    right: '5%',
                    top: '5%'
                },
                xAxis: {
                    type: 'category',
                    data: data.categories,
                    axisLabel: {
                        rotate: 0,
                        interval: 0,
                        textStyle: {
                            align: 'center',
                            baseline: 'top',
                            fontSize: 12
                        },
                        lineHeight: 12,
                        margin: 15
                    },
                    axisTick: {
                        show: false,
                        alignWithLabel: true,
                        length: 6
                    },
                    axisLine: {
                        show: true
                    },
                    boundaryGap: true,
                    // splitLine: {
                    //     show: false
                    // },
                    // offset: 10
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        formatter: '{value}亿'
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            type: 'dashed'
                        }
                    },
                },
                dataZoom: [
                    {
                        type: 'slider',
                        show: true,
                        xAxisIndex: [0],
                        start: 0,
                        end: 7,
                        bottom: '17%',
                        height: 28,
                        zoomLock: true,
                        moveOnMouseMove: false,
                        preventDefaultMouseMove: false
                    },
                    // {
                    //     type: 'inside',
                    //     xAxisIndex: [0],
                    //     start: 0,
                    //     end: 10,
                    //     zoomOnMouseWheel: false,
                    //     moveOnMouseMove: true,
                    //     moveOnMouseWheel: true
                    // }
                ],
                series: [
                    {
                        name: '主力净流入',
                        type: 'bar',
                        barWidth: '50%',
                        barCategoryGap: '30%',
                        barGap: 0,
                        data: data.data.map(function(value) {
                            var numValue = parseFloat(value);
                            return !isNaN(numValue) && numValue >= 0 ? numValue : null;
                        }),
                        itemStyle: {
                            color: '#FF3F3E'
                        },
                        label: {
                            show: true,
                            position: 'top',
                            formatter: function(params) {
                                return params.value ? parseFloat(params.value).toFixed(1) + '亿' : '';
                            },
                            color: '#FF3F3E',
                            fontSize: 10
                        }
                    },
                    {
                        name: '主力净流出',
                        type: 'bar',
                        barWidth: '50%',
                        barCategoryGap: '30%',
                        barGap: 0,
                        data: data.data.map(function(value) {
                            var numValue = parseFloat(value);
                            return !isNaN(numValue) && numValue < 0 ? numValue : null;
                        }),
                        itemStyle: {
                            color: '#06960A'
                        },
                        label: {
                            show: true,
                            position: 'bottom',
                            formatter: function(params) {
                                return params.value ? parseFloat(params.value).toFixed(1) + '亿' : '';
                            },
                            color: '#06960A',
                            fontSize: 10
                        }
                    }
                ]
            };
            
            // console.log('处理后的净流入数据:', option.series[0].data); // 调试用
            // console.log('处理后的净流出数据:', option.series[1].data); // 调试用
            
            chart.setOption(option);
            chart.resize();
        }
    </script>
</body>
</html>












