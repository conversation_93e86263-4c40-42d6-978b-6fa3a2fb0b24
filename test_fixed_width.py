"""
测试固定8等分宽度的历史记录标签
"""

import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QWidget, QLabel, QVBoxLayout
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

class FixedWidthDemo(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("固定8等分宽度演示")
        self.setGeometry(100, 100, 1200, 400)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 添加说明
        info_label = QLabel("✅ 固定8等分宽度：无论有多少个历史记录，每个标签宽度都相同", self)
        info_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2E8B57; margin: 10px;")
        layout.addWidget(info_label)
        
        # 测试不同数量的历史记录
        self.test_scenarios = [
            {
                "title": "测试1：只有3个历史记录",
                "data": ["易方达蓝筹精选混合", "华夏成长混合", "嘉实新兴产业股票"]
            },
            {
                "title": "测试2：有6个历史记录", 
                "data": ["易方达蓝筹精选混合", "华夏成长混合", "嘉实新兴产业股票", 
                        "广发稳健增长混合", "南方成份精选混合A", "博时主题行业混合"]
            },
            {
                "title": "测试3：满8个历史记录",
                "data": ["易方达蓝筹精选混合", "华夏成长混合", "嘉实新兴产业股票",
                        "广发稳健增长混合", "南方成份精选混合A", "博时主题行业混合",
                        "富国天惠精选成长混合A", "汇添富价值精选混合A"]
            }
        ]
        
        self.current_test = 0
        self.history_labels = []
        
        # 创建切换按钮
        self.switch_button = QLabel("点击切换测试场景", self)
        self.switch_button.setStyleSheet("""
            QLabel {
                background-color: #4CAF50;
                color: white;
                padding: 10px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                text-align: center;
            }
            QLabel:hover {
                background-color: #45a049;
            }
        """)
        self.switch_button.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.switch_button.setFixedHeight(40)
        self.switch_button.mousePressEvent = self.switch_test
        layout.addWidget(self.switch_button)
        
        # 创建初始标签
        self.create_fixed_width_labels()
    
    def create_fixed_width_labels(self):
        """创建固定8等分宽度的历史记录标签"""
        # 清除之前的标签
        for label in self.history_labels:
            label.deleteLater()
        self.history_labels.clear()
        
        # 获取当前测试数据
        current_scenario = self.test_scenarios[self.current_test]
        history_list = current_scenario["data"]
        
        # 更新标题
        self.switch_button.setText(f"{current_scenario['title']} - 点击切换")
        
        if not history_list:
            return
        
        # 模拟原始标签的位置和尺寸
        start_x = 50
        start_y = 150
        label_height = 40
        available_width = 1100  # 可用宽度
        
        # 固定8等分宽度计算
        spacing = 10  # 标签之间的间距
        total_spacing = 7 * spacing  # 8个标签之间有7个间距
        label_width = (available_width - total_spacing) // 8  # 固定8等分
        
        print(f"\n=== {current_scenario['title']} ===")
        print(f"历史记录数量: {len(history_list)}")
        print(f"可用宽度: {available_width}px")
        print(f"标签宽度: {label_width}px (固定8等分)")
        print(f"间距: {spacing}px")
        
        for i, fund_name in enumerate(history_list):
            # 创建标签
            history_label = QLabel(parent=self.centralWidget())
            
            # 设置位置和尺寸 - 使用固定8等分宽度
            x_pos = start_x + i * (label_width + spacing)
            history_label.setGeometry(x_pos, start_y, label_width, label_height)
            
            # 生成显示用的截断名称
            display_name = fund_name
            if len(fund_name) > 12:
                display_name = fund_name[:12] + "..."
            
            # 设置显示文本
            history_label.setText(display_name)
            history_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            
            # 设置工具提示显示完整名称
            history_label.setToolTip(f"完整名称: {fund_name}")
            
            # 设置字体
            font = QFont()
            font.setPointSize(11)
            font.setBold(True)
            history_label.setFont(font)
            
            # 设置样式表（保持您修改的样式）
            history_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    border: 1px solid #ddd;
                    border-radius: 6px;
                    padding: 4px;
                }
                QLabel:hover {
                    color: #FF4400;
                    background-color: #fff;
                    border-color: #FF4400;
                }
            """)
            
            # 绑定点击事件
            history_label.mousePressEvent = lambda event, fund=fund_name, idx=i: self.on_label_clicked(fund, idx, event)
            
            # 显示标签
            history_label.show()
            
            # 添加到管理列表
            self.history_labels.append(history_label)
            
            print(f"标签 {i+1}: 位置({x_pos}, {start_y}), 宽度({label_width}), 文本: {display_name}")
    
    def switch_test(self, event):
        """切换测试场景"""
        self.current_test = (self.current_test + 1) % len(self.test_scenarios)
        self.create_fixed_width_labels()
    
    def on_label_clicked(self, fund_name, index, event):
        """标签点击事件处理"""
        print(f"\n点击了: {fund_name} (索引: {index})")

def main():
    app = QApplication(sys.argv)
    
    demo = FixedWidthDemo()
    demo.show()
    
    print("🎯 固定8等分宽度演示")
    print("="*50)
    print("✅ 每个标签宽度固定为总宽度的1/8")
    print("✅ 无论有多少个历史记录，宽度都相同")
    print("✅ 保持您修改的样式和位置")
    print("✅ 点击按钮可以切换不同数量的测试场景")
    print("="*50)
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
