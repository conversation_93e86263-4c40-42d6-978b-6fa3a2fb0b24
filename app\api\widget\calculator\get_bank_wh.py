import json
from PyQt6.QtCore import QThread, pyqtSignal
import requests

class get_bank_wh_data(QThread):
    finished = pyqtSignal(list)
    def __init__(self, bank_name):
        super().__init__()
        self.bank_name=bank_name
        self.result_list = []
        self.header={
            "User-Agent":" Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0",
            "referer":"https://www.huilvwang.com/"
        }

    def run(self):
        try:
            self.url="https://stock.finance.sina.com.cn/forex/api/openapi.php/ForexService.getAllBankForex?ft=part&callback=jQuery18208467864580261412_1744801166110&_=*************"
            response=requests.get(self.url,self.header,verify=False,)
            response_text=response.text
            data=json.loads(response_text.replace("jQuery18208467864580261412_1744801166110(","").replace(")","").replace("/*<script>location.href='//sina.com';</script>*/","")
)
            bank_data=data["result"]["data"][self.bank_name]
            print(bank_data)
            for i in bank_data:
                self.result_list.append(f"{i["name"]}|{i["symbol"]}|{i["xh_buy"]}|{i["xh_sell"]}")
            # print(self.result_list)

            self.finished.emit(self.result_list)
        except Exception  as e:
            print(e)
