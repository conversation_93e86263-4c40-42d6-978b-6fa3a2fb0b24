"""
测试板块资金流web渲染功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication, QMainWindow
from main_w import Ui_MainWindow
from app.modules.FundHome2.FundHome2 import FundHome2

def test_bkzj_web():
    """测试板块资金流web渲染功能"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    ui = Ui_MainWindow()
    ui.setupUi(main_window)
    
    # 创建 FundHome2 实例
    fund_home2 = FundHome2(ui)
    
    # 显示主窗口
    main_window.show()
    
    print("测试板块资金流web渲染功能:")
    print("=" * 50)
    
    print("\n检查初始化状态:")
    print(f"当前板块类型: {fund_home2.current_bkzj_type}")
    print(f"当前时间类型: {fund_home2.current_bkzj_time}")
    
    print("\n检查HTML模板文件:")
    html_path = r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\modules\FundHome2\src\bar_chart_template.html"
    if os.path.exists(html_path):
        print(f"✅ HTML模板文件存在: {html_path}")
    else:
        print(f"❌ HTML模板文件不存在: {html_path}")
    
    print("\n检查ECharts库文件:")
    echarts_path = r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\html\echarts.min.js"
    if os.path.exists(echarts_path):
        print(f"✅ ECharts库文件存在: {echarts_path}")
    else:
        print(f"❌ ECharts库文件不存在: {echarts_path}")
    
    print("\n功能说明:")
    print("1. 初始化时应该自动加载地域资金流的今日排行数据")
    print("2. 点击不同类型标签应该加载对应数据")
    print("3. 点击不同时间RadioButton应该加载对应数据")
    print("4. Web组件应该显示ECharts图表")
    
    print("\n如果图表无法显示，可能的原因:")
    print("- 网络请求失败")
    print("- 数据格式错误")
    print("- JavaScript执行失败")
    print("- HTML模板加载失败")
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    test_bkzj_web()
