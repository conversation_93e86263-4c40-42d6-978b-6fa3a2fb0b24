def init_right_zfb_widget_status(self):
    self.right_table=["","行业资金","概念资金","地域资金"]
    self.right_table_t=["今日流向排行榜","5日流向排行榜","10日流向排行榜"]
    self.right_bang_index = 1  # 左边行情排行榜left_label索引

def change_r_l_bang(self,event):
    if self.right_bang_index==2:
        self.r_l_zfb.setStyleSheet("color:#CDCDCD")
        self.right_bang_index -= 1
    elif 1 < self.right_bang_index < 4:
        self.r_l_zfb.setStyleSheet("""QLabel{color:black}QLabel:hover { color: #FF4400; }""")
        self.r_r_zfb.setStyleSheet("""QLabel{color:black}QLabel:hover { color: #FF4400; }""")
        self.right_bang_index-=1
    self.r_center_lb.setText(self.right_table[self.right_bang_index] + self.right_table_t[self.r_table_ph.currentIndex()])
def change_r_r_bang(self,event):
    if self.right_bang_index ==2:
        self.r_r_zfb.setStyleSheet("color:#CDCDCD")
        self.right_bang_index += 1
    elif 0 < self.right_bang_index < 3:
        self.r_l_zfb.setStyleSheet("""QLabel{color:black}QLabel:hover { color: #FF4400; }""")
        self.r_r_zfb.setStyleSheet("""QLabel{color:black}QLabel:hover { color: #FF4400; }""")
        self.right_bang_index += 1
    self.r_center_lb.setText(self.right_table[self.right_bang_index] + self.right_table_t[self.r_table_ph.currentIndex()])

def r_table_index(self,event):
    self.r_center_lb.setText(self.right_table[self.right_bang_index] + self.right_table_t[self.r_table_ph.currentIndex()])
