import datetime
import os
import sys
import time

import requests
from PyQt6.QtCore import QThread, pyqtSignal
from PyQt6.QtWidgets import QMessageBox


class get_exchange_html(QThread):
    finished = pyqtSignal(str,float)
    def __init__(self,source:str,target:str,length,unit):
        super().__init__()
        self.source=source
        self.target=target
        self.length=length
        self.unit=unit
        self.last_value=0
        self.html_path = r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\api\widget\calculator\template.html"
        self.header={
            "User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0",
            "Cookie":"https://wise.com/zh-cn/currency-converter/gbp-to-etb-rate?amount=1"
        }

        # self.check_html_path(self.html_path)

    def check_html_path(self,path):
        if not os.path.exists(self.html_path):
            return False
        return True

    # def reasonable_value(self,sub_v,min_v,max_v):
    #     stepSize=0
    #     max1=0
    #     min1=0
    #     if 0 < sub_v <= 0.5:
    #         stepSize = round((max_v - min_v) / 8, 2)
    #         max1 = round(max_v, 1)
    #         min1 = round(min_v, 1)
    #     elif 0.5 < sub_v <= 2:
    #         stepSize = round((max_v - min_v) / 4, 1)
    #         max1 = round(max_v, 1)
    #         min1 = round(min_v, 1)
    #     elif 2 < sub_v < 10000:
    #         stepSize = int((max_v - min_v) / 4)
    #         max1 = int(max_v)
    #         min1 = int(min_v)
    #     return stepSize,min1-stepSize/2,max1+stepSize/2

    def reasonable_value(self, sub_v, min_v, max_v):
        stepSize = 0
        max1 = 0
        min1 = 0

        if sub_v <= 0.0001:
            # 极差极小（如0.0001），固定步长为0.0001，避免刻度重叠
            stepSize = 0.0001
            max1 = max_v + stepSize * 2
            min1 = min_v - stepSize * 2

        elif 0.0001 < sub_v <= 0.5:
            # 小范围数据（如0.2~0.4），步长保留4位小数
            stepSize = round(sub_v / 5, 4)
            max1 = round(max_v + stepSize, 4)
            min1 = round(min_v - stepSize, 4)

        elif 0.5 < sub_v <= 2:
            # 中等范围（如1.0~2.0），步长保留2位小数
            stepSize = round(sub_v / 4, 2)
            max1 = round(max_v + stepSize, 2)
            min1 = round(min_v - stepSize, 2)

        elif 2 < sub_v <= 10:
            # 较大范围（如3~8），步长为整数或0.5
            stepSize = round(sub_v / 4, 1)
            max1 = round(max_v + stepSize, 1)
            min1 = round(min_v - stepSize, 1)

        elif 10 < sub_v <= 500:
            # 大范围（如100~400），步长为整数
            stepSize = int(sub_v / 5)
            max1 = int(max_v + stepSize)
            min1 = int(min_v - stepSize)

        else:
            # 极差超过500，强制步长为100
            stepSize = 100
            max1 = int(max_v + stepSize)
            min1 = int(min_v - stepSize)

        return stepSize, min1, max1

    def resolution_(self,length,unit):
        if length in [2,7,30] and unit=="day":
            return "hourly"
        else:
            return "daily"



    def get_source_target_history_data(self,source:str,target:str,length,unit):
        resolution=self.resolution_(length,unit)
        self.base_url = f"https://wise.com/rates/history+live?source={source}&target={target}&length={length}&resolution={resolution}&unit={unit}"
        response=requests.get(self.base_url,headers=self.header,verify=False)
        response_data=response.text
        data_list=eval(response_data)
        x_data=[]
        y_data=[]

        if resolution=="daily":
            for i in data_list:
                x_data.append(i["value"])
                y_data.append(datetime.datetime.fromtimestamp(i["time"] / 1000).strftime("%Y-%m-%d"))
        else:
            for i in data_list:
                x_data.append(i["value"])
                y_data.append(datetime.datetime.fromtimestamp(i["time"] / 1000).strftime("%Y-%m-%d %H:%M:%S"))
        self.last_value =x_data[-1]
        print(x_data)
        print(self.last_value)
        min_value=min(x_data)
        max_value=max(x_data)
        print(min_value,max_value)
        sub_value=max_value-min_value
        stepSize, min_, max_=self.reasonable_value(sub_value,min_value,max_value)
        label_=f"{source.upper()}/{target.upper()}"
        print(label_, stepSize, min_, max_)
        return self.insert_html(y_data,x_data,label_, stepSize, min_, max_)


    def insert_html(self,x_labels,y_values,label_,stepSize, min_, max_):
        try:
            with open(self.html_path, 'r') as file:
                html_content = file.read()
            js_code = f"renderChart({x_labels}, {y_values},{stepSize},{min_},{max_});"
            html_content = html_content.replace("</body>", f"<script>{js_code}</script></body>").replace("time_start",x_labels[0]).replace("time_end",x_labels[-1]).replace("kv",label_)
            # print(html_content)
            return html_content
        except:
            print("模板文件插入异常")



    def run(self):
        res=self.get_source_target_history_data(self.source,self.target,self.length,self.unit)
        self.finished.emit(res,self.last_value)


# s_time=time.time()
# da=get_exchange_html()
# da.get_source_target_history_data("gbp","etb",1,"month")
# e_time=time.time()
# print(e_time-s_time)




