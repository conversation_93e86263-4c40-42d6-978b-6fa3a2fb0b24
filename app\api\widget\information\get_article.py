import re

import requests


class article_data:
    def __init__(self):
        self.article_url=""
        self.header = {
            "Referer": "https://so.eastmoney.com/",
            "Cookie": "qgqp_b_id=50d74afee65419b05e9120f0df53c69f; st_si=63307939966789; fund_registerAd_1=1; fullscreengg=1; fullscreengg2=1; emsiderzk=close; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=37; st_psi=20250323193223400-118000300903-9169591590; st_asi=delete",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 Edg/134.0.0.0"
        }
        self.html_start = """
                        <html>
                            <body>"""
        self.html_end = """
                        </body>
                        </html>
                        """

    def return_article_data(self,url):
        pattern = r'<!--文章主体-->(.*?)<!-- 文尾部其它信息 -->'
        data=requests.get(url,headers=self.header,verify=False).text
        match = re.search(pattern, data, re.DOTALL)
        # if match:
        article_ = match.group(1).strip()  # 获取匹配的内容并去掉首尾空白
        article_ = self.html_start + article_ + self.html_end
        # print(article_)
        return article_

article_data=article_data
# print(article_data.return_article_data(None,url="https://finance.eastmoney.com/a/202508043475116314.html"))

