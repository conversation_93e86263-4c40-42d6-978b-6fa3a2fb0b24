import json

import requests


class get_jj_company:
    def __init__(self):
        self.header={
            "User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0",
            "Referer":"https://fund.eastmoney.com/data/hbxfundranking.html",
            "Cookie":"qgqp_b_id=50d74afee65419b05e9120f0df53c69f; emshistory=%5B%22%E5%8D%8A%E5%AF%BC%E4%BD%93%22%2C%22%E9%BB%84%E9%B8%A1%22%2C%22%E4%BA%BA%E6%80%A7%E6%9C%BA%E5%99%A8%E4%BA%BA%22%2C%22%E4%BA%BA%E5%BD%A2%E6%9C%BA%E5%99%A8%E4%BA%BA%22%2C%22%E9%B8%A1%E8%82%89%22%2C%22%E9%BB%84%E9%87%91%22%2C%22CPO%22%2C%22%E6%AD%8C%E5%B0%94%E8%82%A1%E4%BB%BD%22%2C%22%E5%B1%B1%E4%B8%9C%E9%BB%84%E9%87%91%22%2C%22%E7%89%9B%E5%A5%B6%22%5D; mtp=1; ct=WbAM5gZmXDQrlDkq16RoK_RznGcQmui6r7McHKezekoOVfWS4eRm23tSyLCfgpJ91gTgs9aWy3mf5cp8N0AWgTPUAcJbIH5Zz50CRCPnxxkxSfppe40XfGBeWv4aT91DvrWhzpDfcgctRSDiRgMR31yGZCKtxRpjbqpj91xZv64; ut=FobyicMgeV60R-wNFHdtrEkjyi1hTBkF_XKkzaxQ0FFYPohwhuROtceUOY7pLm7jbXajA9ZTTsuKd58BgJJ6cCwAtPrirJ8aEW60Gq79Fo5ldEgytoqlN0PHsXmjNx5rk4qcImtt25inSZOftasUXjZk0yFEvD8jB2N24NIcLDRwv86c9ba6_kM4_Iiwq36fEfYO0oCrQdghgUyNYiDuMNLgCZxWS-8z_nqmXnWUIg6S60j-df8KOaUcsMPjxcU07sJHIjGeESQ; pi=1541316586261916%3Bo1541316586261916%3B%E5%9F%BA%E6%B0%91223A8K7027%3BNsdCm%2BJdoCn8FEqpD2aXGcTSevswUHi20bQV5DwOvgt%2F84sWHGGiHlb3c9G3nMGFCPicGe6e%2B6jwbdTjvlDHY1Kth9WCVJ3%2F4MMM5%2Bbebtlwev0tVal9gXBFEMA1nSkGQ5vjdPVGnQM3XR5sE%2BBRUJbIM09%2FjQQTDX3VVQWj6EoHPXgn2JtcNizDPolZw7wqRb2Hn55G%3BuZgq8J7XOPD0tBYPwhepl1QISKJegWSKEP3UPB4BXR1MX3WY7e3roK6kACNr5LkxC9APFTtcHa0NbcOItnQDU5q1XtlerSzHnicHr7jhIrAU%2BwIoysgG1JTm0lxUqr7fwuKboBRZI6gk6BZhYM18kI2hdwmiJg%3D%3D; uidal=1541316586261916%e5%9f%ba%e6%b0%91223A8K7027; sid=*********; vtpst=|; st_si=08666054905039; st_asi=delete; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=2; st_psi=20250418184925740-112200312936-1503605589; ASP.NET_SessionId=kapdn4aaz4xgss1qb4z1c402"
        }
        self.code_list = []
        self.company_list = []
        self.code_company_list=[]
        # self.get_jj_company_data()


    def get_jj_company_data(self,):
        self.url="https://fund.eastmoney.com/js/jjjz_gs.js?v=0.2779043792547018"
        response = requests.get(self.url, headers=self.header, verify=False, )
        
        # 移除BOM字符和JavaScript包装
        response_text = response.text.strip()
        if response_text.startswith('\ufeff'):  # 移除BOM字符
            response_text = response_text[1:]
        
        # 提取数组部分
        response_text = response_text.strip("var gs={op:").rstrip("}")
        
        try:
            response_data = eval(response_text)
            for i in response_data:
                self.company_list.append(i[1])
                self.code_list.append(i[0])
                self.code_company_list.append(i[1]+" ["+i[0]+"]")
        except Exception as e:
            print(f"解析数据失败: {e}")


    def check_query(self,query):
        l = []
        try:
            for i in range(len(self.code_list)):
                if str(int(query)) in self.code_list[i]:
                    l.append(self.code_company_list[i])
            return l
        except:
            for i in range(len(self.company_list)):
                if query in self.company_list[i]:
                    l.append(self.code_company_list[i])
            return l

get_jj_company=get_jj_company()
# print(get_jj_company.check_query(""))




