"""
指数选择功能使用示例
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication, QMainWindow
from main_w import Ui_MainWindow
from app.modules.FundHome2.FundHome2 import FundHome2

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    ui = Ui_MainWindow()
    ui.setupUi(main_window)
    
    # 创建 FundHome2 实例，这会自动初始化指数选择功能
    fund_home2 = FundHome2(ui)
    
    # 显示主窗口
    main_window.show()
    
    print("指数选择功能已初始化！")
    print("功能特点：")
    print("- 8个指数标签：hs_1 到 hs_8")
    print("- 默认选中第一个指数（上证指数）")
    print("- 点击任意标签切换选中状态")
    print("- 选中标签使用橙色边框样式")
    print("- 未选中标签使用灰色边框样式")
    print("- 点击时输出对应的指数代码")
    print("- 数据处理通过DataHandle.handle_index_selection()方法")
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
