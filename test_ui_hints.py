"""
测试 UI 代码提示问题
"""

from PyQt6.QtWidgets import QApplication, QMainWindow
from main_w import Ui_MainWindow
import sys

def test_ui_attributes():
    """测试 UI 属性"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    
    # 创建 UI 实例
    ui = Ui_MainWindow()
    
    print("=== setupUi 调用前的属性 ===")
    ui_attrs_before = [attr for attr in dir(ui) if not attr.startswith('_')]
    print(f"属性数量: {len(ui_attrs_before)}")
    print(f"属性列表: {ui_attrs_before}")
    
    # 调用 setupUi
    ui.setupUi(main_window)
    
    print("\n=== setupUi 调用后的属性 ===")
    ui_attrs_after = [attr for attr in dir(ui) if not attr.startswith('_')]
    print(f"属性数量: {len(ui_attrs_after)}")
    print(f"前20个属性: {ui_attrs_after[:20]}")
    
    # 查找组件属性
    component_attrs = [attr for attr in ui_attrs_after if not attr.startswith('retranslate') and not attr.startswith('setup')]
    print(f"\n=== 组件属性 (前30个) ===")
    print(component_attrs[:30])
    
    app.quit()

if __name__ == "__main__":
    test_ui_attributes()
