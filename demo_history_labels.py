"""
演示动态历史记录标签功能
"""

import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QWidget, QLabel
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

class HistoryDemo(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("基金历史记录演示")
        self.setGeometry(100, 100, 1200, 200)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 模拟历史记录数据
        self.history_data = [
            "易方达蓝筹精选混合",
            "华夏成长混合",
            "嘉实新兴产业股票",
            "广发稳健增长混合",
            "南方成份精选混合A",
            "博时主题行业混合",
            "富国天惠精选成长混合A",
            "汇添富价值精选混合A"
        ]
        
        self.history_labels = []
        self.create_history_labels()
    
    def create_history_labels(self):
        """创建历史记录标签"""
        # 清除之前的标签
        for label in self.history_labels:
            label.deleteLater()
        self.history_labels.clear()
        
        # 限制最多8个记录
        history_list = self.history_data[:8]
        
        if not history_list:
            return
        
        # 计算布局参数
        start_x = 50
        start_y = 50
        label_height = 40
        available_width = 1100
        spacing = 10
        label_width = (available_width - (len(history_list) - 1) * spacing) // len(history_list)
        
        for i, fund_name in enumerate(history_list):
            # 创建标签
            history_label = QLabel(parent=self.centralWidget())
            
            # 设置位置和尺寸
            x_pos = start_x + i * (label_width + spacing)
            history_label.setGeometry(x_pos, start_y, label_width, label_height)
            
            # 处理长文本
            display_text = fund_name
            if len(fund_name) > 10:
                display_text = fund_name[:10] + "..."
            
            history_label.setText(display_text)
            history_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            
            # 设置字体
            font = QFont()
            font.setPointSize(11)
            font.setBold(True)
            history_label.setFont(font)
            
            # 设置样式表
            history_label.setStyleSheet("""
                QLabel {
                    font-size: 11px;
                    font-weight: bold;
                    color: #333333;
                    background-color: #f5f5f5;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    padding: 5px;
                }
                QLabel:hover {
                    color: #FF4400;
                    background-color: #fff;
                    border-color: #FF4400;
                }
            """)
            
            # 绑定点击事件
            history_label.mousePressEvent = lambda event, fund=fund_name, idx=i: self.on_label_clicked(fund, idx, event)
            
            # 设置工具提示显示完整名称
            history_label.setToolTip(fund_name)
            
            # 显示标签
            history_label.show()
            
            # 添加到管理列表
            self.history_labels.append(history_label)
        
        # 添加说明标签
        info_label = QLabel("点击任意基金名称查看详情，鼠标悬停查看完整名称", parent=self.centralWidget())
        info_label.setGeometry(50, 100, 800, 30)
        info_label.setStyleSheet("color: #666; font-size: 12px;")
        info_label.show()
    
    def on_label_clicked(self, fund_name, index, event):
        """标签点击事件处理"""
        print(f"点击了基金: {fund_name} (索引: {index})")
        
        # 模拟更新浏览记录：将点击的基金移到最前面
        if fund_name in self.history_data:
            self.history_data.remove(fund_name)
        self.history_data.insert(0, fund_name)
        
        # 重新创建标签以反映新的顺序
        self.create_history_labels()
        
        print(f"更新后的历史记录顺序: {self.history_data[:8]}")

def main():
    app = QApplication(sys.argv)
    
    demo = HistoryDemo()
    demo.show()
    
    print("基金历史记录演示:")
    print("1. 显示8个基金历史记录")
    print("2. 鼠标悬停时标签变为橙色 (#FF4400)")
    print("3. 点击标签会将该基金移到最前面")
    print("4. 长名称会被截断并显示省略号")
    print("5. 鼠标悬停可查看完整名称")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
