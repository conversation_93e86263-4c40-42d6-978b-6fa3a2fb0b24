from main_w import Ui_MainWindow
from PyQt6.QtCore import Qt, QStringListModel, QTimer, QUrl
import random
from PyQt6.QtGui import QColor, QPixmap, QImage
from PyQt6.QtWidgets import QHeaderView, QTableWidgetItem, QWidget, QHBoxLayout, QPushButton, QLineEdit, QMessageBox, \
    QTextEdit, QLabel
from PyQt6.QtNetwork import QNetworkAccessManager, QNetworkRequest, QNetworkReply
from app.common.api.code_name import FundCodeName
from .api.get_hot_search import get_hot_search
from .api.get_lz_concept import get_lz_concept
from .api.get_news_data import get_news_data
from .api.get_zs_data import get_zs_data
from .api.get_zs_nums import get_zs_nums
from .src import style
from ..FundHome1.DataHandle import DataHandle
from app.modules.FundHome1.api.get_subject_limit import get_subject_limit
from app.common.api.network_to_img import NetworkToImg

class FundHome1:
    def __init__(self, ui: Ui_MainWindow):
        self.ui = ui
        self.init_setup()


    def init_setup(self):
        self.concept_code =[]
        self.img_path = r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\img"
        self.history_labels = []  # 存储动态创建的历史记录标签
        self.get_recent_fund_history()
        self.ui.search_widget.hide()
        self.ui.fund_detail_widget.hide()
        self.update_gl_status=False

        # 搜索框输入检测
        self.ui.search_fund_textEdit.textChanged.connect(self.quick_s)
        self.ui.search_fund_textEdit.mousePressEvent = self.custom_mouse_press_event
        self.ui.close_search_lb.setPixmap(QPixmap(self.img_path + r'\close.png').scaled(32, 32))
        self.ui.close_search_lb.mousePressEvent = self.close_search_widget
        self.ui.search_ico_lb.setPixmap(QPixmap(self.img_path + r'\search_ico.png').scaled(29, 29))
        self.ui.update_gl_ico.setPixmap(QPixmap(self.img_path + r'\update_gl_ico.png').scaled(29, 29))
        self.ui.clear_his_ico.setPixmap(QPixmap(self.img_path + r'\clear_history_ico.png').scaled(29, 29))
        self.ui.progressBar.setVisible(False)

        # 绑定更新按钮点击事件
        self.ui.update_gl.mousePressEvent = self.start_refresh_progress

        # 绑定清空历史记录按钮点击事件
        self.ui.clear_history_lb.mousePressEvent = self.clear_fund_history

        #获取最新消息
        # self.load_news()
        #获取指数数据
        # self.load_zs_data()
        #获取上涨家数
        # self.load_zs_nums_data()
        #获取主题基金数据、
        # self.load_subject_limit()
        #领涨+今日热搜
        # self.load_lz_concept()

    def start_refresh_progress(self, event):
        """开始刷新进度"""
        # 显示进度条并重置
        self.ui.progressBar.setVisible(True)
        self.ui.progressBar.setValue(0)
        self.ui.update_gl.hide()
        self.ui.update_gl_ico.hide()
        self.ui.update_gl_2.show()
        # self.ui.progressBar.setFormat("刷新中")
        
        # 创建定时器模拟进度
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.update_progress)
        self.refresh_timer.start(25)  # 每25ms更新一次，2.5秒完成
        
        self.progress_value = 0

    def update_progress(self):
        """更新进度条"""
        self.progress_value += 1
        self.ui.progressBar.setValue(self.progress_value)
        
        if self.progress_value >= 100:
            # 进度完成
            self.refresh_timer.stop()
            self.ui.update_gl.show()
            self.ui.update_gl_2.hide()
            self.ui.update_gl_ico.show()
            # self.ui.progressBar.setFormat("刷新完成")
            
            # 1秒后隐藏进度条
            QTimer.singleShot(500, lambda: self.ui.progressBar.setVisible(False))


    def close_search_widget(self,event):
        self.ui.search_widget.hide()

    def load_hot_search_data(self,):
        self.worker_thread = get_hot_search()
        self.worker_thread.finished.connect(self.task_finished_hot_search)
        self.worker_thread.start()

    def task_finished_hot_search(self, data):
        self.ui.hot_search_listWidget.clear()
        for i in range (1,len(data)+1):
            self.ui.hot_search_listWidget.addItem(f"{data[i-1]}")
        self.ui.hot_search_listWidget.itemClicked.connect(self.goto_detail)

    def get_recent_fund_history(self):
        """获取最近的基金浏览历史并动态创建QLabel"""
        try:
            # 清除之前的历史记录标签
            self.clear_history_labels()

            # 获取完整的历史记录（用于事件绑定）
            full_history = DataHandle.get_fund_history()[:8]
            print(f"完整历史记录: {full_history}")

            # 隐藏原来的静态标签
            self.ui.viewed_historys_lb.setText("")

            # 动态创建历史记录标签
            self.create_history_labels(full_history)

            return full_history
        except Exception as e:
            print(f"获取基金历史记录失败: {e}")
            return []

    def clear_fund_history(self, event):
        """清空基金历史记录"""
        try:
            # 调用 DataHandle 的清空方法
            success = DataHandle.clear_fund_history()

            if success:
                # 清空成功后，清除界面上的历史记录标签
                self.clear_history_labels()
                self.ui.viewed_historys_lb.setText("")

        except Exception as e:
            print(f"清空基金历史记录异常: {e}")

    def clear_history_labels(self):
        """清除之前创建的历史记录标签"""
        for label in self.history_labels:
            label.deleteLater()
        self.history_labels.clear()

    def create_history_labels(self, full_history_list):
        """动态创建历史记录标签"""
        if not full_history_list:
            return

        # 获取原始标签的位置和尺寸作为参考
        original_rect = self.ui.viewed_historys_lb.geometry()
        start_x = original_rect.x()
        start_y = original_rect.y()+3
        label_height = original_rect.height()-6

        # 固定8等分宽度，不管实际有多少个历史记录
        available_width = original_rect.width()
        spacing = 10  # 标签之间的间距
        total_spacing = 7 * spacing  # 8个标签之间有7个间距
        label_width = (available_width - total_spacing) // 8  # 固定8等分

        for i, full_fund_name in enumerate(full_history_list):
            # 创建新的QLabel
            history_label = QLabel(parent=self.ui.index_top2_widget)

            # 设置位置和尺寸
            x_pos = start_x + i * (label_width + spacing)
            history_label.setGeometry(x_pos, start_y, label_width, label_height)

            # 生成显示用的截断名称
            display_name = full_fund_name
            if len(full_fund_name) > 10:
                display_name = full_fund_name[:10] + "..."

            # 设置显示文本
            history_label.setText(display_name)
            history_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

            # 设置工具提示显示完整名称
            history_label.setToolTip(full_fund_name)

            # 设置样式表，包含悬停效果
            history_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    border: 1px solid #ddd;
                    border-radius: 6px;
                    padding: 4px;
                    cursor: pointer;
                }
                QLabel:hover {
                    color: #FF4400;
                    background-color: #fff;
                    border-color: #FF4400;
                }
            """)

            # 绑定点击事件 - 使用完整的基金名称
            history_label.mousePressEvent = lambda event, fund=full_fund_name: self.goto_detail_2(fund, event)

            # 显示标签
            history_label.show()

            # 添加到列表中管理
            self.history_labels.append(history_label)

    """搜索框相关"""
    def custom_mouse_press_event(self, event):
        try:
            super(QLineEdit, self.ui.search_fund_textEdit).mousePressEvent(event)  # 调用父类方法，确保正常行为
            self.ui.search_widget.show()
            self.quick_s(event=None)
            # self.load_hot_search_data()
        except Exception as e:
            print(f"点击异常：{e}")

    def quick_s(self,event):
        try:
            for i in range(1,11):
                self.ui.search_code_listWidget.clear()
                self.ui.search_name_listWidget.clear()
                self.ui.search_jp_listWidget.clear()
                self.ui.search_type_listWidget.clear()

            keyword=self.ui.search_fund_textEdit.text()
            data_l,total_data=DataHandle.get_search_fund(keyword)
            self.ui.match_nums_lb.setText(f"匹配到{total_data}条数据")
            for i in range(1,len(data_l)+1):
                self.ui.search_code_listWidget.addItem(f"{data_l[i-1][0]}")
                self.ui.search_name_listWidget.addItem(f"{data_l[i-1][2]}")
                self.ui.search_jp_listWidget.addItem(f"{data_l[i-1][1]}")
                self.ui.search_type_listWidget.addItem(f"{data_l[i-1][3]}")

            self.ui.search_name_listWidget.itemClicked.connect(self.goto_detail)

        except Exception as e:
            print(f"搜索异常{e}")

    def goto_detail(self,item):
        try:
            name=item.text()
            self.ui.fund_detail_widget.show()
            self.ui.fund_detail_title.setText(name)
            DataHandle.add_fund_to_history(name)
            self.get_recent_fund_history()
            print(FundCodeName.return_fund_code(name=name))
        except Exception as e:
            print(f"goto_detail ERROR:{e}")

    def goto_detail_2(self, history_fund, event):
        """从基金历史记录进入详情页面"""
        try:
            # 显示基金详情页面
            self.ui.fund_detail_widget.show()
            self.ui.fund_detail_title.setText(history_fund)

            # 更新浏览记录（将当前基金移到最前面）
            DataHandle.add_fund_to_history(history_fund)

            # 刷新历史记录显示
            self.get_recent_fund_history()

            # 获取基金代码（如果需要的话）
            fund_code = FundCodeName.return_fund_code(name=history_fund)
            print(fund_code)

        except Exception as e:
            print(f"goto_detail_2 ERROR: {e}")

    #最新消息
    def load_news(self,):
        self.worker_thread = get_news_data()
        self.worker_thread.finished.connect(self.task_finished_new_data)
        self.worker_thread.start()

    def task_finished_new_data(self, data):
        self.news_data = data  # 保存所有新闻数据
        self.ui.news_label.setText(data[0])
        
        # 启动定时器，每10秒更新一次
        self.start_news_rotation()

    def start_news_rotation(self):
        """启动新闻轮播定时器"""
        if hasattr(self, 'news_timer'):
            self.news_timer.stop()
        self.news_timer = QTimer()
        self.news_timer.timeout.connect(self.update_random_news)
        self.news_timer.start(5000)  # 10秒 = 10000毫秒

    def update_random_news(self):
        """随机更新新闻内容"""
        if hasattr(self, 'news_data') and self.news_data:
            random_index = random.randint(0, len(self.news_data) - 1)
            self.ui.news_label.setText(self.news_data[random_index])

    #指数数据
    def load_zs_data(self):
        try:
            self.worker_thread_1 = get_zs_data()
            self.worker_thread_1.finished.connect(self.task_finished_zs_data)
            self.worker_thread_1.start()
        except Exception as e:
            print(f"load_zs_data ERROR:{e}")

    def task_finished_zs_data(self, zs_name,zs_value,zs_zf):
        try:
            #01367
            zs_value=DataHandle.zs_data_trans(zs_value)
            zs_zf=DataHandle.zs_data_trans(zs_zf)
            data_value=[]
            data_zf=[]
            for i in [0,1,3,6,7]:
                data_value.append(zs_value[i])
                data_zf.append(zs_zf[i])
            self.show_zs_data(data_value,data_zf)


        except Exception as e:
            print(f"task_finished_zs_data ERROR:{e}")

    def show_zs_data(self,value_list:list,zf_list:list):
        for i in range(1,len(value_list)+1):
            getattr(self.ui,f"zs_value_{i}").setText(value_list[i-1])
            getattr(self.ui,f"zs_zf_{i}").setText(f"{zf_list[i-1]}%")
        color_list=DataHandle.zs_data_color(zf_list)
        for i in range(1,len(color_list)+1):
            if color_list[i-1]=="green":
                getattr(self.ui, f"zs_value_{i}").setStyleSheet(style.QLabel_green)
                getattr(self.ui, f"zs_zf_{i}").setStyleSheet(style.QLabel_green)
                getattr(self.ui,f"zs_bg_{i}").setStyleSheet(style.QWidget_green_bg)
            elif color_list[i-i]=="red":
                getattr(self.ui, f"zs_value_{i}").setStyleSheet(style.QLabel_red)
                getattr(self.ui, f"zs_zf_{i}").setStyleSheet(style.QLabel_red)
                getattr(self.ui, f"zs_bg_{i}").setStyleSheet(style.QWidget_red_bg)
            else:
                getattr(self.ui, f"zs_value_{i}").setStyleSheet(style.QLabel_black)
                getattr(self.ui, f"zs_zf_{i}").setStyleSheet(style.QLabel_black)
                getattr(self.ui, f"zs_bg_{i}").setStyleSheet(style.QWidget_black_bg)

    #上涨家数和栏设计
    def load_zs_nums_data(self):
        try:
            self.worker_thread_2 = get_zs_nums()
            self.worker_thread_2.finished.connect(self.task_finished_zs_nums_data)
            self.worker_thread_2.start()
        except Exception as e:
            print(f"load_zs_nums_data ERROR:{e}")

    def task_finished_zs_nums_data(self,data):
        try:
            print(data)
            self.color_nums=[data[0],data[2],data[-1]]
            for i in range(1,5):
                getattr(self.ui,f"up_nums_{i}").setText(str(data[i-1]))
            self.show_bar()
        except Exception as e:
            print(f"task_finished_zs_nums_data ERROR:{e}")

    #栏设计
    def show_bar(self):
        """栏设计"""
        try:
            # 调用DataHandle的zs_color_width方法计算宽度
            width_list = DataHandle.zs_color_width(
                self.color_nums[0],  # up
                self.color_nums[1],  # down
                self.color_nums[2]   # middle
            )

            # 设置各个bar的宽度和位置
            self.ui.up_bar_lb.setFixedWidth(width_list[0])
            self.ui.up_bar_lb.move(10, self.ui.up_bar_lb.y())  # up从x=10开始
            
            self.ui.mid_bar_lb.setFixedWidth(width_list[2])
            self.ui.mid_bar_lb.move(10 + width_list[0], self.ui.mid_bar_lb.y())  # middle的x位置
            
            self.ui.down_bar_lb.setFixedWidth(width_list[1])
            self.ui.down_bar_lb.move(10 + width_list[0] + width_list[2], self.ui.down_bar_lb.y())  # down的x位置

            print(f"设置宽度: up={width_list[0]}, middle={width_list[2]}, down={width_list[1]}")

        except Exception as e:
            print(f"show_bar ERROR: {e}")

    #基金主题
    def load_subject_limit(self):
        try:

            self.worker_thread_3 = get_subject_limit()
            self.worker_thread_3.finished.connect(self.task_finished_load_subject_limit)
            self.worker_thread_3.start()
        except Exception as e:
            print(f"load_subject_limit ERROR:{e}")

    def task_finished_load_subject_limit(self,name:list,value:list):
        for i in range(1,9):
            getattr(self.ui,f"concept_name_{i}").setText(name[i-1])
            getattr(self.ui,f"concept_value_{i}").setText(value[i-1])

    #领涨概念
    def load_lz_concept(self):
        try:
            self.worker_thread_4 = get_lz_concept()
            self.worker_thread_4.finished.connect(self.task_finished_load_lz_concept)
            self.worker_thread_4.start()
        except Exception as e:
            print(f"load_lz_concept ERROR:{e}")

    def task_finished_load_lz_concept(self,concept_code, concept_name, concept_value, concept_stock):

        self.concept_code=concept_code
        for i in range(1,11):
            getattr(self.ui,f"lz_name_{i}").setText(concept_name[i-1])
            getattr(self.ui,f"lz_value_{i}").setText(concept_value[i-1])
            if "-"  not in concept_value[i-1]:
                getattr(self.ui,f"lz_value_{i}").setStyleSheet(style.QLabel_red)
            else:
                getattr(self.ui, f"lz_value_{i}").setStyleSheet(style.QLabel_green)
            getattr(self.ui,f"lz_stock_{i}").setText(concept_stock[i-1])
        self.show_lzbk_img()

    def show_lzbk_img(self):
        try:
            # 初始化网络管理器和图片加载器
            self.network_manager = QNetworkAccessManager()
            self.network_img_loader = NetworkToImg(self.network_manager, self.ui)

            # 使用封装的类加载图片
            self.network_img_loader.load_image(
                f"https://webquotepic.eastmoney.com/GetPic.aspx?nid=90.{self.concept_code[0]}&imageType=rs&0.25487243381954694",
                "lzbk_img_lb"
            )
            self.network_img_loader.load_image(
                f"https://webquotepic.eastmoney.com/GetPic.aspx?id=899001_TB&imageType=FFRS&token=44c9d251add88e27b65ed86506f6e5da&type=FFR",
                "zjlrlc_img_lb"
            )
            """设置图片不模糊，无锯齿"""
            # 宽高与图片的匹配
            #scaledContents：True


        except Exception as e:
            print(f"img error:{e}")









